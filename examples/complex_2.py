
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.data = {}
            self.initialized = True
    
    def get_data(self, key):
        return self.data.get(key)
    
    def set_data(self, key, value):
        self.data[key] = value
