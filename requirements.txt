# NEUROGLYPH v2.0 - Dipendenze Core
# Aggiornato per approccio modulare e incrementale

# Core ML/AI
torch>=2.0.0
transformers>=4.35.0
tokenizers>=0.15.0
datasets>=2.14.0
accelerate>=0.24.0

# Training e Fine-tuning
unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git
peft>=0.6.0
trl>=0.7.0
bitsandbytes>=0.41.0

# Symbolic Processing
numpy>=1.24.0
scipy>=1.11.0
sympy>=1.12.0
networkx>=3.1.0

# Memory e Storage
faiss-cpu>=1.7.4
lmdb>=1.4.1
sqlite3  # Built-in Python
redis>=5.0.0

# Data Processing
pandas>=2.0.0
jsonlines>=4.0.0
pydantic>=2.4.0
typing-extensions>=4.8.0

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
hypothesis>=6.88.0

# Development
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0

# Utilities
tqdm>=4.66.0
rich>=13.6.0
click>=8.1.0
python-dotenv>=1.0.0

# Optional: GPU Support (uncomment if needed)
# torch-audio>=2.0.0
# torch-vision>=0.15.0

# Optional: Advanced Features
# ray[tune]>=2.7.0  # For hyperparameter tuning
# wandb>=0.15.0     # For experiment tracking
