#!/usr/bin/env python3
"""
Test rapido per verificare progressi Step S-1
"""

import sys
sys.path.append('.')

from neuroglyph.core.encoder.encoder import NGEncoder
import ast

def test_simple_round_trip():
    """Test round-trip semplice"""
    print("=== TEST ROUND-TRIP SEMPLICE ===")
    
    encoder = NGEncoder()
    
    # Codice semplice
    code = """def hello():
    return "world" """
    
    print(f"Codice originale:\n{code}")
    
    # Encoding
    result = encoder.encode(code, encoding_level=2)
    print(f"Simboli: {result.compressed_symbols}")
    
    # Decoding
    decoded = encoder.decode(result.compressed_symbols, result.metadata)
    print(f"Codice ricostruito:\n{decoded.reconstructed_code}")
    
    # Verifica sintassi
    try:
        ast.parse(decoded.reconstructed_code)
        print("✅ Sintassi valida")
    except SyntaxError as e:
        print(f"❌ Sintassi invalida: {e}")
    
    # Verifica round-trip
    if decoded.round_trip_successful:
        print("✅ Round-trip riuscito")
    else:
        print("❌ Round-trip fallito")
    
    print(f"Fidelity: {decoded.fidelity_score:.2f}")
    print()

def test_complex_round_trip():
    """Test round-trip complesso"""
    print("=== TEST ROUND-TRIP COMPLESSO ===")
    
    encoder = NGEncoder()
    
    # Codice complesso
    code = """def test_function():
    for i in range(10):
        result = i * 2
        if result > 5:
            return result
    return None"""
    
    print(f"Codice originale:\n{code}")
    
    # Encoding
    result = encoder.encode(code, encoding_level=2)
    print(f"Simboli: {result.compressed_symbols}")
    
    # Decoding
    decoded = encoder.decode(result.compressed_symbols, result.metadata)
    print(f"Codice ricostruito:\n{decoded.reconstructed_code}")
    
    # Verifica sintassi
    try:
        ast.parse(decoded.reconstructed_code)
        print("✅ Sintassi valida")
    except SyntaxError as e:
        print(f"❌ Sintassi invalida: {e}")
    
    # Verifica round-trip
    if decoded.round_trip_successful:
        print("✅ Round-trip riuscito")
    else:
        print("❌ Round-trip fallito")
    
    print(f"Fidelity: {decoded.fidelity_score:.2f}")
    print()

def test_list_comprehension():
    """Test list comprehension"""
    print("=== TEST LIST COMPREHENSION ===")
    
    encoder = NGEncoder()
    
    # Codice con list comprehension
    code = """result = [x for x in range(10) if x > 5]"""
    
    print(f"Codice originale:\n{code}")
    
    # Encoding
    result = encoder.encode(code, encoding_level=2)
    print(f"Simboli: {result.compressed_symbols}")
    
    # Decoding
    decoded = encoder.decode(result.compressed_symbols, result.metadata)
    print(f"Codice ricostruito:\n{decoded.reconstructed_code}")
    
    # Verifica sintassi
    try:
        ast.parse(decoded.reconstructed_code)
        print("✅ Sintassi valida")
    except SyntaxError as e:
        print(f"❌ Sintassi invalida: {e}")
    
    print(f"Fidelity: {decoded.fidelity_score:.2f}")
    print()

if __name__ == "__main__":
    test_simple_round_trip()
    test_complex_round_trip()
    test_list_comprehension()
