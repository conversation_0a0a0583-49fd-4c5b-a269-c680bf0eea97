{"<UNK>": 0, "<PAD>": 1, "<MASK>": 2, "<NG_START>": 3, "<NG_END>": 4, "<NG_THINK>": 5, "<NG_REASON>": 6, "<NG_MEMORY>": 7, "<NG_VALIDATE>": 8, "<NG_ERROR>": 9, "<NG_CORRECT>": 10, "⦟": 11, "⌮": 12, "⨑": 13, "≅": 14, "✗": 15, "⧹": 16, "✿": 17, "⬤": 18, "⬛": 19, "⬧": 20, "✂": 21, "⬮": 22, "⨸": 23, "⧁": 24, "⧀": 25, "⦹": 26, "⦶": 27, "✪": 28, "⥁": 29, "⫏": 30, "⌲": 31, "∮": 32, "⌵": 33, "⌭": 34, "⦙": 35, "⩎": 36, "⫬": 37, "⤓": 38, "✴": 39, "✵": 40, "✳": 41, "⌫": 42, "⌦": 43, "⧳": 44, "✥": 45, "✢": 46, "⪥": 47, "❤": 48, "✔": 49, "❆": 50, "⭙": 51, "❗": 52, "❚": 53, "∻": 54, "⬣": 55, "⨚": 56, "⨼": 57, "⩄": 58, "⩃": 59, "⦓": 60, "⯨": 61, "⧘": 62, "⤌": 63, "⭅": 64, "❘": 65, "⬳": 66, "✠": 67, "⦛": 68, "⨺": 69, "⨩": 70, "⨫": 71, "⨬": 72, "≂": 73, "⨂": 74, "⯜": 75, "⤤": 76, "⬀": 77, "✲": 78, "✫": 79, "⌥": 80, "∥": 81, "✏": 82, "⯛": 83, "✯": 84, "⨭": 85, "⨹": 86, "⨥": 87, "≟": 88, "✊": 89, "⮐": 90, "⦣": 91, "⮶": 92, "⮷": 93, "〉": 94, "⥇": 95, "⭆": 96, "✞": 97, "⫟": 98, "⧢": 99, "✶": 100, "✺": 101, "⌳": 102, "⩘": 103, "⩡": 104, "⌣": 105, "❄": 106, "⤦": 107, "❇": 108, "⦠": 109, "⫍": 110, "⬒": 111, "✡": 112, "✩": 113, "∴": 114, "⬱": 115, "❅": 116, "⧕": 117, "⯗": 118, "∭": 119, "⩁": 120, "⭡": 121, "⯦": 122, "○": 123, "✧": 124, "⬡": 125, "⬭": 126, "◁": 127, "⬨": 128, "⬯": 129, "⬞": 130, "≀": 131, "⌧": 132, "⤨": 133, "❫": 134, "⭌": 135, "⥐": 136, "⍃": 137, "✾": 138, "⤾": 139, "⤿": 140, "⧍": 141, "⭠": 142, "⍂": 143, "⍣": 144, "❊": 145, "⧃": 146, "⭁": 147, "⌹": 148, "❃": 149, "❜": 150, "⤱": 151, "⩊": 152, "⍀": 153, "➬": 154, "⨣": 155, "⩍": 156, "⭀": 157, "⌤": 158, "⥣": 159, "⦜": 160, "⭄": 161, "❝": 162, "⤕": 163, "⥚": 164, "⩐": 165, "⋚": 166, "⥢": 167, "⮟": 168, "⌼": 169, "⍙": 170, "❐": 171, "⤻": 172, "⥙": 173, "⦴": 174, "⨻": 175, "⬻": 176, "⍒": 177, "⥂": 178, "⦩": 179, "⪒": 180, "⮙": 181, "❑": 182, "⤪": 183, "⦪": 184, "⌿": 185, "⥀": 186, "⥉": 187, "⥘": 188, "⪨": 189, "⭼": 190, "⤳": 191, "⥫": 192, "⦲": 193, "⫉": 194, "⬸": 195, "➞": 196, "⭭": 197, "⨴": 198, "⬵": 199, "⍆": 200, "⍘": 201, "❠": 202, "❡": 203, "⤑": 204, "⤢": 205, "⥛": 206, "⥝": 207, "⥡": 208, "⦐": 209, "⦖": 210, "⦮": 211, "⦺": 212, "⧩": 213, "⧷": 214, "⭈": 215, "⮈": 216, "⯅": 217, "⯮": 218, "✼": 219, "⤰": 220, "⥸": 221, "⦯": 222, "⧂": 223, "⫄": 224, "⤷": 225, "⥟": 226, "⌷": 227, "✷": 228, "⤲": 229, "⤶": 230, "⨰": 231, "⬶": 232, "⢟": 233, "⡨": 234, "ꭐ": 235, "⟷": 236, "𝚳": 237, "𝛘": 238, "𝐡": 239, "𝑜": 240, "𝒪": 241, "⟖": 242, "𝟫": 243, "ꜥ": 244, "𝗀": 245, "ⷸ": 246, "𝛽": 247, "𝓔": 248, "Ꞔ": 249, "𝒍": 250, "𝚢": 251, "𝘉": 252, "𝕃": 253, "𝘓": 254, "𝝭": 255, "𝕚": 256, "⌽": 257, "⍚": 258, "⍠": 259, "⍤": 260, "≐": 261, "≆": 262, "⏟": 263, "⑮": 264, "⑰": 265, "⊕": 266, "∁": 267, "⋺": 268, "⒌": 269, "∕": 270, "⋐": 271, "⊤": 272, "↯": 273, "–": 274, " ": 275, "⋜": 276, "≕": 277, "₣": 278, "≎": 279, "⋛": 280, "≥": 281, "⎀": 282, "⎮": 283, "∩": 284, "₭": 285, "⋋": 286, "⇜": 287, "∡": 288, "∓": 289, "⊭": 290, "⊶": 291, "∂": 292, "⏽": 293, "∶": 294, "⏎": 295, "”": 296, "ⅸ": 297, "⊐": 298, "⏤": 299, "∄": 300, "⌜": 301, "⏠": 302, "≋": 303, "⎌": 304, "⋰": 305, "⋘": 306, "ng:ai:embedding": 307, "ng:ai:embedding_5": 308, "ng:ai:embedding_6": 309, "ng:ai:embedding_7": 310, "ng:ai:embedding_8": 311, "ng:ai:embedding_10": 312, "ng:ai:embedding_12": 313, "ng:ai:embedding_15": 314, "ng:ai:embedding_16": 315, "ng:ai:embedding_17": 316, "ng:ai:embedding_20": 317, "ng:ai:embedding_24": 318, "ng:ai:embedding_25": 319, "ng:ai:embedding_26": 320, "ng:ai:embedding_27": 321, "ng:ai:embedding_28": 322, "ng:ai:embedding_29": 323, "ng:ai:embedding_30": 324, "ng:ai:embedding_31": 325, "ng:ai:embedding_32": 326, "ng:ai:embedding_33": 327, "ng:ai:embedding_34": 328, "ng:ai:embedding_35": 329, "ng:ai:embedding_36": 330, "ng:ai:embedding_37": 331, "ng:ai:embedding_38": 332, "ng:ai:embedding_39": 333, "ng:ai:embedding_40": 334, "ng:ai:embedding_41": 335, "ng:ai:embedding_42": 336, "ng:ai:embedding_43": 337, "ng:ai:embedding_44": 338, "ng:ai:embedding_45": 339, "ng:ai:embedding_46": 340, "ng:ai:embedding_47": 341, "ng:ai:embedding_48": 342, "ng:ai:embedding_49": 343, "ng:ai:embedding_50": 344, "ng:ai:embedding_51": 345, "ng:ai:embedding_52": 346, "ng:ai:embedding_53": 347, "ng:ai:embedding_54": 348, "ng:ai:embedding_55": 349, "ng:ai:embedding_56": 350, "ng:ai:embedding_57": 351, "ng:ai:embedding_58": 352, "ng:ai:embedding_59": 353, "ng:ai:embedding_60": 354, "ng:ai:embedding_61": 355, "ng:ai:embedding_62": 356, "ng:ai:embedding_63": 357, "ng:ai:embedding_64": 358, "ng:ai:embedding_65": 359, "ng:ai:embedding_66": 360, "ng:ai:embedding_67": 361, "ng:ai:embedding_68": 362, "ng:ai:embedding_69": 363, "ng:ai:embedding_70": 364, "ng:ai:embedding_71": 365, "ng:ai:embedding_72": 366, "ng:ai:embedding_73": 367, "ng:ai:embedding_74": 368, "ng:ai:embedding_75": 369, "ng:ai:embedding_76": 370, "ng:ai:embedding_77": 371, "ng:ai:embedding_78": 372, "ng:ai:embedding_79": 373, "ng:ai:embedding_80": 374, "ng:ai:embedding_81": 375, "ng:ai:embedding_82": 376, "ng:ai:embedding_83": 377, "ng:ai:embedding_84": 378, "ng:ai:embedding_85": 379, "ng:ai:embedding_86": 380, "ng:ai:embedding_87": 381, "ng:ai:reasoning": 382, "ng:ai:agent_1": 383, "ng:ai:reasoning_1": 384, "ng:ai:planner_3": 385, "ng:ai:attention_4": 386, "ng:ai:transformer_4": 387, "ng:ai:neural_4": 388, "ng:ai:agent_5": 389, "ng:ai:planner_5": 390, "ng:ai:neural_5": 391, "ng:ai:planner_6": 392, "ng:ai:gradient_6": 393, "ng:ai:transformer_6": 394, "ng:ai:neural_6": 395, "ng:ai:agent_7": 396, "ng:ai:planner_7": 397, "ng:ai:gradient_7": 398, "ng:ai:neural_7": 399, "ng:ai:agent_8": 400, "ng:ai:planner_8": 401, "ng:ai:reasoning_8": 402, "ng:ai:gradient_8": 403, "ng:ai:attention_8": 404, "ng:ai:neural_8": 405, "ng:ai:agent_9": 406, "ng:ai:planner_9": 407, "ng:ai:gradient_9": 408, "ng:ai:agent_10": 409, "ng:ai:reasoning_10": 410, "ng:ai:attention_10": 411, "ng:ai:transformer_10": 412, "ng:ai:attention_11": 413, "ng:ai:neural_11": 414, "ng:ai:agent_12": 415, "ng:ai:planner_12": 416, "ng:ai:reasoning_12": 417, "ng:ai:transformer_12": 418, "ng:ai:reasoning_13": 419, "ng:ai:gradient_13": 420, "ng:ai:neural_13": 421, "ng:ai:planner_14": 422, "ng:ai:reasoning_14": 423, "ng:ai:gradient_14": 424, "ng:ai:attention_14": 425, "ng:ai:neural_14": 426, "ng:ai:agent_15": 427, "ng:ai:planner_15": 428, "ng:ai:reasoning_15": 429, "ng:ai:gradient_15": 430, "ng:ai:attention_15": 431, "ng:ai:planner_16": 432, "ng:ai:reasoning_16": 433, "ng:ai:gradient_16": 434, "ng:ai:attention_16": 435, "ng:ai:transformer_16": 436, "ng:ai:agent_17": 437, "ng:ai:reasoning_17": 438, "ng:ai:attention_17": 439, "ng:ai:neural_17": 440, "ng:ai:agent_18": 441, "ng:ai:reasoning_18": 442, "ng:ai:gradient_18": 443, "ng:ai:attention_18": 444, "ng:ai:planner_19": 445, "ng:ai:reasoning_19": 446, "ng:ai:gradient_19": 447, "ng:ai:attention_19": 448, "ng:ai:transformer_19": 449, "ng:ai:agent_20": 450, "ng:ai:planner_20": 451, "ng:ai:agent_21": 452, "ng:ai:reasoning_21": 453, "ng:ai:gradient_21": 454, "ng:ai:planner_22": 455, "ng:ai:attention_22": 456, "ng:ai:neural_22": 457, "ng:ai:agent_24": 458, "ng:ai:planner_24": 459, "ng:ai:reasoning_24": 460, "ng:ai:gradient_24": 461, "ng:ai:attention_24": 462, "ng:ai:transformer_24": 463, "ng:ai:neural_24": 464, "ng:ai:agent_25": 465, "ng:ai:planner_25": 466, "ng:ai:reasoning_25": 467, "ng:ai:gradient_25": 468, "ng:ai:attention_25": 469, "ng:ai:transformer_25": 470, "ng:ai:neural_25": 471, "ng:ai:agent_26": 472, "ng:ai:planner_26": 473, "ng:ai:reasoning_26": 474, "ng:ai:gradient_26": 475, "ng:ai:attention_26": 476, "ng:ai:transformer_26": 477, "ng:ai:neural_26": 478, "ng:ai:agent_27": 479, "ng:ai:planner_27": 480, "ng:ai:reasoning_27": 481, "ng:ai:gradient_27": 482, "ng:ai:attention_27": 483, "ng:ai:transformer_27": 484, "ng:ai:neural_27": 485, "ng:ai:agent_28": 486, "ng:ai:planner_28": 487, "ng:ai:reasoning_28": 488, "ng:ai:gradient_28": 489, "ng:ai:attention_28": 490, "ng:ai:transformer_28": 491, "ng:ai:neural_28": 492, "ng:ai:agent_29": 493, "ng:ai:planner_29": 494, "ng:ai:reasoning_29": 495, "ng:ai:gradient_29": 496, "ng:ai:attention_29": 497, "ng:ai:transformer_29": 498, "ng:ai:neural_29": 499, "ng:ai:agent_30": 500, "ng:ai:planner_30": 501, "ng:ai:reasoning_30": 502, "ng:ai:gradient_30": 503, "ng:ai:attention_30": 504, "ng:ai:transformer_30": 505, "ng:ai:neural_30": 506, "ng:ai:agent_31": 507, "ng:ai:planner_31": 508, "ng:ai:reasoning_31": 509, "ng:ai:gradient_31": 510, "ng:ai:attention_31": 511, "ng:ai:transformer_31": 512, "ng:ai:neural_31": 513, "ng:ai:agent_32": 514, "ng:ai:planner_32": 515, "ng:ai:reasoning_32": 516, "ng:ai:gradient_32": 517, "ng:ai:attention_32": 518, "ng:ai:transformer_32": 519, "ng:ai:neural_32": 520, "ng:ai:agent_33": 521, "ng:ai:planner_33": 522, "ng:ai:reasoning_33": 523, "ng:ai:gradient_33": 524, "ng:ai:attention_33": 525, "ng:ai:transformer_33": 526, "ng:ai:neural_33": 527, "ng:ai:agent_34": 528, "ng:ai:planner_34": 529, "ng:ai:reasoning_34": 530, "ng:ai:gradient_34": 531, "ng:ai:attention_34": 532, "ng:ai:transformer_34": 533, "ng:ai:neural_34": 534, "ng:ai:agent_35": 535, "ng:ai:planner_35": 536, "ng:ai:reasoning_35": 537, "ng:ai:gradient_35": 538, "ng:ai:attention_35": 539, "ng:ai:transformer_35": 540, "ng:ai:neural_35": 541, "ng:ai:agent_36": 542, "ng:ai:planner_36": 543, "ng:ai:reasoning_36": 544, "ng:ai:gradient_36": 545, "ng:ai:attention_36": 546, "ng:ai:transformer_36": 547, "ng:ai:neural_36": 548, "ng:ai:agent_37": 549, "ng:ai:planner_37": 550, "ng:ai:reasoning_37": 551, "ng:ai:gradient_37": 552, "ng:ai:attention_37": 553, "ng:ai:transformer_37": 554, "ng:ai:neural_37": 555, "ng:ai:agent_38": 556, "ng:ai:planner_38": 557, "ng:ai:reasoning_38": 558, "ng:ai:gradient_38": 559, "ng:ai:attention_38": 560, "ng:ai:transformer_38": 561, "ng:ai:neural_38": 562, "ng:ai:agent_39": 563, "ng:ai:planner_39": 564, "ng:ai:reasoning_39": 565, "ng:ai:gradient_39": 566, "ng:ai:attention_39": 567, "ng:ai:transformer_39": 568, "ng:ai:neural_39": 569, "ng:ai:agent_40": 570, "ng:ai:planner_40": 571, "ng:ai:reasoning_40": 572, "ng:ai:gradient_40": 573, "ng:ai:attention_40": 574, "ng:ai:transformer_40": 575, "ng:ai:neural_40": 576, "ng:ai:agent_41": 577, "ng:ai:planner_41": 578, "ng:ai:reasoning_41": 579, "ng:ai:gradient_41": 580, "ng:ai:attention_41": 581, "ng:ai:transformer_41": 582, "ng:ai:neural_41": 583, "ng:ai:agent_42": 584, "ng:ai:planner_42": 585, "ng:ai:reasoning_42": 586, "ng:ai:gradient_42": 587, "ng:ai:attention_42": 588, "ng:ai:transformer_42": 589, "ng:ai:neural_42": 590, "ng:ai:agent_43": 591, "ng:ai:planner_43": 592, "ng:ai:reasoning_43": 593, "ng:ai:gradient_43": 594, "ng:ai:attention_43": 595, "ng:ai:transformer_43": 596, "ng:ai:neural_43": 597, "ng:ai:agent_44": 598, "ng:ai:planner_44": 599, "ng:ai:reasoning_44": 600, "ng:ai:gradient_44": 601, "ng:ai:attention_44": 602, "ng:ai:transformer_44": 603, "ng:ai:neural_44": 604, "ng:ai:agent_45": 605, "ng:ai:planner_45": 606, "ng:ai:reasoning_45": 607, "ng:ai:gradient_45": 608, "ng:ai:attention_45": 609, "ng:ai:transformer_45": 610, "ng:ai:neural_45": 611, "ng:ai:agent_46": 612, "ng:ai:planner_46": 613, "ng:ai:reasoning_46": 614, "ng:ai:gradient_46": 615, "ng:ai:attention_46": 616, "ng:ai:transformer_46": 617, "ng:ai:neural_46": 618, "ng:ai:agent_47": 619, "ng:ai:planner_47": 620, "ng:ai:reasoning_47": 621, "ng:ai:gradient_47": 622, "ng:ai:attention_47": 623, "ng:ai:transformer_47": 624, "ng:ai:neural_47": 625, "ng:ai:agent_48": 626, "ng:ai:planner_48": 627, "ng:ai:reasoning_48": 628, "ng:ai:gradient_48": 629, "ng:ai:attention_48": 630, "ng:ai:transformer_48": 631, "ng:ai:neural_48": 632, "ng:ai:agent_49": 633, "ng:ai:planner_49": 634, "ng:ai:reasoning_49": 635, "ng:ai:gradient_49": 636, "ng:ai:attention_49": 637, "ng:ai:transformer_49": 638, "ng:ai:neural_49": 639, "ng:ai:agent_50": 640, "ng:ai:planner_50": 641, "ng:ai:reasoning_50": 642, "ng:ai:gradient_50": 643, "ng:ai:attention_50": 644, "ng:ai:transformer_50": 645, "ng:ai:neural_50": 646, "ng:ai:agent_51": 647, "ng:ai:planner_51": 648, "ng:ai:reasoning_51": 649, "ng:ai:gradient_51": 650, "ng:ai:attention_51": 651, "ng:ai:transformer_51": 652, "ng:ai:neural_51": 653, "ng:ai:agent_52": 654, "ng:ai:planner_52": 655, "ng:ai:reasoning_52": 656, "ng:ai:gradient_52": 657, "ng:ai:attention_52": 658, "ng:ai:transformer_52": 659, "ng:ai:neural_52": 660, "ng:ai:agent_53": 661, "ng:ai:planner_53": 662, "ng:ai:reasoning_53": 663, "ng:ai:gradient_53": 664, "ng:ai:attention_53": 665, "ng:ai:transformer_53": 666, "ng:ai:neural_53": 667, "ng:ai:agent_54": 668, "ng:ai:planner_54": 669, "ng:ai:reasoning_54": 670, "ng:ai:gradient_54": 671, "ng:ai:attention_54": 672, "ng:ai:transformer_54": 673, "ng:ai:neural_54": 674, "ng:ai:agent_55": 675, "ng:ai:planner_55": 676, "ng:ai:reasoning_55": 677, "ng:ai:gradient_55": 678, "ng:ai:attention_55": 679, "ng:ai:transformer_55": 680, "ng:ai:neural_55": 681, "ng:ai:agent_56": 682, "ng:ai:planner_56": 683, "ng:ai:reasoning_56": 684, "ng:ai:gradient_56": 685, "ng:ai:attention_56": 686, "ng:ai:transformer_56": 687, "ng:ai:neural_56": 688, "ng:ai:agent_57": 689, "ng:ai:planner_57": 690, "ng:ai:reasoning_57": 691, "ng:ai:gradient_57": 692, "ng:ai:attention_57": 693, "ng:ai:transformer_57": 694, "ng:ai:neural_57": 695, "ng:ai:agent_58": 696, "ng:ai:planner_58": 697, "ng:ai:reasoning_58": 698, "ng:ai:gradient_58": 699, "ng:ai:attention_58": 700, "ng:ai:transformer_58": 701, "ng:ai:neural_58": 702, "ng:ai:agent_59": 703, "ng:ai:planner_59": 704, "ng:ai:reasoning_59": 705, "ng:ai:gradient_59": 706, "ng:ai:attention_59": 707, "ng:ai:transformer_59": 708, "ng:ai:neural_59": 709, "ng:ai:agent_60": 710, "ng:ai:planner_60": 711, "ng:ai:reasoning_60": 712, "ng:ai:gradient_60": 713, "ng:ai:attention_60": 714, "ng:ai:transformer_60": 715, "ng:ai:neural_60": 716, "ng:ai:agent_61": 717, "ng:ai:planner_61": 718, "ng:ai:reasoning_61": 719, "ng:ai:gradient_61": 720, "ng:ai:attention_61": 721, "ng:ai:transformer_61": 722, "ng:ai:neural_61": 723, "ng:ai:agent_62": 724, "ng:ai:planner_62": 725, "ng:ai:reasoning_62": 726, "ng:ai:gradient_62": 727, "ng:ai:attention_62": 728, "ng:ai:transformer_62": 729, "ng:ai:neural_62": 730, "ng:ai:agent_63": 731, "ng:ai:planner_63": 732, "ng:ai:reasoning_63": 733, "ng:ai:gradient_63": 734, "ng:ai:attention_63": 735, "ng:ai:transformer_63": 736, "ng:ai:neural_63": 737, "ng:ai:agent_64": 738, "ng:ai:planner_64": 739, "ng:ai:reasoning_64": 740, "ng:ai:gradient_64": 741, "ng:ai:attention_64": 742, "ng:ai:transformer_64": 743, "ng:ai:neural_64": 744, "ng:ai:agent_65": 745, "ng:ai:planner_65": 746, "ng:ai:reasoning_65": 747, "ng:ai:gradient_65": 748, "ng:ai:attention_65": 749, "ng:ai:transformer_65": 750, "ng:ai:neural_65": 751, "ng:ai:agent_66": 752, "ng:ai:planner_66": 753, "ng:ai:reasoning_66": 754, "ng:ai:gradient_66": 755, "ng:ai:attention_66": 756, "ng:ai:transformer_66": 757, "ng:ai:neural_66": 758, "ng:ai:agent_67": 759, "ng:ai:planner_67": 760, "ng:ai:reasoning_67": 761, "ng:ai:gradient_67": 762, "ng:ai:attention_67": 763, "ng:ai:transformer_67": 764, "ng:ai:neural_67": 765, "ng:ai:agent_68": 766, "ng:ai:planner_68": 767, "ng:ai:reasoning_68": 768, "ng:ai:gradient_68": 769, "ng:ai:attention_68": 770, "ng:ai:transformer_68": 771, "ng:ai:neural_68": 772, "ng:ai:agent_69": 773, "ng:ai:planner_69": 774, "ng:ai:reasoning_69": 775, "ng:ai:gradient_69": 776, "ng:ai:attention_69": 777, "ng:ai:transformer_69": 778, "ng:ai:neural_69": 779, "ng:ai:agent_70": 780, "ng:ai:planner_70": 781, "ng:ai:reasoning_70": 782, "ng:ai:gradient_70": 783, "ng:ai:attention_70": 784, "ng:ai:transformer_70": 785, "ng:ai:neural_70": 786, "ng:ai:agent_71": 787, "ng:ai:planner_71": 788, "ng:ai:reasoning_71": 789, "ng:ai:gradient_71": 790, "ng:ai:attention_71": 791, "ng:ai:transformer_71": 792, "ng:ai:neural_71": 793, "ng:ai:agent_72": 794, "ng:ai:planner_72": 795, "ng:ai:reasoning_72": 796, "ng:ai:gradient_72": 797, "ng:ai:attention_72": 798, "ng:ai:transformer_72": 799, "ng:ai:neural_72": 800, "ng:ai:agent_73": 801, "ng:ai:planner_73": 802, "ng:ai:reasoning_73": 803, "ng:ai:gradient_73": 804, "ng:ai:attention_73": 805, "ng:ai:transformer_73": 806, "ng:ai:neural_73": 807, "ng:ai:agent_74": 808, "ng:ai:planner_74": 809, "ng:ai:reasoning_74": 810, "ng:ai:gradient_74": 811, "ng:ai:attention_74": 812, "ng:ai:transformer_74": 813, "ng:ai:neural_74": 814, "ng:ai:agent_75": 815, "ng:ai:planner_75": 816, "ng:ai:reasoning_75": 817, "ng:ai:gradient_75": 818, "ng:ai:attention_75": 819, "ng:ai:transformer_75": 820, "ng:ai:neural_75": 821, "ng:ai:agent_76": 822, "ng:ai:planner_76": 823, "ng:ai:reasoning_76": 824, "ng:ai:gradient_76": 825, "ng:ai:attention_76": 826, "ng:ai:transformer_76": 827, "ng:ai:neural_76": 828, "ng:ai:agent_77": 829, "ng:ai:planner_77": 830, "ng:ai:reasoning_77": 831, "ng:ai:gradient_77": 832, "ng:ai:attention_77": 833, "ng:ai:transformer_77": 834, "ng:ai:neural_77": 835, "ng:ai:agent_78": 836, "ng:ai:planner_78": 837, "ng:ai:reasoning_78": 838, "ng:ai:gradient_78": 839, "ng:ai:attention_78": 840, "ng:ai:transformer_78": 841, "ng:ai:neural_78": 842, "ng:ai:agent_79": 843, "ng:ai:planner_79": 844, "ng:ai:reasoning_79": 845, "ng:ai:gradient_79": 846, "ng:ai:attention_79": 847, "ng:ai:transformer_79": 848, "ng:ai:neural_79": 849, "ng:ai:agent_80": 850, "ng:ai:planner_80": 851, "ng:ai:reasoning_80": 852, "ng:ai:gradient_80": 853, "ng:ai:attention_80": 854, "ng:ai:transformer_80": 855, "ng:ai:neural_80": 856, "ng:ai:agent_81": 857, "ng:ai:planner_81": 858, "ng:ai:reasoning_81": 859, "ng:ai:gradient_81": 860, "ng:ai:attention_81": 861, "ng:ai:transformer_81": 862, "ng:ai:neural_81": 863, "ng:ai:agent_82": 864, "ng:ai:planner_82": 865, "ng:ai:reasoning_82": 866, "ng:ai:gradient_82": 867, "ng:ai:attention_82": 868, "ng:ai:transformer_82": 869, "ng:ai:neural_82": 870, "ng:ai:agent_83": 871, "ng:ai:planner_83": 872, "ng:ai:reasoning_83": 873, "ng:ai:gradient_83": 874, "ng:ai:attention_83": 875, "ng:ai:transformer_83": 876, "ng:ai:neural_83": 877, "ng:ai:agent_84": 878, "ng:ai:planner_84": 879, "ng:ai:reasoning_84": 880, "ng:ai:gradient_84": 881, "ng:ai:attention_84": 882, "ng:ai:transformer_84": 883, "ng:ai:neural_84": 884, "ng:ai:agent_85": 885, "ng:ai:planner_85": 886, "ng:ai:reasoning_85": 887, "ng:ai:gradient_85": 888, "ng:ai:attention_85": 889, "ng:ai:transformer_85": 890, "ng:ai:neural_85": 891, "ng:ai:agent_86": 892, "ng:ai:planner_86": 893, "ng:ai:reasoning_86": 894, "ng:ai:gradient_86": 895, "ng:ai:attention_86": 896, "ng:ai:transformer_86": 897, "ng:ai:neural_86": 898, "ng:ai:agent_87": 899, "ng:ai:planner_87": 900, "ng:ai:reasoning_87": 901, "ng:ai:gradient_87": 902, "ng:ai:attention_87": 903, "ng:ai:transformer_87": 904, "ng:ai:neural_87": 905, "℻": 906, "⇐": 907, "⇴": 908, "↬": 909, "↾": 910, "ℙ": 911, "ℽ": 912, "⇤": 913, "↜": 914, "⇶": 915, "⣲": 916, "⣸": 917, "⠨": 918, "ⷩ": 919, "ⷡ": 920, "Ɡ": 921, "ꞏ": 922, "⸨": 923, "𝒞": 924, "𝒴": 925, "⸬": 926, "𝔙": 927, "𝘥": 928, "ꟃ": 929, "𝔅": 930, "𝕫": 931, "𝟷": 932, "𝖦": 933, "𝑷": 934, "⇇": 935, "ℎ": 936, "↣": 937, "↑": 938, "ⅅ": 939, "⅍": 940, "ℨ": 941, "№": 942, "℺": 943, "⇏": 944, "⍺": 945, "⧸": 946, "➿": 947, "≷": 948, "➚": 949, "≶": 950, "⩹": 951, "⧶": 952, "⥹": 953, "⯟": 954, "⍷": 955, "⭶": 956, "≸": 957, "❸": 958, "⍶": 959, "❶": 960, "⭵": 961, "❷": 962, "⥷": 963, "⫲": 964, "❹": 965, "❺": 966, "⥺": 967, "⭹": 968, "⍹": 969, "⩸": 970, "🚛": 971, "🚗": 972, "🚼": 973, "🛄": 974, "🛀": 975, "🛁": 976, "🛉": 977, "🚸": 978, "🛈": 979, "🛋": 980, "🛃": 981, "🚚": 982, "🚪": 983, "🛊": 984, "🛅": 985, "🚹": 986, "🚝": 987, "🚞": 988, "🚫": 989, "🚭": 990, "🚘": 991, "🚖": 992, "🛂": 993, "🛇": 994, "🚙": 995, "🚻": 996, "🚿": 997, "🚬": 998, "🚕": 999, "🚽": 1000, "🚜": 1001, "🛆": 1002, "🚩": 1003, "🚾": 1004, "🚺": 1005, "②": 1006, "⑬": 1007, "⇫": 1008, "⇻": 1009, "⋩": 1010, "⍄": 1011, "⍋": 1012, "⋄": 1013, "‡": 1014, "ℝ": 1015, " ": 1016, "∎": 1017, "∜": 1018, "≑": 1019, "―": 1020, "⇔": 1021, "∑": 1022, "⑻": 1023, "⑸": 1024, "≾": 1025, "∷": 1026, "⊾": 1027, "ⅴ": 1028, "⏱": 1029, "⁸": 1030, "⇳": 1031, "⇡": 1032, "⅑": 1033, "ng:code:optimize": 1034, "ng:code:async_1": 1035, "ng:code:async_2": 1036, "ng:code:pattern_2": 1037, "ng:code:refactor_2": 1038, "ng:code:pattern_3": 1039, "ng:code:closure_4": 1040, "ng:code:recursion_4": 1041, "ng:code:ast_4": 1042, "ng:code:loop_4": 1043, "ng:code:async_4": 1044, "ng:code:pattern_4": 1045, "ng:code:refactor_4": 1046, "ng:code:optimize_4": 1047, "ng:code:loop_5": 1048, "ng:code:async_5": 1049, "ng:code:pattern_5": 1050, "ng:code:closure_6": 1051, "ng:code:loop_6": 1052, "ng:code:pattern_6": 1053, "ng:code:closure_7": 1054, "ng:code:ast_7": 1055, "ng:code:loop_7": 1056, "ng:code:optimize_7": 1057, "ng:code:recursion_8": 1058, "ng:code:ast_8": 1059, "ng:code:loop_8": 1060, "ng:code:async_8": 1061, "ng:code:optimize_8": 1062, "ng:code:pattern_9": 1063, "ng:code:loop_10": 1064, "ng:code:pattern_10": 1065, "ng:code:refactor_10": 1066, "ng:code:ast_11": 1067, "ng:code:loop_11": 1068, "ng:code:async_11": 1069, "ng:code:pattern_11": 1070, "ng:code:recursion_12": 1071, "ng:code:async_12": 1072, "ng:code:closure_13": 1073, "ng:code:ast_13": 1074, "ng:code:loop_13": 1075, "ng:code:refactor_13": 1076, "ng:code:closure_14": 1077, "ng:code:recursion_14": 1078, "ng:code:ast_14": 1079, "ng:code:async_14": 1080, "ng:code:pattern_14": 1081, "ng:code:refactor_14": 1082, "ng:code:recursion_15": 1083, "ng:code:ast_15": 1084, "ng:code:loop_15": 1085, "ng:code:async_15": 1086, "ng:code:closure_16": 1087, "ng:code:recursion_16": 1088, "ng:code:loop_16": 1089, "ng:code:async_16": 1090, "ng:code:pattern_16": 1091, "ng:code:refactor_16": 1092, "ng:code:closure_17": 1093, "ng:code:recursion_17": 1094, "ng:code:pattern_17": 1095, "ng:code:refactor_17": 1096, "ng:code:optimize_17": 1097, "ng:code:closure_18": 1098, "ng:code:ast_18": 1099, "ng:code:refactor_18": 1100, "ng:code:optimize_18": 1101, "ng:code:recursion_19": 1102, "ng:code:ast_19": 1103, "ng:code:loop_19": 1104, "ng:code:async_19": 1105, "ng:code:pattern_19": 1106, "ng:code:closure_20": 1107, "ng:code:recursion_20": 1108, "ng:code:ast_20": 1109, "ng:code:pattern_20": 1110, "ng:code:closure_21": 1111, "ng:code:loop_21": 1112, "ng:code:recursion_22": 1113, "ng:code:ast_22": 1114, "ng:code:loop_22": 1115, "ng:code:async_22": 1116, "ng:code:async_23": 1117, "ng:code:closure_24": 1118, "ng:code:recursion_24": 1119, "ng:code:ast_24": 1120, "ng:code:loop_24": 1121, "ng:code:async_24": 1122, "ng:code:pattern_24": 1123, "ng:code:refactor_24": 1124, "ng:code:optimize_24": 1125, "ng:code:closure_25": 1126, "ng:code:recursion_25": 1127, "ng:code:ast_25": 1128, "ng:code:loop_25": 1129, "ng:code:async_25": 1130, "ng:code:pattern_25": 1131, "ng:code:refactor_25": 1132, "ng:code:optimize_25": 1133, "ng:code:closure_26": 1134, "ng:code:recursion_26": 1135, "ng:code:ast_26": 1136, "ng:code:loop_26": 1137, "ng:code:async_26": 1138, "ng:code:pattern_26": 1139, "ng:code:refactor_26": 1140, "ng:code:optimize_26": 1141, "ng:code:closure_27": 1142, "ng:code:recursion_27": 1143, "ng:code:ast_27": 1144, "ng:code:loop_27": 1145, "ng:code:async_27": 1146, "ng:code:pattern_27": 1147, "ng:code:refactor_27": 1148, "ng:code:optimize_27": 1149, "ng:code:closure_28": 1150, "ng:code:recursion_28": 1151, "ng:code:ast_28": 1152, "ng:code:loop_28": 1153, "ng:code:async_28": 1154, "ng:code:pattern_28": 1155, "ng:code:refactor_28": 1156, "ng:code:optimize_28": 1157, "ng:code:closure_29": 1158, "ng:code:ast_29": 1159, "ng:code:loop_29": 1160, "ng:code:async_29": 1161, "ng:code:pattern_29": 1162, "ng:code:refactor_29": 1163, "ng:code:optimize_29": 1164, "ng:code:closure_30": 1165, "ng:code:recursion_30": 1166, "ng:code:ast_30": 1167, "ng:code:loop_30": 1168, "ng:code:async_30": 1169, "ng:code:pattern_30": 1170, "ng:code:refactor_30": 1171, "ng:code:optimize_30": 1172, "ng:code:closure_31": 1173, "ng:code:recursion_31": 1174, "ng:code:ast_31": 1175, "ng:code:loop_31": 1176, "ng:code:async_31": 1177, "ng:code:pattern_31": 1178, "ng:code:refactor_31": 1179, "ng:code:optimize_31": 1180, "ng:code:closure_32": 1181, "ng:code:recursion_32": 1182, "ng:code:ast_32": 1183, "ng:code:loop_32": 1184, "ng:code:async_32": 1185, "ng:code:pattern_32": 1186, "ng:code:refactor_32": 1187, "ng:code:optimize_32": 1188, "ng:code:closure_33": 1189, "ng:code:recursion_33": 1190, "ng:code:ast_33": 1191, "ng:code:loop_33": 1192, "ng:code:async_33": 1193, "ng:code:pattern_33": 1194, "ng:code:refactor_33": 1195, "ng:code:optimize_33": 1196, "ng:code:closure_34": 1197, "ng:code:recursion_34": 1198, "ng:code:ast_34": 1199, "ng:code:loop_34": 1200, "ng:code:async_34": 1201, "ng:code:pattern_34": 1202, "ng:code:refactor_34": 1203, "ng:code:optimize_34": 1204, "ng:code:closure_35": 1205, "ng:code:recursion_35": 1206, "ng:code:ast_35": 1207, "ng:code:loop_35": 1208, "ng:code:async_35": 1209, "ng:code:pattern_35": 1210, "ng:code:refactor_35": 1211, "ng:code:optimize_35": 1212, "ng:code:closure_36": 1213, "ng:code:recursion_36": 1214, "ng:code:ast_36": 1215, "ng:code:loop_36": 1216, "ng:code:async_36": 1217, "ng:code:pattern_36": 1218, "ng:code:refactor_36": 1219, "ng:code:optimize_36": 1220, "ng:code:closure_37": 1221, "ng:code:recursion_37": 1222, "ng:code:ast_37": 1223, "ng:code:loop_37": 1224, "ng:code:async_37": 1225, "ng:code:pattern_37": 1226, "ng:code:refactor_37": 1227, "ng:code:optimize_37": 1228, "ng:code:closure_38": 1229, "ng:code:recursion_38": 1230, "ng:code:ast_38": 1231, "ng:code:loop_38": 1232, "ng:code:async_38": 1233, "ng:code:pattern_38": 1234, "ng:code:refactor_38": 1235, "ng:code:optimize_38": 1236, "ng:code:closure_39": 1237, "ng:code:recursion_39": 1238, "ng:code:ast_39": 1239, "ng:code:loop_39": 1240, "ng:code:async_39": 1241, "ng:code:pattern_39": 1242, "ng:code:refactor_39": 1243, "ng:code:optimize_39": 1244, "ng:code:closure_40": 1245, "ng:code:recursion_40": 1246, "ng:code:ast_40": 1247, "ng:code:loop_40": 1248, "ng:code:async_40": 1249, "ng:code:pattern_40": 1250, "ng:code:refactor_40": 1251, "ng:code:optimize_40": 1252, "ng:code:closure_41": 1253, "ng:code:recursion_41": 1254, "ng:code:ast_41": 1255, "ng:code:loop_41": 1256, "ng:code:async_41": 1257, "ng:code:pattern_41": 1258, "ng:code:refactor_41": 1259, "ng:code:optimize_41": 1260, "ng:code:closure_42": 1261, "ng:code:recursion_42": 1262, "ng:code:ast_42": 1263, "ng:code:loop_42": 1264, "ng:code:async_42": 1265, "ng:code:pattern_42": 1266, "ng:code:refactor_42": 1267, "ng:code:optimize_42": 1268, "ng:code:closure_43": 1269, "ng:code:recursion_43": 1270, "ng:code:ast_43": 1271, "ng:code:loop_43": 1272, "ng:code:async_43": 1273, "ng:code:pattern_43": 1274, "ng:code:refactor_43": 1275, "ng:code:optimize_43": 1276, "ng:code:closure_44": 1277, "ng:code:recursion_44": 1278, "ng:code:ast_44": 1279, "ng:code:loop_44": 1280, "ng:code:async_44": 1281, "ng:code:pattern_44": 1282, "ng:code:refactor_44": 1283, "ng:code:optimize_44": 1284, "ng:code:closure_45": 1285, "ng:code:recursion_45": 1286, "ng:code:ast_45": 1287, "ng:code:loop_45": 1288, "ng:code:async_45": 1289, "ng:code:pattern_45": 1290, "ng:code:refactor_45": 1291, "ng:code:optimize_45": 1292, "ng:code:closure_46": 1293, "ng:code:recursion_46": 1294, "ng:code:ast_46": 1295, "ng:code:loop_46": 1296, "ng:code:async_46": 1297, "ng:code:pattern_46": 1298, "ng:code:refactor_46": 1299, "ng:code:optimize_46": 1300, "ng:code:closure_47": 1301, "ng:code:recursion_47": 1302, "ng:code:ast_47": 1303, "ng:code:loop_47": 1304, "ng:code:async_47": 1305, "ng:code:pattern_47": 1306, "ng:code:refactor_47": 1307, "ng:code:optimize_47": 1308, "ng:code:closure_48": 1309, "ng:code:recursion_48": 1310, "ng:code:ast_48": 1311, "ng:code:loop_48": 1312, "ng:code:async_48": 1313, "ng:code:pattern_48": 1314, "ng:code:refactor_48": 1315, "ng:code:optimize_48": 1316, "ng:code:closure_49": 1317, "ng:code:recursion_49": 1318, "ng:code:ast_49": 1319, "ng:code:loop_49": 1320, "ng:code:async_49": 1321, "ng:code:pattern_49": 1322, "ng:code:refactor_49": 1323, "ng:code:optimize_49": 1324, "ng:code:closure_50": 1325, "ng:code:recursion_50": 1326, "ng:code:ast_50": 1327, "ng:code:loop_50": 1328, "ng:code:async_50": 1329, "ng:code:pattern_50": 1330, "ng:code:refactor_50": 1331, "ng:code:optimize_50": 1332, "ng:code:closure_51": 1333, "ng:code:recursion_51": 1334, "ng:code:ast_51": 1335, "ng:code:loop_51": 1336, "ng:code:async_51": 1337, "ng:code:pattern_51": 1338, "ng:code:refactor_51": 1339, "ng:code:optimize_51": 1340, "ng:code:closure_52": 1341, "ng:code:recursion_52": 1342, "ng:code:ast_52": 1343, "ng:code:loop_52": 1344, "ng:code:async_52": 1345, "ng:code:pattern_52": 1346, "ng:code:refactor_52": 1347, "ng:code:optimize_52": 1348, "ng:code:closure_53": 1349, "ng:code:recursion_53": 1350, "ng:code:ast_53": 1351, "ng:code:loop_53": 1352, "ng:code:async_53": 1353, "ng:code:pattern_53": 1354, "ng:code:refactor_53": 1355, "ng:code:optimize_53": 1356, "ng:code:closure_54": 1357, "ng:code:recursion_54": 1358, "ng:code:ast_54": 1359, "ng:code:loop_54": 1360, "ng:code:async_54": 1361, "ng:code:pattern_54": 1362, "ng:code:refactor_54": 1363, "ng:code:optimize_54": 1364, "ng:code:closure_55": 1365, "ng:code:recursion_55": 1366, "ng:code:ast_55": 1367, "ng:code:loop_55": 1368, "ng:code:async_55": 1369, "ng:code:pattern_55": 1370, "ng:code:refactor_55": 1371, "ng:code:optimize_55": 1372, "ng:code:closure_56": 1373, "ng:code:recursion_56": 1374, "ng:code:ast_56": 1375, "ng:code:loop_56": 1376, "ng:code:async_56": 1377, "ng:code:pattern_56": 1378, "ng:code:refactor_56": 1379, "ng:code:optimize_56": 1380, "ng:code:closure_57": 1381, "ng:code:recursion_57": 1382, "ng:code:ast_57": 1383, "ng:code:loop_57": 1384, "ng:code:async_57": 1385, "ng:code:pattern_57": 1386, "ng:code:refactor_57": 1387, "ng:code:optimize_57": 1388, "ng:code:closure_58": 1389, "ng:code:recursion_58": 1390, "ng:code:ast_58": 1391, "ng:code:loop_58": 1392, "ng:code:async_58": 1393, "ng:code:pattern_58": 1394, "ng:code:refactor_58": 1395, "ng:code:optimize_58": 1396, "ng:code:closure_59": 1397, "ng:code:recursion_59": 1398, "ng:code:ast_59": 1399, "ng:code:loop_59": 1400, "ng:code:async_59": 1401, "ng:code:pattern_59": 1402, "ng:code:refactor_59": 1403, "ng:code:optimize_59": 1404, "ng:code:closure_60": 1405, "ng:code:recursion_60": 1406, "ng:code:ast_60": 1407, "ng:code:loop_60": 1408, "ng:code:async_60": 1409, "ng:code:pattern_60": 1410, "ng:code:refactor_60": 1411, "ng:code:optimize_60": 1412, "ng:code:closure_61": 1413, "ng:code:recursion_61": 1414, "ng:code:ast_61": 1415, "ng:code:loop_61": 1416, "ng:code:async_61": 1417, "ng:code:pattern_61": 1418, "ng:code:refactor_61": 1419, "ng:code:optimize_61": 1420, "ng:code:closure_62": 1421, "ng:code:recursion_62": 1422, "ng:code:ast_62": 1423, "ng:code:loop_62": 1424, "ng:code:async_62": 1425, "ng:code:pattern_62": 1426, "ng:code:refactor_62": 1427, "ng:code:optimize_62": 1428, "ng:code:closure_63": 1429, "ng:code:recursion_63": 1430, "ng:code:ast_63": 1431, "ng:code:loop_63": 1432, "ng:code:async_63": 1433, "ng:code:pattern_63": 1434, "ng:code:refactor_63": 1435, "ng:code:optimize_63": 1436, "ng:code:closure_64": 1437, "ng:code:recursion_64": 1438, "ng:code:ast_64": 1439, "ng:code:loop_64": 1440, "ng:code:async_64": 1441, "ng:code:pattern_64": 1442, "ng:code:refactor_64": 1443, "ng:code:optimize_64": 1444, "ng:code:closure_65": 1445, "ng:code:recursion_65": 1446, "ng:code:ast_65": 1447, "ng:code:loop_65": 1448, "ng:code:async_65": 1449, "ng:code:pattern_65": 1450, "ng:code:refactor_65": 1451, "ng:code:optimize_65": 1452, "ng:code:closure_66": 1453, "ng:code:recursion_66": 1454, "ng:code:ast_66": 1455, "ng:code:loop_66": 1456, "ng:code:async_66": 1457, "ng:code:pattern_66": 1458, "ng:code:refactor_66": 1459, "ng:code:optimize_66": 1460, "ng:code:closure_67": 1461, "ng:code:recursion_67": 1462, "ng:code:ast_67": 1463, "ng:code:loop_67": 1464, "ng:code:async_67": 1465, "ng:code:pattern_67": 1466, "ng:code:refactor_67": 1467, "ng:code:optimize_67": 1468, "ng:code:closure_68": 1469, "ng:code:recursion_68": 1470, "ng:code:ast_68": 1471, "ng:code:loop_68": 1472, "ng:code:async_68": 1473, "ng:code:pattern_68": 1474, "ng:code:refactor_68": 1475, "ng:code:optimize_68": 1476, "ng:code:closure_69": 1477, "ng:code:recursion_69": 1478, "ng:code:loop_69": 1479, "ng:code:async_69": 1480, "ng:code:pattern_69": 1481, "ng:code:refactor_69": 1482, "ng:code:optimize_69": 1483, "ng:code:closure_70": 1484, "ng:code:recursion_70": 1485, "ng:code:ast_70": 1486, "ng:code:loop_70": 1487, "ng:code:async_70": 1488, "ng:code:pattern_70": 1489, "ng:code:refactor_70": 1490, "ng:code:optimize_70": 1491, "ng:code:closure_71": 1492, "ng:code:recursion_71": 1493, "ng:code:ast_71": 1494, "ng:code:loop_71": 1495, "ng:code:async_71": 1496, "ng:code:pattern_71": 1497, "ng:code:refactor_71": 1498, "ng:code:optimize_71": 1499, "ng:code:closure_72": 1500, "ng:code:recursion_72": 1501, "ng:code:ast_72": 1502, "ng:code:loop_72": 1503, "ng:code:async_72": 1504, "ng:code:pattern_72": 1505, "ng:code:refactor_72": 1506, "ng:code:optimize_72": 1507, "ng:code:closure_73": 1508, "ng:code:recursion_73": 1509, "ng:code:ast_73": 1510, "ng:code:loop_73": 1511, "ng:code:async_73": 1512, "ng:code:refactor_73": 1513, "ng:code:optimize_73": 1514, "ng:code:closure_74": 1515, "ng:code:recursion_74": 1516, "ng:code:ast_74": 1517, "ng:code:loop_74": 1518, "ng:code:async_74": 1519, "ng:code:pattern_74": 1520, "ng:code:refactor_74": 1521, "ng:code:optimize_74": 1522, "ng:code:closure_75": 1523, "ng:code:recursion_75": 1524, "ng:code:ast_75": 1525, "ng:code:loop_75": 1526, "ng:code:async_75": 1527, "ng:code:pattern_75": 1528, "ng:code:refactor_75": 1529, "ng:code:optimize_75": 1530, "ng:code:closure_76": 1531, "ng:code:recursion_76": 1532, "ng:code:ast_76": 1533, "ng:code:loop_76": 1534, "ng:code:async_76": 1535, "ng:code:pattern_76": 1536, "ng:code:refactor_76": 1537, "ng:code:optimize_76": 1538, "ng:code:closure_77": 1539, "ng:code:recursion_77": 1540, "ng:code:ast_77": 1541, "ng:code:loop_77": 1542, "ng:code:async_77": 1543, "ng:code:pattern_77": 1544, "ng:code:refactor_77": 1545, "ng:code:optimize_77": 1546, "ng:code:closure_78": 1547, "ng:code:recursion_78": 1548, "ng:code:ast_78": 1549, "ng:code:loop_78": 1550, "ng:code:async_78": 1551, "ng:code:pattern_78": 1552, "ng:code:refactor_78": 1553, "ng:code:optimize_78": 1554, "ng:code:closure_79": 1555, "ng:code:recursion_79": 1556, "ng:code:ast_79": 1557, "ng:code:loop_79": 1558, "ng:code:async_79": 1559, "ng:code:pattern_79": 1560, "ng:code:refactor_79": 1561, "ng:code:optimize_79": 1562, "ng:code:closure_80": 1563, "ng:code:recursion_80": 1564, "ng:code:ast_80": 1565, "ng:code:loop_80": 1566, "ng:code:async_80": 1567, "ng:code:pattern_80": 1568, "ng:code:refactor_80": 1569, "ng:code:optimize_80": 1570, "ng:code:closure_81": 1571, "ng:code:recursion_81": 1572, "ng:code:ast_81": 1573, "ng:code:loop_81": 1574, "ng:code:async_81": 1575, "ng:code:pattern_81": 1576, "ng:code:refactor_81": 1577, "ng:code:optimize_81": 1578, "ng:code:closure_82": 1579, "ng:code:recursion_82": 1580, "ng:code:ast_82": 1581, "ng:code:loop_82": 1582, "ng:code:async_82": 1583, "ng:code:pattern_82": 1584, "ng:code:refactor_82": 1585, "ng:code:optimize_82": 1586, "ng:code:closure_83": 1587, "ng:code:recursion_83": 1588, "ng:code:ast_83": 1589, "ng:code:loop_83": 1590, "ng:code:async_83": 1591, "ng:code:pattern_83": 1592, "ng:code:refactor_83": 1593, "ng:code:optimize_83": 1594, "ng:code:closure_84": 1595, "ng:code:recursion_84": 1596, "ng:code:ast_84": 1597, "ng:code:loop_84": 1598, "ng:code:async_84": 1599, "ng:code:pattern_84": 1600, "ng:code:refactor_84": 1601, "ng:code:optimize_84": 1602, "ng:code:closure_85": 1603, "ng:code:recursion_85": 1604, "ng:code:ast_85": 1605, "ng:code:loop_85": 1606, "ng:code:async_85": 1607, "ng:code:pattern_85": 1608, "ng:code:refactor_85": 1609, "ng:code:optimize_85": 1610, "ng:code:closure_86": 1611, "ng:code:recursion_86": 1612, "ng:code:ast_86": 1613, "ng:code:loop_86": 1614, "ng:code:async_86": 1615, "ng:code:pattern_86": 1616, "ng:code:refactor_86": 1617, "ng:code:optimize_86": 1618, "ng:code:closure_87": 1619, "ng:code:recursion_87": 1620, "ng:code:ast_87": 1621, "ng:code:loop_87": 1622, "ng:code:async_87": 1623, "ng:code:pattern_87": 1624, "ng:code:refactor_87": 1625, "ng:code:optimize_87": 1626, "⍑": 1627, "⍱": 1628, "⌒": 1629, "₳": 1630, "ℜ": 1631, "⌞": 1632, "⠃": 1633, "⡕": 1634, "⢮": 1635, "⊙": 1636, "∲": 1637, "⊵": 1638, "⋽": 1639, "⏋": 1640, "⊮": 1641, "‖": 1642, "ℕ": 1643, "⋵": 1644, "⋕": 1645, "≚": 1646, "⎽": 1647, "⌛": 1648, "≓": 1649, "∫": 1650, "ₛ": 1651, "↮": 1652, "⁅": 1653, "⎡": 1654, "↢": 1655, "⋦": 1656, "𝔪": 1657, "𝑍": 1658, "⋀": 1659, "⊈": 1660, "₪": 1661, "≉": 1662, "≠": 1663, "⋬": 1664, "≁": 1665, "⒔": 1666, "⒜": 1667, " ": 1668, "⎭": 1669, "⸊": 1670, "⇢": 1671, "↠": 1672, "Ⅳ": 1673, "⊓": 1674, "⊏": 1675, "≣": 1676, "⊊": 1677, "≽": 1678, "⁵": 1679, "⊋": 1680, "₮": 1681, "⊥": 1682, "⋮": 1683, "ꝙ": 1684, "𝙯": 1685, "𝗙": 1686, "ꝿ": 1687, "𝝟": 1688, "ⱻ": 1689, "𝜱": 1690, "Ɫ": 1691, "ng:cognition:attention_1": 1692, "ng:cognition:bias_1": 1693, "ng:cognition:memory_2": 1694, "ng:cognition:metacognition_2": 1695, "ng:cognition:memory_3": 1696, "ng:cognition:attention_3": 1697, "ng:cognition:metacognition_3": 1698, "ng:cognition:qualia_3": 1699, "ng:cognition:attention_4": 1700, "ng:cognition:chunking_4": 1701, "ng:cognition:salience_4": 1702, "ng:cognition:metacognition_4": 1703, "ng:cognition:qualia_4": 1704, "ng:cognition:memory_5": 1705, "ng:cognition:bias_5": 1706, "ng:cognition:metacognition_5": 1707, "ng:cognition:consciousness_5": 1708, "ng:cognition:attention_6": 1709, "ng:cognition:chunking_6": 1710, "ng:cognition:metacognition_6": 1711, "ng:cognition:bias_7": 1712, "ng:cognition:consciousness_7": 1713, "ng:cognition:qualia_7": 1714, "ng:cognition:memory_8": 1715, "ng:cognition:bias_8": 1716, "ng:cognition:chunking_8": 1717, "ng:cognition:chunking_9": 1718, "ng:cognition:memory_10": 1719, "ng:cognition:chunking_10": 1720, "ng:cognition:qualia_10": 1721, "ng:cognition:chunking_11": 1722, "ng:cognition:metacognition_11": 1723, "ng:cognition:memory_12": 1724, "ng:cognition:attention_12": 1725, "ng:cognition:bias_12": 1726, "ng:cognition:attention_13": 1727, "ng:cognition:chunking_13": 1728, "ng:cognition:qualia_13": 1729, "ng:cognition:memory_14": 1730, "ng:cognition:attention_14": 1731, "ng:cognition:chunking_14": 1732, "ng:cognition:salience_14": 1733, "ng:cognition:consciousness_14": 1734, "ng:cognition:memory_15": 1735, "ng:cognition:salience_15": 1736, "ng:cognition:metacognition_15": 1737, "ng:cognition:qualia_15": 1738, "ng:cognition:memory_16": 1739, "ng:cognition:bias_16": 1740, "ng:cognition:chunking_16": 1741, "ng:cognition:salience_16": 1742, "ng:cognition:consciousness_16": 1743, "ng:cognition:memory_17": 1744, "ng:cognition:attention_17": 1745, "ng:cognition:bias_17": 1746, "ng:cognition:chunking_17": 1747, "ng:cognition:metacognition_17": 1748, "ng:cognition:consciousness_17": 1749, "ng:cognition:memory_18": 1750, "ng:cognition:chunking_18": 1751, "ng:cognition:metacognition_18": 1752, "ng:cognition:consciousness_18": 1753, "ng:cognition:memory_19": 1754, "ng:cognition:chunking_19": 1755, "ng:cognition:salience_19": 1756, "ng:cognition:metacognition_19": 1757, "ng:cognition:consciousness_19": 1758, "ng:cognition:chunking_20": 1759, "ng:cognition:metacognition_20": 1760, "ng:cognition:consciousness_20": 1761, "ng:cognition:qualia_20": 1762, "ng:cognition:chunking_21": 1763, "ng:cognition:salience_21": 1764, "ng:cognition:metacognition_21": 1765, "ng:cognition:qualia_21": 1766, "ng:cognition:memory_22": 1767, "ng:cognition:bias_22": 1768, "ng:cognition:salience_22": 1769, "ng:cognition:consciousness_22": 1770, "ng:cognition:memory_24": 1771, "ng:cognition:attention_24": 1772, "ng:cognition:bias_24": 1773, "ng:cognition:chunking_24": 1774, "ng:cognition:salience_24": 1775, "ng:cognition:metacognition_24": 1776, "ng:cognition:consciousness_24": 1777, "ng:cognition:qualia_24": 1778, "ng:cognition:memory_25": 1779, "ng:cognition:attention_25": 1780, "ng:cognition:bias_25": 1781, "ng:cognition:chunking_25": 1782, "ng:cognition:salience_25": 1783, "ng:cognition:metacognition_25": 1784, "ng:cognition:consciousness_25": 1785, "ng:cognition:qualia_25": 1786, "ng:cognition:memory_26": 1787, "ng:cognition:attention_26": 1788, "ng:cognition:bias_26": 1789, "ng:cognition:chunking_26": 1790, "ng:cognition:salience_26": 1791, "ng:cognition:metacognition_26": 1792, "ng:cognition:consciousness_26": 1793, "ng:cognition:qualia_26": 1794, "ng:cognition:memory_27": 1795, "ng:cognition:attention_27": 1796, "ng:cognition:bias_27": 1797, "ng:cognition:chunking_27": 1798, "ng:cognition:salience_27": 1799, "ng:cognition:metacognition_27": 1800, "ng:cognition:consciousness_27": 1801, "ng:cognition:qualia_27": 1802, "ng:cognition:memory_28": 1803, "ng:cognition:attention_28": 1804, "ng:cognition:bias_28": 1805, "ng:cognition:chunking_28": 1806, "ng:cognition:salience_28": 1807, "ng:cognition:metacognition_28": 1808, "ng:cognition:consciousness_28": 1809, "ng:cognition:qualia_28": 1810, "ng:cognition:memory_29": 1811, "ng:cognition:attention_29": 1812, "ng:cognition:bias_29": 1813, "ng:cognition:chunking_29": 1814, "ng:cognition:salience_29": 1815, "ng:cognition:metacognition_29": 1816, "ng:cognition:consciousness_29": 1817, "ng:cognition:qualia_29": 1818, "ng:cognition:memory_30": 1819, "ng:cognition:attention_30": 1820, "ng:cognition:bias_30": 1821, "ng:cognition:chunking_30": 1822, "ng:cognition:salience_30": 1823, "ng:cognition:metacognition_30": 1824, "ng:cognition:consciousness_30": 1825, "ng:cognition:qualia_30": 1826, "ng:cognition:memory_31": 1827, "ng:cognition:attention_31": 1828, "ng:cognition:bias_31": 1829, "ng:cognition:chunking_31": 1830, "ng:cognition:salience_31": 1831, "ng:cognition:metacognition_31": 1832, "ng:cognition:consciousness_31": 1833, "ng:cognition:qualia_31": 1834, "ng:cognition:memory_32": 1835, "ng:cognition:attention_32": 1836, "ng:cognition:bias_32": 1837, "ng:cognition:chunking_32": 1838, "ng:cognition:salience_32": 1839, "ng:cognition:metacognition_32": 1840, "ng:cognition:consciousness_32": 1841, "ng:cognition:qualia_32": 1842, "ng:cognition:memory_33": 1843, "ng:cognition:attention_33": 1844, "ng:cognition:bias_33": 1845, "ng:cognition:chunking_33": 1846, "ng:cognition:salience_33": 1847, "ng:cognition:metacognition_33": 1848, "ng:cognition:consciousness_33": 1849, "ng:cognition:qualia_33": 1850, "ng:cognition:memory_34": 1851, "ng:cognition:attention_34": 1852, "ng:cognition:bias_34": 1853, "ng:cognition:chunking_34": 1854, "ng:cognition:salience_34": 1855, "ng:cognition:metacognition_34": 1856, "ng:cognition:consciousness_34": 1857, "ng:cognition:qualia_34": 1858, "ng:cognition:memory_35": 1859, "ng:cognition:attention_35": 1860, "ng:cognition:bias_35": 1861, "ng:cognition:chunking_35": 1862, "ng:cognition:salience_35": 1863, "ng:cognition:metacognition_35": 1864, "ng:cognition:consciousness_35": 1865, "ng:cognition:qualia_35": 1866, "ng:cognition:memory_36": 1867, "ng:cognition:attention_36": 1868, "ng:cognition:bias_36": 1869, "ng:cognition:chunking_36": 1870, "ng:cognition:salience_36": 1871, "ng:cognition:metacognition_36": 1872, "ng:cognition:consciousness_36": 1873, "ng:cognition:qualia_36": 1874, "ng:cognition:memory_37": 1875, "ng:cognition:attention_37": 1876, "ng:cognition:bias_37": 1877, "ng:cognition:chunking_37": 1878, "ng:cognition:salience_37": 1879, "ng:cognition:metacognition_37": 1880, "ng:cognition:consciousness_37": 1881, "ng:cognition:qualia_37": 1882, "ng:cognition:memory_38": 1883, "ng:cognition:attention_38": 1884, "ng:cognition:bias_38": 1885, "ng:cognition:chunking_38": 1886, "ng:cognition:salience_38": 1887, "ng:cognition:metacognition_38": 1888, "ng:cognition:consciousness_38": 1889, "ng:cognition:qualia_38": 1890, "ng:cognition:memory_39": 1891, "ng:cognition:attention_39": 1892, "ng:cognition:bias_39": 1893, "ng:cognition:chunking_39": 1894, "ng:cognition:salience_39": 1895, "ng:cognition:metacognition_39": 1896, "ng:cognition:consciousness_39": 1897, "ng:cognition:qualia_39": 1898, "ng:cognition:memory_40": 1899, "ng:cognition:attention_40": 1900, "ng:cognition:bias_40": 1901, "ng:cognition:chunking_40": 1902, "ng:cognition:salience_40": 1903, "ng:cognition:metacognition_40": 1904, "ng:cognition:consciousness_40": 1905, "ng:cognition:qualia_40": 1906, "ng:cognition:memory_41": 1907, "ng:cognition:attention_41": 1908, "ng:cognition:bias_41": 1909, "ng:cognition:chunking_41": 1910, "ng:cognition:salience_41": 1911, "ng:cognition:metacognition_41": 1912, "ng:cognition:consciousness_41": 1913, "ng:cognition:qualia_41": 1914, "ng:cognition:memory_42": 1915, "ng:cognition:attention_42": 1916, "ng:cognition:bias_42": 1917, "ng:cognition:chunking_42": 1918, "ng:cognition:salience_42": 1919, "ng:cognition:metacognition_42": 1920, "ng:cognition:consciousness_42": 1921, "ng:cognition:qualia_42": 1922, "ng:cognition:memory_43": 1923, "ng:cognition:attention_43": 1924, "ng:cognition:bias_43": 1925, "ng:cognition:chunking_43": 1926, "ng:cognition:salience_43": 1927, "ng:cognition:metacognition_43": 1928, "ng:cognition:consciousness_43": 1929, "ng:cognition:qualia_43": 1930, "ng:cognition:memory_44": 1931, "ng:cognition:attention_44": 1932, "ng:cognition:bias_44": 1933, "ng:cognition:chunking_44": 1934, "ng:cognition:salience_44": 1935, "ng:cognition:metacognition_44": 1936, "ng:cognition:consciousness_44": 1937, "ng:cognition:qualia_44": 1938, "ng:cognition:memory_45": 1939, "ng:cognition:attention_45": 1940, "ng:cognition:bias_45": 1941, "ng:cognition:chunking_45": 1942, "ng:cognition:salience_45": 1943, "ng:cognition:metacognition_45": 1944, "ng:cognition:consciousness_45": 1945, "ng:cognition:qualia_45": 1946, "ng:cognition:memory_46": 1947, "ng:cognition:attention_46": 1948, "ng:cognition:bias_46": 1949, "ng:cognition:chunking_46": 1950, "ng:cognition:salience_46": 1951, "ng:cognition:metacognition_46": 1952, "ng:cognition:consciousness_46": 1953, "ng:cognition:qualia_46": 1954, "ng:cognition:memory_47": 1955, "ng:cognition:attention_47": 1956, "ng:cognition:bias_47": 1957, "ng:cognition:chunking_47": 1958, "ng:cognition:salience_47": 1959, "ng:cognition:metacognition_47": 1960, "ng:cognition:consciousness_47": 1961, "ng:cognition:qualia_47": 1962, "ng:cognition:memory_48": 1963, "ng:cognition:attention_48": 1964, "ng:cognition:bias_48": 1965, "ng:cognition:chunking_48": 1966, "ng:cognition:salience_48": 1967, "ng:cognition:metacognition_48": 1968, "ng:cognition:consciousness_48": 1969, "ng:cognition:qualia_48": 1970, "ng:cognition:memory_49": 1971, "ng:cognition:attention_49": 1972, "ng:cognition:bias_49": 1973, "ng:cognition:chunking_49": 1974, "ng:cognition:salience_49": 1975, "ng:cognition:metacognition_49": 1976, "ng:cognition:consciousness_49": 1977, "ng:cognition:qualia_49": 1978, "ng:cognition:memory_50": 1979, "ng:cognition:attention_50": 1980, "ng:cognition:bias_50": 1981, "ng:cognition:chunking_50": 1982, "ng:cognition:salience_50": 1983, "ng:cognition:metacognition_50": 1984, "ng:cognition:consciousness_50": 1985, "ng:cognition:qualia_50": 1986, "ng:cognition:memory_51": 1987, "ng:cognition:attention_51": 1988, "ng:cognition:bias_51": 1989, "ng:cognition:chunking_51": 1990, "ng:cognition:salience_51": 1991, "ng:cognition:metacognition_51": 1992, "ng:cognition:consciousness_51": 1993, "ng:cognition:qualia_51": 1994, "ng:cognition:memory_52": 1995, "ng:cognition:attention_52": 1996, "ng:cognition:bias_52": 1997, "ng:cognition:chunking_52": 1998, "ng:cognition:salience_52": 1999, "ng:cognition:metacognition_52": 2000, "ng:cognition:consciousness_52": 2001, "ng:cognition:qualia_52": 2002, "ng:cognition:memory_53": 2003, "ng:cognition:attention_53": 2004, "ng:cognition:bias_53": 2005, "ng:cognition:chunking_53": 2006, "ng:cognition:salience_53": 2007, "ng:cognition:metacognition_53": 2008, "ng:cognition:consciousness_53": 2009, "ng:cognition:qualia_53": 2010, "ng:cognition:memory_54": 2011, "ng:cognition:attention_54": 2012, "ng:cognition:bias_54": 2013, "ng:cognition:chunking_54": 2014, "ng:cognition:salience_54": 2015, "ng:cognition:metacognition_54": 2016, "ng:cognition:consciousness_54": 2017, "ng:cognition:qualia_54": 2018, "ng:cognition:memory_55": 2019, "ng:cognition:attention_55": 2020, "ng:cognition:bias_55": 2021, "ng:cognition:chunking_55": 2022, "ng:cognition:salience_55": 2023, "ng:cognition:metacognition_55": 2024, "ng:cognition:consciousness_55": 2025, "ng:cognition:qualia_55": 2026, "ng:cognition:memory_56": 2027, "ng:cognition:attention_56": 2028, "ng:cognition:bias_56": 2029, "ng:cognition:chunking_56": 2030, "ng:cognition:salience_56": 2031, "ng:cognition:metacognition_56": 2032, "ng:cognition:consciousness_56": 2033, "ng:cognition:qualia_56": 2034, "ng:cognition:memory_57": 2035, "ng:cognition:attention_57": 2036, "ng:cognition:bias_57": 2037, "ng:cognition:chunking_57": 2038, "ng:cognition:salience_57": 2039, "ng:cognition:metacognition_57": 2040, "ng:cognition:consciousness_57": 2041, "ng:cognition:qualia_57": 2042, "ng:cognition:memory_58": 2043, "ng:cognition:attention_58": 2044, "ng:cognition:bias_58": 2045, "ng:cognition:chunking_58": 2046, "ng:cognition:salience_58": 2047, "ng:cognition:metacognition_58": 2048, "ng:cognition:consciousness_58": 2049, "ng:cognition:qualia_58": 2050, "ng:cognition:memory_59": 2051, "ng:cognition:attention_59": 2052, "ng:cognition:bias_59": 2053, "ng:cognition:chunking_59": 2054, "ng:cognition:salience_59": 2055, "ng:cognition:metacognition_59": 2056, "ng:cognition:consciousness_59": 2057, "ng:cognition:attention_60": 2058, "ng:cognition:bias_60": 2059, "ng:cognition:chunking_60": 2060, "ng:cognition:salience_60": 2061, "ng:cognition:metacognition_60": 2062, "ng:cognition:consciousness_60": 2063, "ng:cognition:qualia_60": 2064, "ng:cognition:memory_61": 2065, "ng:cognition:attention_61": 2066, "ng:cognition:bias_61": 2067, "ng:cognition:chunking_61": 2068, "ng:cognition:salience_61": 2069, "ng:cognition:metacognition_61": 2070, "ng:cognition:consciousness_61": 2071, "ng:cognition:qualia_61": 2072, "ng:cognition:memory_62": 2073, "ng:cognition:attention_62": 2074, "ng:cognition:bias_62": 2075, "ng:cognition:chunking_62": 2076, "ng:cognition:salience_62": 2077, "ng:cognition:metacognition_62": 2078, "ng:cognition:consciousness_62": 2079, "ng:cognition:qualia_62": 2080, "ng:cognition:memory_63": 2081, "ng:cognition:attention_63": 2082, "ng:cognition:bias_63": 2083, "ng:cognition:chunking_63": 2084, "ng:cognition:salience_63": 2085, "ng:cognition:metacognition_63": 2086, "ng:cognition:consciousness_63": 2087, "ng:cognition:qualia_63": 2088, "ng:cognition:memory_64": 2089, "ng:cognition:attention_64": 2090, "ng:cognition:bias_64": 2091, "ng:cognition:chunking_64": 2092, "ng:cognition:salience_64": 2093, "ng:cognition:metacognition_64": 2094, "ng:cognition:consciousness_64": 2095, "ng:cognition:qualia_64": 2096, "ng:cognition:memory_65": 2097, "ng:cognition:attention_65": 2098, "ng:cognition:bias_65": 2099, "ng:cognition:chunking_65": 2100, "ng:cognition:salience_65": 2101, "ng:cognition:metacognition_65": 2102, "ng:cognition:consciousness_65": 2103, "ng:cognition:qualia_65": 2104, "ng:cognition:memory_66": 2105, "ng:cognition:attention_66": 2106, "ng:cognition:bias_66": 2107, "ng:cognition:chunking_66": 2108, "ng:cognition:salience_66": 2109, "ng:cognition:metacognition_66": 2110, "ng:cognition:consciousness_66": 2111, "ng:cognition:qualia_66": 2112, "ng:cognition:memory_67": 2113, "ng:cognition:attention_67": 2114, "ng:cognition:bias_67": 2115, "ng:cognition:chunking_67": 2116, "ng:cognition:salience_67": 2117, "ng:cognition:metacognition_67": 2118, "ng:cognition:consciousness_67": 2119, "ng:cognition:qualia_67": 2120, "ng:cognition:memory_68": 2121, "ng:cognition:attention_68": 2122, "ng:cognition:bias_68": 2123, "ng:cognition:chunking_68": 2124, "ng:cognition:salience_68": 2125, "ng:cognition:metacognition_68": 2126, "ng:cognition:consciousness_68": 2127, "ng:cognition:qualia_68": 2128, "ng:cognition:memory_69": 2129, "ng:cognition:attention_69": 2130, "ng:cognition:bias_69": 2131, "ng:cognition:chunking_69": 2132, "ng:cognition:salience_69": 2133, "ng:cognition:metacognition_69": 2134, "ng:cognition:consciousness_69": 2135, "ng:cognition:qualia_69": 2136, "ng:cognition:memory_70": 2137, "ng:cognition:attention_70": 2138, "ng:cognition:bias_70": 2139, "ng:cognition:chunking_70": 2140, "ng:cognition:salience_70": 2141, "ng:cognition:metacognition_70": 2142, "ng:cognition:consciousness_70": 2143, "ng:cognition:qualia_70": 2144, "ng:cognition:memory_71": 2145, "ng:cognition:attention_71": 2146, "ng:cognition:bias_71": 2147, "ng:cognition:chunking_71": 2148, "ng:cognition:salience_71": 2149, "ng:cognition:metacognition_71": 2150, "ng:cognition:consciousness_71": 2151, "ng:cognition:qualia_71": 2152, "ng:cognition:memory_72": 2153, "ng:cognition:attention_72": 2154, "ng:cognition:bias_72": 2155, "ng:cognition:chunking_72": 2156, "ng:cognition:salience_72": 2157, "ng:cognition:metacognition_72": 2158, "ng:cognition:consciousness_72": 2159, "ng:cognition:qualia_72": 2160, "ng:cognition:memory_73": 2161, "ng:cognition:attention_73": 2162, "ng:cognition:bias_73": 2163, "ng:cognition:chunking_73": 2164, "ng:cognition:salience_73": 2165, "ng:cognition:metacognition_73": 2166, "ng:cognition:consciousness_73": 2167, "ng:cognition:qualia_73": 2168, "ng:cognition:memory_74": 2169, "ng:cognition:attention_74": 2170, "ng:cognition:bias_74": 2171, "ng:cognition:chunking_74": 2172, "ng:cognition:salience_74": 2173, "ng:cognition:metacognition_74": 2174, "ng:cognition:consciousness_74": 2175, "ng:cognition:qualia_74": 2176, "ng:cognition:memory_75": 2177, "ng:cognition:attention_75": 2178, "ng:cognition:bias_75": 2179, "ng:cognition:chunking_75": 2180, "ng:cognition:salience_75": 2181, "ng:cognition:metacognition_75": 2182, "ng:cognition:consciousness_75": 2183, "ng:cognition:qualia_75": 2184, "ng:cognition:memory_76": 2185, "ng:cognition:attention_76": 2186, "ng:cognition:bias_76": 2187, "ng:cognition:chunking_76": 2188, "ng:cognition:salience_76": 2189, "ng:cognition:metacognition_76": 2190, "ng:cognition:consciousness_76": 2191, "ng:cognition:qualia_76": 2192, "ng:cognition:memory_77": 2193, "ng:cognition:attention_77": 2194, "ng:cognition:bias_77": 2195, "ng:cognition:chunking_77": 2196, "ng:cognition:salience_77": 2197, "ng:cognition:metacognition_77": 2198, "ng:cognition:consciousness_77": 2199, "ng:cognition:qualia_77": 2200, "ng:cognition:memory_78": 2201, "ng:cognition:attention_78": 2202, "ng:cognition:bias_78": 2203, "ng:cognition:chunking_78": 2204, "ng:cognition:salience_78": 2205, "ng:cognition:metacognition_78": 2206, "ng:cognition:consciousness_78": 2207, "ng:cognition:qualia_78": 2208, "ng:cognition:memory_79": 2209, "ng:cognition:attention_79": 2210, "ng:cognition:bias_79": 2211, "ng:cognition:chunking_79": 2212, "ng:cognition:salience_79": 2213, "ng:cognition:metacognition_79": 2214, "ng:cognition:consciousness_79": 2215, "ng:cognition:qualia_79": 2216, "ng:cognition:memory_80": 2217, "ng:cognition:attention_80": 2218, "ng:cognition:bias_80": 2219, "ng:cognition:chunking_80": 2220, "ng:cognition:salience_80": 2221, "ng:cognition:metacognition_80": 2222, "ng:cognition:consciousness_80": 2223, "ng:cognition:qualia_80": 2224, "ng:cognition:memory_81": 2225, "ng:cognition:attention_81": 2226, "ng:cognition:bias_81": 2227, "ng:cognition:chunking_81": 2228, "ng:cognition:salience_81": 2229, "ng:cognition:metacognition_81": 2230, "ng:cognition:consciousness_81": 2231, "ng:cognition:qualia_81": 2232, "ng:cognition:memory_82": 2233, "ng:cognition:attention_82": 2234, "ng:cognition:bias_82": 2235, "ng:cognition:chunking_82": 2236, "ng:cognition:salience_82": 2237, "ng:cognition:metacognition_82": 2238, "ng:cognition:consciousness_82": 2239, "ng:cognition:qualia_82": 2240, "ng:cognition:memory_83": 2241, "ng:cognition:attention_83": 2242, "ng:cognition:bias_83": 2243, "ng:cognition:chunking_83": 2244, "ng:cognition:salience_83": 2245, "ng:cognition:metacognition_83": 2246, "ng:cognition:consciousness_83": 2247, "ng:cognition:qualia_83": 2248, "ng:cognition:memory_84": 2249, "ng:cognition:attention_84": 2250, "ng:cognition:bias_84": 2251, "ng:cognition:chunking_84": 2252, "ng:cognition:salience_84": 2253, "ng:cognition:metacognition_84": 2254, "ng:cognition:consciousness_84": 2255, "ng:cognition:qualia_84": 2256, "ng:cognition:memory_85": 2257, "ng:cognition:attention_85": 2258, "ng:cognition:bias_85": 2259, "ng:cognition:chunking_85": 2260, "ng:cognition:salience_85": 2261, "ng:cognition:metacognition_85": 2262, "ng:cognition:consciousness_85": 2263, "ng:cognition:qualia_85": 2264, "ng:cognition:memory_86": 2265, "ng:cognition:attention_86": 2266, "ng:cognition:bias_86": 2267, "ng:cognition:chunking_86": 2268, "ng:cognition:salience_86": 2269, "ng:cognition:metacognition_86": 2270, "ng:cognition:consciousness_86": 2271, "ng:cognition:qualia_86": 2272, "ng:cognition:memory_87": 2273, "ng:cognition:attention_87": 2274, "ng:cognition:bias_87": 2275, "ng:cognition:chunking_87": 2276, "ng:cognition:salience_87": 2277, "ng:cognition:metacognition_87": 2278, "ng:cognition:consciousness_87": 2279, "ng:cognition:qualia_87": 2280, "𝑶": 2281, "𝟜": 2282, "╜": 2283, "⮽": 2284, "⯁": 2285, "⯀": 2286, "⮿": 2287, "⮾": 2288, "⪼": 2289, "➕": 2290, "⎼": 2291, "⩧": 2292, "⮲": 2293, "⪽": 2294, "⪿": 2295, "⫀": 2296, "➼": 2297, "⎿": 2298, "⏀": 2299, "⏁": 2300, "⫁": 2301, "⧨": 2302, "➜": 2303, "⎾": 2304, "➫": 2305, "➾": 2306, "⏂": 2307, "⏄": 2308, "⫂": 2309, "⫃": 2310, "ℿ": 2311, "↞": 2312, "ℛ": 2313, "⇂": 2314, "⇸": 2315, "⯥": 2316, "⎊": 2317, "⎄": 2318, "⎃": 2319, "⪆": 2320, "⎈": 2321, "⭾": 2322, "⪉": 2323, "⪅": 2324, "⫝": 2325, "⊄": 2326, "⫨": 2327, "⊂": 2328, "⧾": 2329, "⯺": 2330, "⥾": 2331, "⍿": 2332, "⯎": 2333, "❾": 2334, "➃": 2335, "⪃": 2336, "⎅": 2337, "➄": 2338, "⩨": 2339, "⮄": 2340, "❿": 2341, "⩾": 2342, "⎉": 2343, "➅": 2344, "➆": 2345, "➇": 2346, "➈": 2347, "➉": 2348, "➊": 2349, "⪇": 2350, "⪈": 2351, "⪊": 2352, "⫯": 2353, "⭷": 2354, "⮅": 2355, "⮆": 2356, "⮇": 2357, "⮡": 2358, "➀": 2359, "⎂": 2360, "➂": 2361, "⮁": 2362, "⮂": 2363, "➁": 2364, "✈": 2365, "⨿": 2366, "⌶": 2367, "◄": 2368, "◀": 2369, "⬥": 2370, "∙": 2371, "⦿": 2372, "◌": 2373, "⌄": 2374, "≙": 2375, "⌂": 2376, "⨛": 2377, "◘": 2378, "⦅": 2379, "⬅": 2380, "⩒": 2381, "◡": 2382, "✎": 2383, "⨁": 2384, "⌆": 2385, "⨧": 2386, "⦰": 2387, "⌐": 2388, "⌉": 2389, "❍": 2390, "✰": 2391, "∍": 2392, "⬋": 2393, "✨": 2394, "∢": 2395, "⊃": 2396, "⌎": 2397, "⨇": 2398, "⦽": 2399, "⌃": 2400, "◦": 2401, "❀": 2402, "✅": 2403, "▱": 2404, "⦇": 2405, "⨡": 2406, "⤁": 2407, "⩉": 2408, "⦳": 2409, "◕": 2410, "◭": 2411, "⬎": 2412, "⤅": 2413, "◛": 2414, "◳": 2415, "⥖": 2416, "⦕": 2417, "⥈": 2418, "◔": 2419, "⤼": 2420, "◪": 2421, "◃": 2422, "⤮": 2423, "⨱": 2424, "⥃": 2425, "⬼": 2426, "⋳": 2427, "⊉": 2428, "🝛": 2429, "🞷": 2430, "🞆": 2431, "🞉": 2432, "🟊": 2433, "🟉": 2434, "🞯": 2435, "🞵": 2436, "🟋": 2437, "🞶": 2438, "🟍": 2439, "🟂": 2440, "🞈": 2441, "🝚": 2442, "🝜": 2443, "🝝": 2444, "🞊": 2445, "🟀": 2446, "🟁": 2447, "🟇": 2448, "🟏": 2449, "🟐": 2450, "🟑": 2451, "🟓": 2452, "₡": 2453, "℃": 2454, "‗": 2455, "⏸": 2456, "⋞": 2457, " ": 2458, "↚": 2459, "₤": 2460, "⁎": 2461, "⋯": 2462, "≪": 2463, "⊎": 2464, "⋃": 2465, "≱": 2466, "⊅": 2467, "≯": 2468, "⋢": 2469, "⑵": 2470, "⒃": 2471, "⅌": 2472, "⊰": 2473, "⁏": 2474, "∽": 2475, "Ⅻ": 2476, "⊒": 2477, "⋥": 2478, "⋆": 2479, "≿": 2480, "∃": 2481, "ⅎ": 2482, "℣": 2483, "ng:distributed:replication": 2484, "ng:distributed:consistency": 2485, "ng:distributed:availability": 2486, "ng:distributed:replication_1": 2487, "ng:distributed:gossip_1": 2488, "ng:distributed:consensus_2": 2489, "ng:distributed:replication_2": 2490, "ng:distributed:consistency_2": 2491, "ng:distributed:partition_3": 2492, "ng:distributed:coordination_3": 2493, "ng:distributed:consensus_4": 2494, "ng:distributed:replication_4": 2495, "ng:distributed:consistency_4": 2496, "ng:distributed:availability_4": 2497, "ng:distributed:gossip_4": 2498, "ng:distributed:raft_4": 2499, "ng:distributed:consensus_5": 2500, "ng:distributed:replication_5": 2501, "ng:distributed:consistency_5": 2502, "ng:distributed:availability_5": 2503, "ng:distributed:gossip_5": 2504, "ng:distributed:consensus_6": 2505, "ng:distributed:gossip_6": 2506, "ng:distributed:partition_7": 2507, "ng:distributed:partition_8": 2508, "ng:distributed:consistency_8": 2509, "ng:distributed:raft_8": 2510, "ng:distributed:consensus_9": 2511, "ng:distributed:replication_9": 2512, "ng:distributed:consistency_9": 2513, "ng:distributed:raft_9": 2514, "ng:distributed:partition_10": 2515, "ng:distributed:replication_10": 2516, "ng:distributed:consensus_11": 2517, "ng:distributed:replication_11": 2518, "ng:distributed:consistency_11": 2519, "ng:distributed:availability_11": 2520, "ng:distributed:coordination_11": 2521, "ng:distributed:partition_12": 2522, "ng:distributed:consistency_12": 2523, "ng:distributed:coordination_12": 2524, "ng:distributed:raft_12": 2525, "ng:distributed:partition_13": 2526, "ng:distributed:replication_13": 2527, "ng:distributed:consistency_13": 2528, "ng:distributed:availability_13": 2529, "ng:distributed:coordination_13": 2530, "ng:distributed:gossip_13": 2531, "ng:distributed:partition_14": 2532, "ng:distributed:replication_14": 2533, "ng:distributed:consistency_14": 2534, "ng:distributed:availability_14": 2535, "ng:distributed:coordination_14": 2536, "ng:distributed:gossip_14": 2537, "ng:distributed:consensus_15": 2538, "ng:distributed:partition_15": 2539, "ng:distributed:replication_15": 2540, "ng:distributed:consistency_15": 2541, "ng:distributed:availability_15": 2542, "ng:distributed:raft_15": 2543, "ng:distributed:replication_16": 2544, "ng:distributed:consistency_16": 2545, "ng:distributed:coordination_16": 2546, "ng:distributed:raft_16": 2547, "ng:distributed:consensus_17": 2548, "ng:distributed:replication_17": 2549, "ng:distributed:consistency_17": 2550, "ng:distributed:availability_17": 2551, "ng:distributed:coordination_17": 2552, "ng:distributed:gossip_17": 2553, "ng:distributed:raft_17": 2554, "ng:distributed:replication_18": 2555, "ng:distributed:gossip_18": 2556, "ng:distributed:raft_18": 2557, "ng:distributed:replication_19": 2558, "ng:distributed:availability_19": 2559, "ng:distributed:coordination_19": 2560, "ng:distributed:gossip_19": 2561, "ng:distributed:consensus_20": 2562, "ng:distributed:partition_20": 2563, "ng:distributed:coordination_20": 2564, "ng:distributed:raft_20": 2565, "ng:distributed:consensus_21": 2566, "ng:distributed:replication_21": 2567, "ng:distributed:consistency_21": 2568, "ng:distributed:availability_21": 2569, "ng:distributed:gossip_21": 2570, "ng:distributed:consensus_24": 2571, "ng:distributed:partition_24": 2572, "ng:distributed:replication_24": 2573, "ng:distributed:consistency_24": 2574, "ng:distributed:availability_24": 2575, "ng:distributed:coordination_24": 2576, "ng:distributed:gossip_24": 2577, "ng:distributed:raft_24": 2578, "ng:distributed:consensus_25": 2579, "ng:distributed:partition_25": 2580, "ng:distributed:replication_25": 2581, "ng:distributed:consistency_25": 2582, "ng:distributed:availability_25": 2583, "ng:distributed:coordination_25": 2584, "ng:distributed:gossip_25": 2585, "ng:distributed:raft_25": 2586, "ng:distributed:consensus_26": 2587, "ng:distributed:partition_26": 2588, "ng:distributed:replication_26": 2589, "ng:distributed:consistency_26": 2590, "ng:distributed:availability_26": 2591, "ng:distributed:coordination_26": 2592, "ng:distributed:gossip_26": 2593, "ng:distributed:raft_26": 2594, "ng:distributed:consensus_27": 2595, "ng:distributed:partition_27": 2596, "ng:distributed:replication_27": 2597, "ng:distributed:consistency_27": 2598, "ng:distributed:availability_27": 2599, "ng:distributed:coordination_27": 2600, "ng:distributed:gossip_27": 2601, "ng:distributed:raft_27": 2602, "ng:distributed:consensus_28": 2603, "ng:distributed:partition_28": 2604, "ng:distributed:replication_28": 2605, "ng:distributed:consistency_28": 2606, "ng:distributed:availability_28": 2607, "ng:distributed:coordination_28": 2608, "ng:distributed:gossip_28": 2609, "ng:distributed:raft_28": 2610, "ng:distributed:consensus_29": 2611, "ng:distributed:partition_29": 2612, "ng:distributed:replication_29": 2613, "ng:distributed:consistency_29": 2614, "ng:distributed:availability_29": 2615, "ng:distributed:coordination_29": 2616, "ng:distributed:gossip_29": 2617, "ng:distributed:raft_29": 2618, "ng:distributed:consensus_30": 2619, "ng:distributed:partition_30": 2620, "ng:distributed:replication_30": 2621, "ng:distributed:consistency_30": 2622, "ng:distributed:availability_30": 2623, "ng:distributed:coordination_30": 2624, "ng:distributed:gossip_30": 2625, "ng:distributed:raft_30": 2626, "ng:distributed:consensus_31": 2627, "ng:distributed:partition_31": 2628, "ng:distributed:replication_31": 2629, "ng:distributed:availability_31": 2630, "ng:distributed:coordination_31": 2631, "ng:distributed:gossip_31": 2632, "ng:distributed:raft_31": 2633, "ng:distributed:consensus_32": 2634, "ng:distributed:partition_32": 2635, "ng:distributed:replication_32": 2636, "ng:distributed:consistency_32": 2637, "ng:distributed:availability_32": 2638, "ng:distributed:coordination_32": 2639, "ng:distributed:gossip_32": 2640, "ng:distributed:raft_32": 2641, "ng:distributed:consensus_33": 2642, "ng:distributed:partition_33": 2643, "ng:distributed:replication_33": 2644, "ng:distributed:consistency_33": 2645, "ng:distributed:availability_33": 2646, "ng:distributed:coordination_33": 2647, "ng:distributed:gossip_33": 2648, "ng:distributed:raft_33": 2649, "ng:distributed:consensus_34": 2650, "ng:distributed:partition_34": 2651, "ng:distributed:replication_34": 2652, "ng:distributed:consistency_34": 2653, "ng:distributed:availability_34": 2654, "ng:distributed:coordination_34": 2655, "ng:distributed:gossip_34": 2656, "ng:distributed:raft_34": 2657, "ng:distributed:consensus_35": 2658, "ng:distributed:partition_35": 2659, "ng:distributed:replication_35": 2660, "ng:distributed:consistency_35": 2661, "ng:distributed:availability_35": 2662, "ng:distributed:coordination_35": 2663, "ng:distributed:gossip_35": 2664, "ng:distributed:raft_35": 2665, "ng:distributed:consensus_36": 2666, "ng:distributed:partition_36": 2667, "ng:distributed:replication_36": 2668, "ng:distributed:consistency_36": 2669, "ng:distributed:availability_36": 2670, "ng:distributed:coordination_36": 2671, "ng:distributed:gossip_36": 2672, "ng:distributed:raft_36": 2673, "ng:distributed:consensus_37": 2674, "ng:distributed:partition_37": 2675, "ng:distributed:replication_37": 2676, "ng:distributed:availability_37": 2677, "ng:distributed:coordination_37": 2678, "ng:distributed:gossip_37": 2679, "ng:distributed:raft_37": 2680, "ng:distributed:consensus_38": 2681, "ng:distributed:partition_38": 2682, "ng:distributed:replication_38": 2683, "ng:distributed:consistency_38": 2684, "ng:distributed:availability_38": 2685, "ng:distributed:coordination_38": 2686, "ng:distributed:gossip_38": 2687, "ng:distributed:raft_38": 2688, "ng:distributed:consensus_39": 2689, "ng:distributed:partition_39": 2690, "ng:distributed:replication_39": 2691, "ng:distributed:consistency_39": 2692, "ng:distributed:availability_39": 2693, "ng:distributed:coordination_39": 2694, "ng:distributed:gossip_39": 2695, "ng:distributed:raft_39": 2696, "ng:distributed:consensus_40": 2697, "ng:distributed:partition_40": 2698, "ng:distributed:replication_40": 2699, "ng:distributed:consistency_40": 2700, "ng:distributed:availability_40": 2701, "ng:distributed:coordination_40": 2702, "ng:distributed:gossip_40": 2703, "ng:distributed:raft_40": 2704, "ng:distributed:consensus_41": 2705, "ng:distributed:partition_41": 2706, "ng:distributed:replication_41": 2707, "ng:distributed:consistency_41": 2708, "ng:distributed:availability_41": 2709, "ng:distributed:coordination_41": 2710, "ng:distributed:gossip_41": 2711, "ng:distributed:consensus_42": 2712, "ng:distributed:partition_42": 2713, "ng:distributed:replication_42": 2714, "ng:distributed:consistency_42": 2715, "ng:distributed:coordination_42": 2716, "ng:distributed:gossip_42": 2717, "ng:distributed:raft_42": 2718, "ng:distributed:consensus_43": 2719, "ng:distributed:partition_43": 2720, "ng:distributed:replication_43": 2721, "ng:distributed:consistency_43": 2722, "ng:distributed:availability_43": 2723, "ng:distributed:coordination_43": 2724, "ng:distributed:gossip_43": 2725, "ng:distributed:raft_43": 2726, "ng:distributed:consensus_44": 2727, "ng:distributed:partition_44": 2728, "ng:distributed:replication_44": 2729, "ng:distributed:consistency_44": 2730, "ng:distributed:availability_44": 2731, "ng:distributed:coordination_44": 2732, "ng:distributed:gossip_44": 2733, "ng:distributed:raft_44": 2734, "ng:distributed:consensus_45": 2735, "ng:distributed:partition_45": 2736, "ng:distributed:replication_45": 2737, "ng:distributed:consistency_45": 2738, "ng:distributed:availability_45": 2739, "ng:distributed:coordination_45": 2740, "ng:distributed:raft_45": 2741, "ng:distributed:consensus_46": 2742, "ng:distributed:partition_46": 2743, "ng:distributed:replication_46": 2744, "ng:distributed:availability_46": 2745, "ng:distributed:coordination_46": 2746, "ng:distributed:gossip_46": 2747, "ng:distributed:raft_46": 2748, "ng:distributed:consensus_47": 2749, "ng:distributed:partition_47": 2750, "ng:distributed:replication_47": 2751, "ng:distributed:consistency_47": 2752, "ng:distributed:availability_47": 2753, "ng:distributed:coordination_47": 2754, "ng:distributed:gossip_47": 2755, "ng:distributed:raft_47": 2756, "ng:distributed:consensus_48": 2757, "ng:distributed:partition_48": 2758, "ng:distributed:replication_48": 2759, "ng:distributed:consistency_48": 2760, "ng:distributed:availability_48": 2761, "ng:distributed:coordination_48": 2762, "ng:distributed:gossip_48": 2763, "ng:distributed:raft_48": 2764, "ng:distributed:consensus_49": 2765, "ng:distributed:partition_49": 2766, "ng:distributed:replication_49": 2767, "ng:distributed:consistency_49": 2768, "ng:distributed:availability_49": 2769, "ng:distributed:coordination_49": 2770, "ng:distributed:gossip_49": 2771, "ng:distributed:raft_49": 2772, "ng:distributed:consensus_50": 2773, "ng:distributed:partition_50": 2774, "ng:distributed:replication_50": 2775, "ng:distributed:consistency_50": 2776, "ng:distributed:availability_50": 2777, "ng:distributed:coordination_50": 2778, "ng:distributed:gossip_50": 2779, "ng:distributed:raft_50": 2780, "ng:distributed:consensus_51": 2781, "ng:distributed:partition_51": 2782, "ng:distributed:replication_51": 2783, "ng:distributed:consistency_51": 2784, "ng:distributed:availability_51": 2785, "ng:distributed:coordination_51": 2786, "ng:distributed:gossip_51": 2787, "ng:distributed:raft_51": 2788, "ng:distributed:consensus_52": 2789, "ng:distributed:partition_52": 2790, "ng:distributed:replication_52": 2791, "ng:distributed:consistency_52": 2792, "ng:distributed:availability_52": 2793, "ng:distributed:coordination_52": 2794, "ng:distributed:gossip_52": 2795, "ng:distributed:raft_52": 2796, "ng:distributed:consensus_53": 2797, "ng:distributed:partition_53": 2798, "ng:distributed:replication_53": 2799, "ng:distributed:consistency_53": 2800, "ng:distributed:availability_53": 2801, "ng:distributed:coordination_53": 2802, "ng:distributed:gossip_53": 2803, "ng:distributed:raft_53": 2804, "ng:distributed:consensus_54": 2805, "ng:distributed:partition_54": 2806, "ng:distributed:replication_54": 2807, "ng:distributed:consistency_54": 2808, "ng:distributed:availability_54": 2809, "ng:distributed:coordination_54": 2810, "ng:distributed:gossip_54": 2811, "ng:distributed:raft_54": 2812, "ng:distributed:consensus_55": 2813, "ng:distributed:partition_55": 2814, "ng:distributed:replication_55": 2815, "ng:distributed:consistency_55": 2816, "ng:distributed:availability_55": 2817, "ng:distributed:coordination_55": 2818, "ng:distributed:gossip_55": 2819, "ng:distributed:raft_55": 2820, "ng:distributed:consensus_56": 2821, "ng:distributed:partition_56": 2822, "ng:distributed:replication_56": 2823, "ng:distributed:consistency_56": 2824, "ng:distributed:availability_56": 2825, "ng:distributed:coordination_56": 2826, "ng:distributed:gossip_56": 2827, "ng:distributed:raft_56": 2828, "ng:distributed:consensus_57": 2829, "ng:distributed:partition_57": 2830, "ng:distributed:replication_57": 2831, "ng:distributed:consistency_57": 2832, "ng:distributed:availability_57": 2833, "ng:distributed:coordination_57": 2834, "ng:distributed:gossip_57": 2835, "ng:distributed:raft_57": 2836, "ng:distributed:consensus_58": 2837, "ng:distributed:partition_58": 2838, "ng:distributed:replication_58": 2839, "ng:distributed:consistency_58": 2840, "ng:distributed:availability_58": 2841, "ng:distributed:coordination_58": 2842, "ng:distributed:gossip_58": 2843, "ng:distributed:raft_58": 2844, "ng:distributed:consensus_59": 2845, "ng:distributed:partition_59": 2846, "ng:distributed:replication_59": 2847, "ng:distributed:consistency_59": 2848, "ng:distributed:availability_59": 2849, "ng:distributed:coordination_59": 2850, "ng:distributed:gossip_59": 2851, "ng:distributed:raft_59": 2852, "ng:distributed:consensus_60": 2853, "ng:distributed:partition_60": 2854, "ng:distributed:replication_60": 2855, "ng:distributed:consistency_60": 2856, "ng:distributed:availability_60": 2857, "ng:distributed:coordination_60": 2858, "ng:distributed:gossip_60": 2859, "ng:distributed:raft_60": 2860, "ng:distributed:consensus_61": 2861, "ng:distributed:partition_61": 2862, "ng:distributed:replication_61": 2863, "ng:distributed:consistency_61": 2864, "ng:distributed:availability_61": 2865, "ng:distributed:coordination_61": 2866, "ng:distributed:gossip_61": 2867, "ng:distributed:raft_61": 2868, "ng:distributed:consensus_62": 2869, "ng:distributed:partition_62": 2870, "ng:distributed:replication_62": 2871, "ng:distributed:consistency_62": 2872, "ng:distributed:availability_62": 2873, "ng:distributed:coordination_62": 2874, "ng:distributed:gossip_62": 2875, "ng:distributed:raft_62": 2876, "ng:distributed:consensus_63": 2877, "ng:distributed:partition_63": 2878, "ng:distributed:replication_63": 2879, "ng:distributed:consistency_63": 2880, "ng:distributed:availability_63": 2881, "ng:distributed:coordination_63": 2882, "ng:distributed:gossip_63": 2883, "ng:distributed:raft_63": 2884, "ng:distributed:consensus_64": 2885, "ng:distributed:replication_64": 2886, "ng:distributed:consistency_64": 2887, "ng:distributed:availability_64": 2888, "ng:distributed:coordination_64": 2889, "ng:distributed:gossip_64": 2890, "ng:distributed:raft_64": 2891, "ng:distributed:consensus_65": 2892, "ng:distributed:partition_65": 2893, "ng:distributed:replication_65": 2894, "ng:distributed:consistency_65": 2895, "ng:distributed:availability_65": 2896, "ng:distributed:gossip_65": 2897, "ng:distributed:raft_65": 2898, "ng:distributed:consensus_66": 2899, "ng:distributed:partition_66": 2900, "ng:distributed:replication_66": 2901, "ng:distributed:consistency_66": 2902, "ng:distributed:availability_66": 2903, "ng:distributed:coordination_66": 2904, "ng:distributed:gossip_66": 2905, "ng:distributed:raft_66": 2906, "ng:distributed:consensus_67": 2907, "ng:distributed:partition_67": 2908, "ng:distributed:replication_67": 2909, "ng:distributed:consistency_67": 2910, "ng:distributed:availability_67": 2911, "ng:distributed:coordination_67": 2912, "ng:distributed:gossip_67": 2913, "ng:distributed:raft_67": 2914, "ng:distributed:consensus_68": 2915, "ng:distributed:partition_68": 2916, "ng:distributed:replication_68": 2917, "ng:distributed:consistency_68": 2918, "ng:distributed:availability_68": 2919, "ng:distributed:coordination_68": 2920, "ng:distributed:gossip_68": 2921, "ng:distributed:raft_68": 2922, "ng:distributed:consensus_69": 2923, "ng:distributed:partition_69": 2924, "ng:distributed:replication_69": 2925, "ng:distributed:consistency_69": 2926, "ng:distributed:availability_69": 2927, "ng:distributed:coordination_69": 2928, "ng:distributed:gossip_69": 2929, "ng:distributed:raft_69": 2930, "ng:distributed:consensus_70": 2931, "ng:distributed:partition_70": 2932, "ng:distributed:replication_70": 2933, "ng:distributed:consistency_70": 2934, "ng:distributed:availability_70": 2935, "ng:distributed:coordination_70": 2936, "ng:distributed:gossip_70": 2937, "ng:distributed:raft_70": 2938, "ng:distributed:consensus_71": 2939, "ng:distributed:partition_71": 2940, "ng:distributed:replication_71": 2941, "ng:distributed:consistency_71": 2942, "ng:distributed:availability_71": 2943, "ng:distributed:coordination_71": 2944, "ng:distributed:gossip_71": 2945, "ng:distributed:raft_71": 2946, "ng:distributed:consensus_72": 2947, "ng:distributed:partition_72": 2948, "ng:distributed:replication_72": 2949, "ng:distributed:consistency_72": 2950, "ng:distributed:availability_72": 2951, "ng:distributed:coordination_72": 2952, "ng:distributed:gossip_72": 2953, "ng:distributed:raft_72": 2954, "ng:distributed:consensus_73": 2955, "ng:distributed:partition_73": 2956, "ng:distributed:replication_73": 2957, "ng:distributed:consistency_73": 2958, "ng:distributed:availability_73": 2959, "ng:distributed:coordination_73": 2960, "ng:distributed:gossip_73": 2961, "ng:distributed:raft_73": 2962, "ng:distributed:consensus_74": 2963, "ng:distributed:partition_74": 2964, "ng:distributed:replication_74": 2965, "ng:distributed:consistency_74": 2966, "ng:distributed:availability_74": 2967, "ng:distributed:coordination_74": 2968, "ng:distributed:gossip_74": 2969, "ng:distributed:raft_74": 2970, "ng:distributed:consensus_75": 2971, "ng:distributed:partition_75": 2972, "ng:distributed:replication_75": 2973, "ng:distributed:consistency_75": 2974, "ng:distributed:availability_75": 2975, "ng:distributed:coordination_75": 2976, "ng:distributed:gossip_75": 2977, "ng:distributed:raft_75": 2978, "ng:distributed:consensus_76": 2979, "ng:distributed:partition_76": 2980, "ng:distributed:replication_76": 2981, "ng:distributed:consistency_76": 2982, "ng:distributed:availability_76": 2983, "ng:distributed:coordination_76": 2984, "ng:distributed:gossip_76": 2985, "ng:distributed:raft_76": 2986, "ng:distributed:consensus_77": 2987, "ng:distributed:partition_77": 2988, "ng:distributed:replication_77": 2989, "ng:distributed:consistency_77": 2990, "ng:distributed:availability_77": 2991, "ng:distributed:coordination_77": 2992, "ng:distributed:gossip_77": 2993, "ng:distributed:raft_77": 2994, "ng:distributed:consensus_78": 2995, "ng:distributed:partition_78": 2996, "ng:distributed:replication_78": 2997, "ng:distributed:availability_78": 2998, "ng:distributed:coordination_78": 2999, "ng:distributed:gossip_78": 3000, "ng:distributed:raft_78": 3001, "ng:distributed:consensus_79": 3002, "ng:distributed:partition_79": 3003, "ng:distributed:replication_79": 3004, "ng:distributed:consistency_79": 3005, "ng:distributed:availability_79": 3006, "ng:distributed:coordination_79": 3007, "ng:distributed:gossip_79": 3008, "ng:distributed:raft_79": 3009, "ng:distributed:consensus_80": 3010, "ng:distributed:partition_80": 3011, "ng:distributed:replication_80": 3012, "ng:distributed:consistency_80": 3013, "ng:distributed:availability_80": 3014, "ng:distributed:coordination_80": 3015, "ng:distributed:gossip_80": 3016, "ng:distributed:raft_80": 3017, "ng:distributed:consensus_81": 3018, "ng:distributed:partition_81": 3019, "ng:distributed:replication_81": 3020, "ng:distributed:consistency_81": 3021, "ng:distributed:availability_81": 3022, "ng:distributed:coordination_81": 3023, "ng:distributed:gossip_81": 3024, "ng:distributed:raft_81": 3025, "ng:distributed:consensus_82": 3026, "ng:distributed:partition_82": 3027, "ng:distributed:replication_82": 3028, "ng:distributed:consistency_82": 3029, "ng:distributed:availability_82": 3030, "ng:distributed:coordination_82": 3031, "ng:distributed:gossip_82": 3032, "ng:distributed:raft_82": 3033, "ng:distributed:consensus_83": 3034, "ng:distributed:partition_83": 3035, "ng:distributed:replication_83": 3036, "ng:distributed:consistency_83": 3037, "ng:distributed:availability_83": 3038, "ng:distributed:coordination_83": 3039, "ng:distributed:gossip_83": 3040, "ng:distributed:raft_83": 3041, "ng:distributed:consensus_84": 3042, "ng:distributed:partition_84": 3043, "ng:distributed:replication_84": 3044, "ng:distributed:consistency_84": 3045, "ng:distributed:availability_84": 3046, "ng:distributed:coordination_84": 3047, "ng:distributed:gossip_84": 3048, "ng:distributed:raft_84": 3049, "ng:distributed:consensus_85": 3050, "ng:distributed:partition_85": 3051, "ng:distributed:replication_85": 3052, "ng:distributed:consistency_85": 3053, "ng:distributed:availability_85": 3054, "ng:distributed:coordination_85": 3055, "ng:distributed:gossip_85": 3056, "ng:distributed:raft_85": 3057, "ng:distributed:consensus_86": 3058, "ng:distributed:partition_86": 3059, "ng:distributed:replication_86": 3060, "ng:distributed:consistency_86": 3061, "ng:distributed:availability_86": 3062, "ng:distributed:coordination_86": 3063, "ng:distributed:gossip_86": 3064, "ng:distributed:raft_86": 3065, "❉": 3066, "◾": 3067, "⭑": 3068, "⧒": 3069, "⦷": 3070, "⦾": 3071, "✣": 3072, "⪧": 3073, "⭘": 3074, "⭕": 3075, "✮": 3076, "⭖": 3077, "⨏": 3078, "⦑": 3079, "⬄": 3080, "⤎": 3081, "∧": 3082, "∨": 3083, "✃": 3084, "❙": 3085, "⬁": 3086, "⦧": 3087, "⫚": 3088, "⨤": 3089, "⦥": 3090, "⤚": 3091, "⮕": 3092, "⤜": 3093, "⧴": 3094, "⩗": 3095, "⪾": 3096, "✻": 3097, "⯊": 3098, "⏜": 3099, "✹": 3100, "⩖": 3101, "⩂": 3102, "✁": 3103, "⧖": 3104, "⥎": 3105, "⥬": 3106, "◈": 3107, "❂": 3108, "⤬": 3109, "⦨": 3110, "⩈": 3111, "⩞": 3112, "⫵": 3113, "⬕": 3114, "⬺": 3115, "⭚": 3116, "⮢": 3117, "⍓": 3118, "⧎": 3119, "⤇": 3120, "⩜": 3121, "⭣": 3122, "⤆": 3123, "⭢": 3124, "⦏": 3125, "⤫": 3126, "⍗": 3127, "⤠": 3128, "⤯": 3129, "⭛": 3130, "⍔": 3131, "❕": 3132, "⥓": 3133, "⮊": 3134, "🜁": 3135, "🝪": 3136, "🝅": 3137, "🝗": 3138, "🝂": 3139, "🝃": 3140, "🝙": 3141, "🝠": 3142, "🜃": 3143, "🜂": 3144, "🝉": 3145, "🝋": 3146, "🜲": 3147, "🝭": 3148, "🜔": 3149, "🜛": 3150, "🝔": 3151, "🝇": 3152, "🜍": 3153, "🜿": 3154, "🝕": 3155, "🜊": 3156, "🜄": 3157, "🝊": 3158, "🞟": 3159, "🞗": 3160, "🞌": 3161, "🞘": 3162, "🞞": 3163, "🞱": 3164, "🞤": 3165, "🞫": 3166, "⊘": 3167, "⊖": 3168, "⊗": 3169, "⫽": 3170, "🠿": 3171, "🠷": 3172, "🡇": 3173, "🢓": 3174, "🞧": 3175, "🞮": 3176, "🟆": 3177, "🞾": 3178, "🞲": 3179, "🞬": 3180, "🟌": 3181, "🞇": 3182, "🟫": 3183, "🟢": 3184, "🡘": 3185, "🠴": 3186, "🟄": 3187, "🞎": 3188, "🞼": 3189, "🞰": 3190, "🞏": 3191, "🟖": 3192, "🡔": 3193, "⯽": 3194, "⏾": 3195, "🠾": 3196, "⯻": 3197, "🞡": 3198, "🞨": 3199, "⫸": 3200, "⊨": 3201, "🠽": 3202, "🡅": 3203, "🞥": 3204, "🞒": 3205, "🢣": 3206, "🝐": 3207, "🟻": 3208, "⯶": 3209, "🞛": 3210, "🡨": 3211, "🜵": 3212, "🢀": 3213, "🟵": 3214, "🜷": 3215, "🝸": 3216, "🢯": 3217, "🟈": 3218, "⯴": 3219, "🟳": 3220, "⯸": 3221, "🠚": 3222, "🢁": 3223, "🡛": 3224, "🜳": 3225, "🞕": 3226, "🞠": 3227, "🢚": 3228, "🜝": 3229, "🠦": 3230, "🢘": 3231, "🢥": 3232, "🟚": 3233, "🟎": 3234, "🠃": 3235, "🢙": 3236, "🜼": 3237, "🝘": 3238, "🡴": 3239, "🝓": 3240, "🢔": 3241, "⫻": 3242, "🢳": 3243, "🟯": 3244, "🣃": 3245, "🢛": 3246, "🜱": 3247, "🢱": 3248, "🞴": 3249, "🠝": 3250, "⯷": 3251, "🣆": 3252, "🜘": 3253, "🟱": 3254, "🠉": 3255, "🟔": 3256, "🝎": 3257, "🠢": 3258, "🝩": 3259, "🠪": 3260, "🟃": 3261, "🟝": 3262, "🡈": 3263, "⯵": 3264, "🞂": 3265, "🟮": 3266, "🡞": 3267, "🡌": 3268, "🜉": 3269, "⋻": 3270, "🠘": 3271, "🟰": 3272, "🜢": 3273, "🠇": 3274, "🝏": 3275, "🢍": 3276, "🟒": 3277, "🠛": 3278, "🡼": 3279, "🡎": 3280, "🣂": 3281, "🝵": 3282, "🢕": 3283, "🟹": 3284, "🠫": 3285, "🠳": 3286, "🞳": 3287, "🟷": 3288, "🢎": 3289, "🝢": 3290, "🢧": 3291, "🠭": 3292, "🜭": 3293, "🞹": 3294, "🜎": 3295, "🢂": 3296, "⯳": 3297, "🜈": 3298, "🜗": 3299, "🠆": 3300, "🡩": 3301, "🡠": 3302, "🞚": 3303, "🜏": 3304, "🠲": 3305, "🜾": 3306, "🝁": 3307, "🞀": 3308, "🢃": 3309, "🢋": 3310, "🠂": 3311, "🠈": 3312, "🝲": 3313, "🢺": 3314, "🟶": 3315, "🡵": 3316, "🞿": 3317, "🟅": 3318, "🠣": 3319, "🝼": 3320, "⍳": 3321, "⍵": 3322, "⍴": 3323, "⩴": 3324, "⩱": 3325, "⩮": 3326, "≮": 3327, "⩲": 3328, "⧵": 3329, "⩵": 3330, "⍲": 3331, "❲": 3332, "≴": 3333, "❳": 3334, "❵": 3335, "⥪": 3336, "⩳": 3337, "⭳": 3338, "⭴": 3339, "⍯": 3340, "❯": 3341, "⩯": 3342, "≰": 3343, "⍰": 3344, "⪁": 3345, "⥱": 3346, "⍮": 3347, "⭮": 3348, "⇘": 3349, "⇪": 3350, "↴": 3351, "⇓": 3352, "⇥": 3353, "↝": 3354, "ℬ": 3355, "℈": 3356, "⇷": 3357, "⇁": 3358, "ⅇ": 3359, "⇉": 3360, "ℴ": 3361, "↷": 3362, "⇭": 3363, "⦞": 3364, "≃": 3365, "∵": 3366, "⍾": 3367, "✬": 3368, "▰": 3369, "⌍": 3370, "⌟": 3371, "⌌": 3372, "⏡": 3373, "◎": 3374, "◐": 3375, "◓": 3376, "⊳": 3377, "≘": 3378, "⌀": 3379, "⬖": 3380, "⬗": 3381, "∣": 3382, "⋇": 3383, "⋅": 3384, "⩔": 3385, "„": 3386, "ℂ": 3387, "⋱": 3388, "⤋": 3389, "↡": 3390, "⌁": 3391, "⋝": 3392, "≍": 3393, "⨍": 3394, "⊩": 3395, " ": 3396, "≳": 3397, " ": 3398, "✚": 3399, "⧜": 3400, "⨎": 3401, "⌨": 3402, "⬲": 3403, "⌊": 3404, "〈": 3405, "←": 3406, "≲": 3407, "ₒ": 3408, "↹": 3409, "⇍": 3410, "⇎": 3411, "⇞": 3412, "≒": 3413, "⊯": 3414, "⋭": 3415, "⌾": 3416, "⍈": 3417, "⍊": 3418, "⍐": 3419, "⍖": 3420, "⍛": 3421, "⍫": 3422, "⎋": 3423, "⎓": 3424, "⏆": 3425, "⩛": 3426, "◊": 3427, "≫": 3428, "₦": 3429, "⊼": 3430, "⎘": 3431, "‑": 3432, "≄": 3433, "≭": 3434, "⋪": 3435, "℥": 3436, "✙": 3437, "⑽": 3438, "⋔": 3439, "⌘": 3440, "℞": 3441, "⌋": 3442, "◗": 3443, "⊢": 3444, "⦄": 3445, "≗": 3446, "ℐ": 3447, "⌓": 3448, "∿": 3449, " ": 3450, "⋾": 3451, "∊": 3452, "⌑": 3453, "⁺": 3454, "∯": 3455, "⦀": 3456, "⩕": 3457, "✐": 3458, "◹": 3459, "⤒": 3460, "⤊": 3461, "⌚": 3462, "⌇": 3463, "◇": 3464, "◅": 3465, "◽": 3466, "◻": 3467, "▦": 3468, "⋉": 3469, "⬽": 3470, "❏": 3471, "⦈": 3472, "⨒": 3473, "⋨": 3474, "◰": 3475, "⤧": 3476, "⬑": 3477, "ng:logic:modal": 3478, "ng:logic:fuzzy": 3479, "ng:logic:quantum": 3480, "ng:logic:negation_1": 3481, "ng:logic:conjunction_2": 3482, "ng:logic:negation_2": 3483, "ng:logic:temporal_2": 3484, "ng:logic:conjunction_3": 3485, "ng:logic:temporal_3": 3486, "ng:logic:negation_4": 3487, "ng:logic:modal_5": 3488, "ng:logic:temporal_5": 3489, "ng:logic:implication_6": 3490, "ng:logic:modal_6": 3491, "ng:logic:fuzzy_6": 3492, "ng:logic:conjunction_7": 3493, "ng:logic:negation_7": 3494, "ng:logic:temporal_7": 3495, "ng:logic:fuzzy_7": 3496, "ng:logic:quantum_7": 3497, "ng:logic:negation_8": 3498, "ng:logic:biconditional_8": 3499, "ng:logic:quantum_8": 3500, "ng:logic:quantum_9": 3501, "ng:logic:negation_10": 3502, "ng:logic:modal_10": 3503, "ng:logic:temporal_10": 3504, "ng:logic:fuzzy_10": 3505, "ng:logic:quantum_10": 3506, "ng:logic:biconditional_11": 3507, "ng:logic:modal_11": 3508, "ng:logic:fuzzy_11": 3509, "ng:logic:quantum_11": 3510, "ng:logic:implication_12": 3511, "ng:logic:conjunction_12": 3512, "ng:logic:negation_12": 3513, "ng:logic:biconditional_12": 3514, "ng:logic:modal_12": 3515, "ng:logic:fuzzy_12": 3516, "ng:logic:implication_13": 3517, "ng:logic:negation_13": 3518, "ng:logic:biconditional_13": 3519, "ng:logic:modal_13": 3520, "ng:logic:temporal_13": 3521, "ng:logic:quantum_13": 3522, "ng:logic:temporal_14": 3523, "ng:logic:fuzzy_14": 3524, "ng:logic:implication_15": 3525, "ng:logic:negation_15": 3526, "ng:logic:biconditional_15": 3527, "ng:logic:modal_15": 3528, "ng:logic:temporal_15": 3529, "ng:logic:fuzzy_15": 3530, "ng:logic:implication_16": 3531, "ng:logic:biconditional_16": 3532, "ng:logic:modal_16": 3533, "ng:logic:temporal_16": 3534, "ng:logic:fuzzy_16": 3535, "ng:logic:quantum_16": 3536, "ng:logic:implication_17": 3537, "ng:logic:modal_17": 3538, "ng:logic:temporal_17": 3539, "ng:logic:fuzzy_17": 3540, "ng:logic:negation_18": 3541, "ng:logic:biconditional_18": 3542, "ng:logic:fuzzy_18": 3543, "ng:logic:implication_19": 3544, "ng:logic:negation_19": 3545, "ng:logic:biconditional_19": 3546, "ng:logic:fuzzy_19": 3547, "ng:logic:implication_20": 3548, "ng:logic:negation_20": 3549, "ng:logic:biconditional_20": 3550, "ng:logic:modal_20": 3551, "ng:logic:temporal_20": 3552, "ng:logic:conjunction_21": 3553, "ng:logic:biconditional_21": 3554, "ng:logic:modal_21": 3555, "ng:logic:fuzzy_21": 3556, "ng:logic:implication_22": 3557, "ng:logic:negation_22": 3558, "ng:logic:biconditional_22": 3559, "ng:logic:modal_22": 3560, "ng:logic:fuzzy_22": 3561, "ng:logic:quantum_22": 3562, "ng:logic:implication_25": 3563, "ng:logic:conjunction_25": 3564, "ng:logic:negation_25": 3565, "ng:logic:biconditional_25": 3566, "ng:logic:modal_25": 3567, "ng:logic:temporal_25": 3568, "ng:logic:fuzzy_25": 3569, "ng:logic:quantum_25": 3570, "ng:logic:implication_26": 3571, "ng:logic:conjunction_26": 3572, "ng:logic:negation_26": 3573, "ng:logic:biconditional_26": 3574, "ng:logic:modal_26": 3575, "ng:logic:temporal_26": 3576, "ng:logic:fuzzy_26": 3577, "ng:logic:quantum_26": 3578, "ng:logic:implication_27": 3579, "ng:logic:conjunction_27": 3580, "ng:logic:negation_27": 3581, "ng:logic:biconditional_27": 3582, "ng:logic:modal_27": 3583, "ng:logic:temporal_27": 3584, "ng:logic:fuzzy_27": 3585, "ng:logic:quantum_27": 3586, "ng:logic:implication_28": 3587, "ng:logic:conjunction_28": 3588, "ng:logic:negation_28": 3589, "ng:logic:biconditional_28": 3590, "ng:logic:modal_28": 3591, "ng:logic:temporal_28": 3592, "ng:logic:fuzzy_28": 3593, "ng:logic:quantum_28": 3594, "ng:logic:implication_29": 3595, "ng:logic:conjunction_29": 3596, "ng:logic:negation_29": 3597, "ng:logic:biconditional_29": 3598, "ng:logic:modal_29": 3599, "ng:logic:temporal_29": 3600, "ng:logic:fuzzy_29": 3601, "ng:logic:quantum_29": 3602, "ng:logic:implication_30": 3603, "ng:logic:conjunction_30": 3604, "ng:logic:negation_30": 3605, "ng:logic:biconditional_30": 3606, "ng:logic:modal_30": 3607, "ng:logic:temporal_30": 3608, "ng:logic:fuzzy_30": 3609, "ng:logic:quantum_30": 3610, "ng:logic:implication_31": 3611, "ng:logic:conjunction_31": 3612, "ng:logic:negation_31": 3613, "ng:logic:biconditional_31": 3614, "ng:logic:modal_31": 3615, "ng:logic:temporal_31": 3616, "ng:logic:fuzzy_31": 3617, "ng:logic:quantum_31": 3618, "ng:logic:implication_32": 3619, "ng:logic:conjunction_32": 3620, "ng:logic:negation_32": 3621, "ng:logic:biconditional_32": 3622, "ng:logic:modal_32": 3623, "ng:logic:temporal_32": 3624, "ng:logic:fuzzy_32": 3625, "ng:logic:quantum_32": 3626, "ng:logic:implication_33": 3627, "ng:logic:conjunction_33": 3628, "ng:logic:negation_33": 3629, "ng:logic:biconditional_33": 3630, "ng:logic:modal_33": 3631, "ng:logic:temporal_33": 3632, "ng:logic:fuzzy_33": 3633, "ng:logic:quantum_33": 3634, "ng:logic:implication_34": 3635, "ng:logic:conjunction_34": 3636, "ng:logic:negation_34": 3637, "ng:logic:biconditional_34": 3638, "ng:logic:modal_34": 3639, "ng:logic:temporal_34": 3640, "ng:logic:fuzzy_34": 3641, "ng:logic:quantum_34": 3642, "ng:logic:implication_35": 3643, "ng:logic:conjunction_35": 3644, "ng:logic:negation_35": 3645, "ng:logic:biconditional_35": 3646, "ng:logic:modal_35": 3647, "ng:logic:temporal_35": 3648, "ng:logic:fuzzy_35": 3649, "ng:logic:quantum_35": 3650, "ng:logic:implication_36": 3651, "ng:logic:conjunction_36": 3652, "ng:logic:negation_36": 3653, "ng:logic:biconditional_36": 3654, "ng:logic:modal_36": 3655, "ng:logic:temporal_36": 3656, "ng:logic:fuzzy_36": 3657, "ng:logic:quantum_36": 3658, "ng:logic:implication_37": 3659, "ng:logic:conjunction_37": 3660, "ng:logic:negation_37": 3661, "ng:logic:biconditional_37": 3662, "ng:logic:modal_37": 3663, "ng:logic:temporal_37": 3664, "ng:logic:fuzzy_37": 3665, "ng:logic:quantum_37": 3666, "ng:logic:implication_38": 3667, "ng:logic:conjunction_38": 3668, "ng:logic:negation_38": 3669, "ng:logic:modal_38": 3670, "ng:logic:temporal_38": 3671, "ng:logic:fuzzy_38": 3672, "ng:logic:quantum_38": 3673, "ng:logic:implication_39": 3674, "ng:logic:conjunction_39": 3675, "ng:logic:negation_39": 3676, "ng:logic:biconditional_39": 3677, "ng:logic:modal_39": 3678, "ng:logic:temporal_39": 3679, "ng:logic:fuzzy_39": 3680, "ng:logic:quantum_39": 3681, "ng:logic:implication_40": 3682, "ng:logic:conjunction_40": 3683, "ng:logic:negation_40": 3684, "ng:logic:biconditional_40": 3685, "ng:logic:modal_40": 3686, "ng:logic:temporal_40": 3687, "ng:logic:fuzzy_40": 3688, "ng:logic:quantum_40": 3689, "ng:logic:implication_41": 3690, "ng:logic:conjunction_41": 3691, "ng:logic:negation_41": 3692, "ng:logic:biconditional_41": 3693, "ng:logic:modal_41": 3694, "ng:logic:temporal_41": 3695, "ng:logic:fuzzy_41": 3696, "ng:logic:quantum_41": 3697, "ng:logic:implication_42": 3698, "ng:logic:conjunction_42": 3699, "ng:logic:negation_42": 3700, "ng:logic:biconditional_42": 3701, "ng:logic:modal_42": 3702, "ng:logic:temporal_42": 3703, "ng:logic:fuzzy_42": 3704, "ng:logic:quantum_42": 3705, "ng:logic:implication_43": 3706, "ng:logic:conjunction_43": 3707, "ng:logic:negation_43": 3708, "ng:logic:biconditional_43": 3709, "ng:logic:modal_43": 3710, "ng:logic:temporal_43": 3711, "ng:logic:fuzzy_43": 3712, "ng:logic:quantum_43": 3713, "ng:logic:implication_44": 3714, "ng:logic:conjunction_44": 3715, "ng:logic:negation_44": 3716, "ng:logic:biconditional_44": 3717, "ng:logic:modal_44": 3718, "ng:logic:temporal_44": 3719, "ng:logic:fuzzy_44": 3720, "ng:logic:quantum_44": 3721, "ng:logic:conjunction_45": 3722, "ng:logic:negation_45": 3723, "ng:logic:biconditional_45": 3724, "ng:logic:modal_45": 3725, "ng:logic:temporal_45": 3726, "ng:logic:fuzzy_45": 3727, "ng:logic:quantum_45": 3728, "ng:logic:implication_46": 3729, "ng:logic:conjunction_46": 3730, "ng:logic:negation_46": 3731, "ng:logic:biconditional_46": 3732, "ng:logic:modal_46": 3733, "ng:logic:temporal_46": 3734, "ng:logic:fuzzy_46": 3735, "ng:logic:quantum_46": 3736, "ng:logic:implication_47": 3737, "ng:logic:conjunction_47": 3738, "ng:logic:negation_47": 3739, "ng:logic:biconditional_47": 3740, "ng:logic:modal_47": 3741, "ng:logic:temporal_47": 3742, "ng:logic:fuzzy_47": 3743, "ng:logic:quantum_47": 3744, "ng:logic:implication_48": 3745, "ng:logic:conjunction_48": 3746, "ng:logic:negation_48": 3747, "ng:logic:biconditional_48": 3748, "ng:logic:modal_48": 3749, "ng:logic:temporal_48": 3750, "ng:logic:fuzzy_48": 3751, "ng:logic:quantum_48": 3752, "ng:logic:implication_49": 3753, "ng:logic:conjunction_49": 3754, "ng:logic:biconditional_49": 3755, "ng:logic:modal_49": 3756, "ng:logic:temporal_49": 3757, "ng:logic:fuzzy_49": 3758, "ng:logic:quantum_49": 3759, "ng:logic:implication_50": 3760, "ng:logic:conjunction_50": 3761, "ng:logic:negation_50": 3762, "ng:logic:biconditional_50": 3763, "ng:logic:modal_50": 3764, "ng:logic:temporal_50": 3765, "ng:logic:fuzzy_50": 3766, "ng:logic:quantum_50": 3767, "ng:logic:implication_51": 3768, "ng:logic:conjunction_51": 3769, "ng:logic:negation_51": 3770, "ng:logic:biconditional_51": 3771, "ng:logic:modal_51": 3772, "ng:logic:temporal_51": 3773, "ng:logic:fuzzy_51": 3774, "ng:logic:quantum_51": 3775, "ng:logic:implication_52": 3776, "ng:logic:conjunction_52": 3777, "ng:logic:negation_52": 3778, "ng:logic:biconditional_52": 3779, "ng:logic:modal_52": 3780, "ng:logic:temporal_52": 3781, "ng:logic:fuzzy_52": 3782, "ng:logic:quantum_52": 3783, "ng:logic:implication_53": 3784, "ng:logic:conjunction_53": 3785, "ng:logic:negation_53": 3786, "ng:logic:biconditional_53": 3787, "ng:logic:modal_53": 3788, "ng:logic:temporal_53": 3789, "ng:logic:fuzzy_53": 3790, "ng:logic:quantum_53": 3791, "ng:logic:implication_54": 3792, "ng:logic:conjunction_54": 3793, "ng:logic:negation_54": 3794, "ng:logic:biconditional_54": 3795, "ng:logic:modal_54": 3796, "ng:logic:temporal_54": 3797, "ng:logic:fuzzy_54": 3798, "ng:logic:quantum_54": 3799, "ng:logic:implication_55": 3800, "ng:logic:conjunction_55": 3801, "ng:logic:negation_55": 3802, "ng:logic:biconditional_55": 3803, "ng:logic:modal_55": 3804, "ng:logic:temporal_55": 3805, "ng:logic:fuzzy_55": 3806, "ng:logic:quantum_55": 3807, "ng:logic:implication_56": 3808, "ng:logic:conjunction_56": 3809, "ng:logic:negation_56": 3810, "ng:logic:biconditional_56": 3811, "ng:logic:modal_56": 3812, "ng:logic:temporal_56": 3813, "ng:logic:fuzzy_56": 3814, "ng:logic:quantum_56": 3815, "ng:logic:implication_57": 3816, "ng:logic:negation_57": 3817, "ng:logic:biconditional_57": 3818, "ng:logic:modal_57": 3819, "ng:logic:temporal_57": 3820, "ng:logic:fuzzy_57": 3821, "ng:logic:quantum_57": 3822, "ng:logic:implication_58": 3823, "ng:logic:conjunction_58": 3824, "ng:logic:negation_58": 3825, "ng:logic:biconditional_58": 3826, "ng:logic:modal_58": 3827, "ng:logic:temporal_58": 3828, "ng:logic:fuzzy_58": 3829, "ng:logic:quantum_58": 3830, "ng:logic:implication_59": 3831, "ng:logic:conjunction_59": 3832, "ng:logic:negation_59": 3833, "ng:logic:biconditional_59": 3834, "ng:logic:modal_59": 3835, "ng:logic:temporal_59": 3836, "ng:logic:fuzzy_59": 3837, "ng:logic:quantum_59": 3838, "ng:logic:implication_60": 3839, "ng:logic:conjunction_60": 3840, "ng:logic:negation_60": 3841, "ng:logic:biconditional_60": 3842, "ng:logic:modal_60": 3843, "ng:logic:temporal_60": 3844, "ng:logic:fuzzy_60": 3845, "ng:logic:quantum_60": 3846, "ng:logic:implication_61": 3847, "ng:logic:conjunction_61": 3848, "ng:logic:negation_61": 3849, "ng:logic:biconditional_61": 3850, "ng:logic:modal_61": 3851, "ng:logic:temporal_61": 3852, "ng:logic:fuzzy_61": 3853, "ng:logic:quantum_61": 3854, "ng:logic:implication_62": 3855, "ng:logic:conjunction_62": 3856, "ng:logic:negation_62": 3857, "ng:logic:biconditional_62": 3858, "ng:logic:modal_62": 3859, "ng:logic:temporal_62": 3860, "ng:logic:fuzzy_62": 3861, "ng:logic:quantum_62": 3862, "ng:logic:implication_63": 3863, "ng:logic:conjunction_63": 3864, "ng:logic:negation_63": 3865, "ng:logic:biconditional_63": 3866, "ng:logic:modal_63": 3867, "ng:logic:temporal_63": 3868, "ng:logic:fuzzy_63": 3869, "ng:logic:quantum_63": 3870, "ng:logic:implication_64": 3871, "ng:logic:conjunction_64": 3872, "ng:logic:negation_64": 3873, "ng:logic:biconditional_64": 3874, "ng:logic:modal_64": 3875, "ng:logic:temporal_64": 3876, "ng:logic:fuzzy_64": 3877, "ng:logic:quantum_64": 3878, "ng:logic:implication_65": 3879, "ng:logic:conjunction_65": 3880, "ng:logic:negation_65": 3881, "ng:logic:biconditional_65": 3882, "ng:logic:modal_65": 3883, "ng:logic:temporal_65": 3884, "ng:logic:fuzzy_65": 3885, "ng:logic:quantum_65": 3886, "ng:logic:implication_66": 3887, "ng:logic:conjunction_66": 3888, "ng:logic:negation_66": 3889, "ng:logic:biconditional_66": 3890, "ng:logic:modal_66": 3891, "ng:logic:temporal_66": 3892, "ng:logic:fuzzy_66": 3893, "ng:logic:quantum_66": 3894, "ng:logic:implication_67": 3895, "ng:logic:conjunction_67": 3896, "ng:logic:negation_67": 3897, "ng:logic:biconditional_67": 3898, "ng:logic:modal_67": 3899, "ng:logic:temporal_67": 3900, "ng:logic:fuzzy_67": 3901, "ng:logic:quantum_67": 3902, "ng:logic:implication_68": 3903, "ng:logic:conjunction_68": 3904, "ng:logic:negation_68": 3905, "ng:logic:biconditional_68": 3906, "ng:logic:modal_68": 3907, "ng:logic:temporal_68": 3908, "ng:logic:fuzzy_68": 3909, "ng:logic:quantum_68": 3910, "ng:logic:implication_69": 3911, "ng:logic:conjunction_69": 3912, "ng:logic:negation_69": 3913, "ng:logic:biconditional_69": 3914, "ng:logic:modal_69": 3915, "ng:logic:temporal_69": 3916, "ng:logic:fuzzy_69": 3917, "ng:logic:quantum_69": 3918, "ng:logic:implication_70": 3919, "ng:logic:conjunction_70": 3920, "ng:logic:negation_70": 3921, "ng:logic:biconditional_70": 3922, "ng:logic:modal_70": 3923, "ng:logic:temporal_70": 3924, "ng:logic:fuzzy_70": 3925, "ng:logic:quantum_70": 3926, "ng:logic:implication_71": 3927, "ng:logic:conjunction_71": 3928, "ng:logic:negation_71": 3929, "ng:logic:biconditional_71": 3930, "ng:logic:modal_71": 3931, "ng:logic:temporal_71": 3932, "ng:logic:fuzzy_71": 3933, "ng:logic:quantum_71": 3934, "ng:logic:implication_72": 3935, "ng:logic:conjunction_72": 3936, "ng:logic:negation_72": 3937, "ng:logic:biconditional_72": 3938, "ng:logic:modal_72": 3939, "ng:logic:temporal_72": 3940, "ng:logic:fuzzy_72": 3941, "ng:logic:quantum_72": 3942, "ng:logic:implication_73": 3943, "ng:logic:conjunction_73": 3944, "ng:logic:negation_73": 3945, "ng:logic:biconditional_73": 3946, "ng:logic:modal_73": 3947, "ng:logic:temporal_73": 3948, "ng:logic:fuzzy_73": 3949, "ng:logic:quantum_73": 3950, "ng:logic:implication_74": 3951, "ng:logic:conjunction_74": 3952, "ng:logic:negation_74": 3953, "ng:logic:modal_74": 3954, "ng:logic:temporal_74": 3955, "ng:logic:fuzzy_74": 3956, "ng:logic:quantum_74": 3957, "ng:logic:implication_75": 3958, "ng:logic:conjunction_75": 3959, "ng:logic:negation_75": 3960, "ng:logic:biconditional_75": 3961, "ng:logic:modal_75": 3962, "ng:logic:temporal_75": 3963, "ng:logic:fuzzy_75": 3964, "ng:logic:quantum_75": 3965, "ng:logic:implication_76": 3966, "ng:logic:conjunction_76": 3967, "ng:logic:negation_76": 3968, "ng:logic:biconditional_76": 3969, "ng:logic:modal_76": 3970, "ng:logic:temporal_76": 3971, "ng:logic:fuzzy_76": 3972, "ng:logic:quantum_76": 3973, "ng:logic:implication_77": 3974, "ng:logic:conjunction_77": 3975, "ng:logic:negation_77": 3976, "ng:logic:biconditional_77": 3977, "ng:logic:modal_77": 3978, "ng:logic:temporal_77": 3979, "ng:logic:fuzzy_77": 3980, "ng:logic:quantum_77": 3981, "ng:logic:implication_78": 3982, "ng:logic:conjunction_78": 3983, "ng:logic:negation_78": 3984, "ng:logic:biconditional_78": 3985, "ng:logic:modal_78": 3986, "ng:logic:temporal_78": 3987, "ng:logic:fuzzy_78": 3988, "ng:logic:quantum_78": 3989, "ng:logic:implication_79": 3990, "ng:logic:conjunction_79": 3991, "ng:logic:negation_79": 3992, "ng:logic:modal_79": 3993, "ng:logic:temporal_79": 3994, "ng:logic:fuzzy_79": 3995, "ng:logic:quantum_79": 3996, "ng:logic:implication_80": 3997, "ng:logic:conjunction_80": 3998, "ng:logic:negation_80": 3999, "ng:logic:biconditional_80": 4000, "ng:logic:modal_80": 4001, "ng:logic:temporal_80": 4002, "ng:logic:fuzzy_80": 4003, "ng:logic:quantum_80": 4004, "ng:logic:implication_81": 4005, "ng:logic:conjunction_81": 4006, "ng:logic:negation_81": 4007, "ng:logic:biconditional_81": 4008, "ng:logic:modal_81": 4009, "ng:logic:temporal_81": 4010, "ng:logic:fuzzy_81": 4011, "ng:logic:quantum_81": 4012, "ng:logic:implication_82": 4013, "ng:logic:conjunction_82": 4014, "ng:logic:negation_82": 4015, "ng:logic:biconditional_82": 4016, "ng:logic:modal_82": 4017, "ng:logic:temporal_82": 4018, "ng:logic:fuzzy_82": 4019, "ng:logic:quantum_82": 4020, "ng:logic:implication_83": 4021, "ng:logic:conjunction_83": 4022, "ng:logic:negation_83": 4023, "ng:logic:biconditional_83": 4024, "ng:logic:modal_83": 4025, "ng:logic:temporal_83": 4026, "ng:logic:fuzzy_83": 4027, "ng:logic:quantum_83": 4028, "ng:logic:implication_84": 4029, "ng:logic:conjunction_84": 4030, "ng:logic:negation_84": 4031, "ng:logic:biconditional_84": 4032, "ng:logic:modal_84": 4033, "ng:logic:temporal_84": 4034, "ng:logic:fuzzy_84": 4035, "ng:logic:quantum_84": 4036, "ng:logic:implication_85": 4037, "ng:logic:conjunction_85": 4038, "ng:logic:negation_85": 4039, "ng:logic:biconditional_85": 4040, "ng:logic:modal_85": 4041, "ng:logic:temporal_85": 4042, "ng:logic:fuzzy_85": 4043, "ng:logic:quantum_85": 4044, "ng:logic:implication_86": 4045, "ng:logic:conjunction_86": 4046, "ng:logic:negation_86": 4047, "ng:logic:biconditional_86": 4048, "ng:logic:modal_86": 4049, "ng:logic:temporal_86": 4050, "ng:logic:fuzzy_86": 4051, "ng:logic:quantum_86": 4052, "ng:logic:implication_87": 4053, "ng:logic:conjunction_87": 4054, "ng:logic:negation_87": 4055, "ng:logic:biconditional_87": 4056, "ng:logic:modal_87": 4057, "ng:logic:temporal_87": 4058, "ng:logic:fuzzy_87": 4059, "ng:logic:quantum_87": 4060, "⤹": 4061, "◂": 4062, "⭃": 4063, "⨔": 4064, "◲": 4065, "⤈": 4066, "⦋": 4067, "⬏": 4068, "⭞": 4069, "▩": 4070, "↻": 4071, "ℸ": 4072, "℔": 4073, "↩": 4074, "↗": 4075, "⇱": 4076, "Ω": 4077, "⅋": 4078, "⇟": 4079, "⎕": 4080, "⊜": 4081, "⩭": 4082, "⎖": 4083, "⪢": 4084, "⪡": 4085, "➗": 4086, "➖": 4087, "➙": 4088, "➘": 4089, "⎎": 4090, "⎜": 4091, "⎝": 4092, "⎛": 4093, "⎍": 4094, "⊌": 4095, "⮒": 4096, "⮓": 4097, "⎒": 4098, "⯕": 4099, "⎗": 4100, "⎙": 4101, "⮑": 4102, "⮳": 4103, "⥽": 4104, "⎟": 4105, "⎠": 4106, "⪞": 4107, "⪝": 4108, "⪕": 4109, "⎔": 4110, "⊔": 4111, "⊑": 4112, "⊡": 4113, "⊟": 4114, "⮗": 4115, "➍": 4116, "➎": 4117, "➏": 4118, "⪌": 4119, "⪍": 4120, "⪎": 4121, "⮍": 4122, "➒": 4123, "➓": 4124, "➔": 4125, "➝": 4126, "➟": 4127, "➢": 4128, "➽": 4129, "⥵": 4130, "⪄": 4131, "⪋": 4132, "⪓": 4133, "⪔": 4134, "⪗": 4135, "⪛": 4136, "⫮": 4137, "⭰": 4138, "⭲": 4139, "⮖": 4140, "⮚": 4141, "⮛": 4142, "⮝": 4143, "⮠": 4144, "⮪": 4145, "⎐": 4146, "➐": 4147, "➑": 4148, "⩰": 4149, "⪏": 4150, "⪐": 4151, "⪑": 4152, "➌": 4153, "⮋": 4154, "➋": 4155, "⎇": 4156, "⊦": 4157, "ℭ": 4158, "⌡": 4159, "•": 4160, "⑭": 4161, "≔": 4162, "⌴": 4163, "≜": 4164, "⊀": 4165, "⊁": 4166, "⋸": 4167, "∅": 4168, "℮": 4169, "ℇ": 4170, "∹": 4171, "‒": 4172, "⊹": 4173, "⎻": 4174, "‐": 4175, "∾": 4176, "⇿": 4177, "ₐ": 4178, "ₓ": 4179, "⇀": 4180, "⌻": 4181, "⍉": 4182, "⎏": 4183, "⏅": 4184, "⊧": 4185, " ": 4186, "⇗": 4187, "⒘": 4188, "⏻": 4189, "⎞": 4190, "Ⅶ": 4191, "Ⅹ": 4192, "⍽": 4193, "ⅰ": 4194, "↘": 4195, "↙": 4196, "⊠": 4197, "₀": 4198, "⊱": 4199, "∼": 4200, "⌠": 4201, "‴": 4202, "↥": 4203, "⇈": 4204, "∰": 4205, "ng:math:sum": 4206, "ng:math:category": 4207, "ng:math:sum_1": 4208, "ng:math:integral_3": 4209, "ng:math:tensor_3": 4210, "ng:math:topology_3": 4211, "ng:math:lambda_3": 4212, "ng:math:topology_5": 4213, "ng:math:lambda_5": 4214, "ng:math:tensor_6": 4215, "ng:math:matrix_6": 4216, "ng:math:sum_7": 4217, "ng:math:integral_7": 4218, "ng:math:matrix_7": 4219, "ng:math:function_7": 4220, "ng:math:topology_7": 4221, "ng:math:category_7": 4222, "ng:math:matrix_8": 4223, "ng:math:topology_8": 4224, "ng:math:category_8": 4225, "ng:math:tensor_9": 4226, "ng:math:tensor_10": 4227, "ng:math:function_10": 4228, "ng:math:category_10": 4229, "ng:math:lambda_10": 4230, "ng:math:sum_11": 4231, "ng:math:integral_11": 4232, "ng:math:tensor_11": 4233, "ng:math:topology_11": 4234, "ng:math:category_11": 4235, "ng:math:lambda_11": 4236, "ng:math:sum_12": 4237, "ng:math:integral_12": 4238, "ng:math:tensor_12": 4239, "ng:math:function_12": 4240, "ng:math:topology_12": 4241, "ng:math:category_12": 4242, "ng:math:function_13": 4243, "ng:math:topology_13": 4244, "ng:math:lambda_13": 4245, "ng:math:integral_14": 4246, "ng:math:tensor_14": 4247, "ng:math:matrix_14": 4248, "ng:math:function_14": 4249, "ng:math:category_14": 4250, "ng:math:lambda_14": 4251, "ng:math:sum_15": 4252, "ng:math:integral_15": 4253, "ng:math:tensor_15": 4254, "ng:math:function_15": 4255, "ng:math:topology_15": 4256, "ng:math:lambda_15": 4257, "ng:math:sum_16": 4258, "ng:math:tensor_16": 4259, "ng:math:matrix_16": 4260, "ng:math:function_16": 4261, "ng:math:topology_16": 4262, "ng:math:category_16": 4263, "ng:math:sum_17": 4264, "ng:math:integral_17": 4265, "ng:math:matrix_17": 4266, "ng:math:function_17": 4267, "ng:math:category_17": 4268, "ng:math:sum_18": 4269, "ng:math:integral_18": 4270, "ng:math:tensor_18": 4271, "ng:math:function_18": 4272, "ng:math:topology_18": 4273, "ng:math:category_18": 4274, "ng:math:sum_19": 4275, "ng:math:tensor_19": 4276, "ng:math:matrix_19": 4277, "ng:math:function_19": 4278, "ng:math:topology_19": 4279, "ng:math:lambda_19": 4280, "ng:math:sum_20": 4281, "ng:math:tensor_20": 4282, "ng:math:matrix_20": 4283, "ng:math:function_20": 4284, "ng:math:integral_21": 4285, "ng:math:tensor_21": 4286, "ng:math:category_21": 4287, "ng:math:integral_22": 4288, "ng:math:tensor_22": 4289, "ng:math:matrix_22": 4290, "ng:math:sum_24": 4291, "ng:math:integral_24": 4292, "ng:math:tensor_24": 4293, "ng:math:matrix_24": 4294, "ng:math:function_24": 4295, "ng:math:topology_24": 4296, "ng:math:category_24": 4297, "ng:math:lambda_24": 4298, "ng:math:sum_25": 4299, "ng:math:integral_25": 4300, "ng:math:tensor_25": 4301, "ng:math:matrix_25": 4302, "ng:math:function_25": 4303, "ng:math:topology_25": 4304, "ng:math:category_25": 4305, "ng:math:lambda_25": 4306, "ng:math:sum_26": 4307, "ng:math:integral_26": 4308, "ng:math:tensor_26": 4309, "ng:math:matrix_26": 4310, "ng:math:function_26": 4311, "ng:math:topology_26": 4312, "ng:math:category_26": 4313, "ng:math:lambda_26": 4314, "ng:math:sum_27": 4315, "ng:math:integral_27": 4316, "ng:math:tensor_27": 4317, "ng:math:matrix_27": 4318, "ng:math:function_27": 4319, "ng:math:topology_27": 4320, "ng:math:category_27": 4321, "ng:math:lambda_27": 4322, "ng:math:sum_28": 4323, "ng:math:integral_28": 4324, "ng:math:tensor_28": 4325, "ng:math:matrix_28": 4326, "ng:math:function_28": 4327, "ng:math:topology_28": 4328, "ng:math:category_28": 4329, "ng:math:lambda_28": 4330, "ng:math:sum_29": 4331, "ng:math:integral_29": 4332, "ng:math:tensor_29": 4333, "ng:math:matrix_29": 4334, "ng:math:function_29": 4335, "ng:math:topology_29": 4336, "ng:math:category_29": 4337, "ng:math:lambda_29": 4338, "ng:math:sum_30": 4339, "ng:math:integral_30": 4340, "ng:math:tensor_30": 4341, "ng:math:matrix_30": 4342, "ng:math:function_30": 4343, "ng:math:topology_30": 4344, "ng:math:category_30": 4345, "ng:math:lambda_30": 4346, "ng:math:sum_31": 4347, "ng:math:integral_31": 4348, "ng:math:tensor_31": 4349, "ng:math:matrix_31": 4350, "ng:math:function_31": 4351, "ng:math:topology_31": 4352, "ng:math:category_31": 4353, "ng:math:lambda_31": 4354, "ng:math:sum_32": 4355, "ng:math:integral_32": 4356, "ng:math:tensor_32": 4357, "ng:math:matrix_32": 4358, "ng:math:function_32": 4359, "ng:math:topology_32": 4360, "ng:math:category_32": 4361, "ng:math:lambda_32": 4362, "ng:math:sum_33": 4363, "ng:math:integral_33": 4364, "ng:math:tensor_33": 4365, "ng:math:matrix_33": 4366, "ng:math:function_33": 4367, "ng:math:topology_33": 4368, "ng:math:category_33": 4369, "ng:math:lambda_33": 4370, "ng:math:sum_34": 4371, "ng:math:integral_34": 4372, "ng:math:tensor_34": 4373, "ng:math:matrix_34": 4374, "ng:math:function_34": 4375, "ng:math:topology_34": 4376, "ng:math:category_34": 4377, "ng:math:lambda_34": 4378, "ng:math:sum_35": 4379, "ng:math:integral_35": 4380, "ng:math:tensor_35": 4381, "ng:math:matrix_35": 4382, "ng:math:function_35": 4383, "ng:math:topology_35": 4384, "ng:math:category_35": 4385, "ng:math:lambda_35": 4386, "ng:math:sum_36": 4387, "ng:math:integral_36": 4388, "ng:math:tensor_36": 4389, "ng:math:matrix_36": 4390, "ng:math:function_36": 4391, "ng:math:topology_36": 4392, "ng:math:category_36": 4393, "ng:math:lambda_36": 4394, "ng:math:sum_37": 4395, "ng:math:integral_37": 4396, "ng:math:tensor_37": 4397, "ng:math:matrix_37": 4398, "ng:math:function_37": 4399, "ng:math:topology_37": 4400, "ng:math:category_37": 4401, "ng:math:lambda_37": 4402, "ng:math:sum_38": 4403, "ng:math:integral_38": 4404, "ng:math:tensor_38": 4405, "ng:math:matrix_38": 4406, "ng:math:function_38": 4407, "ng:math:topology_38": 4408, "ng:math:category_38": 4409, "ng:math:lambda_38": 4410, "ng:math:sum_39": 4411, "ng:math:integral_39": 4412, "ng:math:tensor_39": 4413, "ng:math:matrix_39": 4414, "ng:math:function_39": 4415, "ng:math:topology_39": 4416, "ng:math:category_39": 4417, "ng:math:lambda_39": 4418, "ng:math:sum_40": 4419, "ng:math:integral_40": 4420, "ng:math:tensor_40": 4421, "ng:math:matrix_40": 4422, "ng:math:function_40": 4423, "ng:math:topology_40": 4424, "ng:math:category_40": 4425, "ng:math:lambda_40": 4426, "ng:math:sum_41": 4427, "ng:math:integral_41": 4428, "ng:math:tensor_41": 4429, "ng:math:matrix_41": 4430, "ng:math:function_41": 4431, "ng:math:topology_41": 4432, "ng:math:category_41": 4433, "ng:math:lambda_41": 4434, "ng:math:sum_42": 4435, "ng:math:integral_42": 4436, "ng:math:tensor_42": 4437, "ng:math:matrix_42": 4438, "ng:math:function_42": 4439, "ng:math:topology_42": 4440, "ng:math:category_42": 4441, "ng:math:lambda_42": 4442, "ng:math:sum_43": 4443, "ng:math:integral_43": 4444, "ng:math:tensor_43": 4445, "ng:math:matrix_43": 4446, "ng:math:function_43": 4447, "ng:math:topology_43": 4448, "ng:math:category_43": 4449, "ng:math:lambda_43": 4450, "ng:math:sum_44": 4451, "ng:math:integral_44": 4452, "ng:math:tensor_44": 4453, "ng:math:matrix_44": 4454, "ng:math:function_44": 4455, "ng:math:topology_44": 4456, "ng:math:category_44": 4457, "ng:math:lambda_44": 4458, "ng:math:sum_45": 4459, "ng:math:integral_45": 4460, "ng:math:tensor_45": 4461, "ng:math:matrix_45": 4462, "ng:math:function_45": 4463, "ng:math:topology_45": 4464, "ng:math:category_45": 4465, "ng:math:lambda_45": 4466, "ng:math:sum_46": 4467, "ng:math:integral_46": 4468, "ng:math:tensor_46": 4469, "ng:math:matrix_46": 4470, "ng:math:function_46": 4471, "ng:math:topology_46": 4472, "ng:math:category_46": 4473, "ng:math:lambda_46": 4474, "ng:math:sum_47": 4475, "ng:math:integral_47": 4476, "ng:math:tensor_47": 4477, "ng:math:matrix_47": 4478, "ng:math:function_47": 4479, "ng:math:topology_47": 4480, "ng:math:category_47": 4481, "ng:math:lambda_47": 4482, "ng:math:sum_48": 4483, "ng:math:integral_48": 4484, "ng:math:tensor_48": 4485, "ng:math:matrix_48": 4486, "ng:math:function_48": 4487, "ng:math:topology_48": 4488, "ng:math:category_48": 4489, "ng:math:lambda_48": 4490, "ng:math:sum_49": 4491, "ng:math:integral_49": 4492, "ng:math:tensor_49": 4493, "ng:math:matrix_49": 4494, "ng:math:function_49": 4495, "ng:math:topology_49": 4496, "ng:math:category_49": 4497, "ng:math:lambda_49": 4498, "ng:math:sum_50": 4499, "ng:math:integral_50": 4500, "ng:math:tensor_50": 4501, "ng:math:matrix_50": 4502, "ng:math:function_50": 4503, "ng:math:topology_50": 4504, "ng:math:category_50": 4505, "ng:math:lambda_50": 4506, "ng:math:sum_51": 4507, "ng:math:integral_51": 4508, "ng:math:tensor_51": 4509, "ng:math:matrix_51": 4510, "ng:math:function_51": 4511, "ng:math:topology_51": 4512, "ng:math:category_51": 4513, "ng:math:lambda_51": 4514, "ng:math:sum_52": 4515, "ng:math:integral_52": 4516, "ng:math:tensor_52": 4517, "ng:math:matrix_52": 4518, "ng:math:function_52": 4519, "ng:math:topology_52": 4520, "ng:math:category_52": 4521, "ng:math:lambda_52": 4522, "ng:math:sum_53": 4523, "ng:math:integral_53": 4524, "ng:math:tensor_53": 4525, "ng:math:matrix_53": 4526, "ng:math:function_53": 4527, "ng:math:topology_53": 4528, "ng:math:category_53": 4529, "ng:math:lambda_53": 4530, "ng:math:sum_54": 4531, "ng:math:integral_54": 4532, "ng:math:tensor_54": 4533, "ng:math:matrix_54": 4534, "ng:math:function_54": 4535, "ng:math:topology_54": 4536, "ng:math:category_54": 4537, "ng:math:lambda_54": 4538, "ng:math:sum_55": 4539, "ng:math:integral_55": 4540, "ng:math:tensor_55": 4541, "ng:math:matrix_55": 4542, "ng:math:function_55": 4543, "ng:math:topology_55": 4544, "ng:math:category_55": 4545, "ng:math:lambda_55": 4546, "ng:math:sum_56": 4547, "ng:math:integral_56": 4548, "ng:math:tensor_56": 4549, "ng:math:matrix_56": 4550, "ng:math:function_56": 4551, "ng:math:topology_56": 4552, "ng:math:category_56": 4553, "ng:math:lambda_56": 4554, "ng:math:sum_57": 4555, "ng:math:integral_57": 4556, "ng:math:tensor_57": 4557, "ng:math:matrix_57": 4558, "ng:math:function_57": 4559, "ng:math:topology_57": 4560, "ng:math:category_57": 4561, "ng:math:lambda_57": 4562, "ng:math:sum_58": 4563, "ng:math:integral_58": 4564, "ng:math:tensor_58": 4565, "ng:math:matrix_58": 4566, "ng:math:function_58": 4567, "ng:math:topology_58": 4568, "ng:math:category_58": 4569, "ng:math:lambda_58": 4570, "ng:math:sum_59": 4571, "ng:math:integral_59": 4572, "ng:math:tensor_59": 4573, "ng:math:matrix_59": 4574, "ng:math:function_59": 4575, "ng:math:topology_59": 4576, "ng:math:category_59": 4577, "ng:math:lambda_59": 4578, "ng:math:sum_60": 4579, "ng:math:integral_60": 4580, "ng:math:tensor_60": 4581, "ng:math:matrix_60": 4582, "ng:math:function_60": 4583, "ng:math:topology_60": 4584, "ng:math:category_60": 4585, "ng:math:lambda_60": 4586, "ng:math:sum_61": 4587, "ng:math:integral_61": 4588, "ng:math:tensor_61": 4589, "ng:math:matrix_61": 4590, "ng:math:function_61": 4591, "ng:math:topology_61": 4592, "ng:math:category_61": 4593, "ng:math:lambda_61": 4594, "ng:math:sum_62": 4595, "ng:math:integral_62": 4596, "ng:math:tensor_62": 4597, "ng:math:matrix_62": 4598, "ng:math:function_62": 4599, "ng:math:topology_62": 4600, "ng:math:category_62": 4601, "ng:math:lambda_62": 4602, "ng:math:sum_63": 4603, "ng:math:integral_63": 4604, "ng:math:tensor_63": 4605, "ng:math:function_63": 4606, "ng:math:topology_63": 4607, "ng:math:category_63": 4608, "ng:math:lambda_63": 4609, "ng:math:sum_64": 4610, "ng:math:integral_64": 4611, "ng:math:tensor_64": 4612, "ng:math:matrix_64": 4613, "ng:math:function_64": 4614, "ng:math:topology_64": 4615, "ng:math:category_64": 4616, "ng:math:lambda_64": 4617, "ng:math:sum_65": 4618, "ng:math:integral_65": 4619, "ng:math:tensor_65": 4620, "ng:math:matrix_65": 4621, "ng:math:function_65": 4622, "ng:math:topology_65": 4623, "ng:math:category_65": 4624, "ng:math:lambda_65": 4625, "ng:math:sum_66": 4626, "ng:math:integral_66": 4627, "ng:math:tensor_66": 4628, "ng:math:matrix_66": 4629, "ng:math:function_66": 4630, "ng:math:topology_66": 4631, "ng:math:category_66": 4632, "ng:math:lambda_66": 4633, "ng:math:sum_67": 4634, "ng:math:integral_67": 4635, "ng:math:tensor_67": 4636, "ng:math:matrix_67": 4637, "ng:math:function_67": 4638, "ng:math:topology_67": 4639, "ng:math:category_67": 4640, "ng:math:lambda_67": 4641, "ng:math:sum_68": 4642, "ng:math:integral_68": 4643, "ng:math:tensor_68": 4644, "ng:math:matrix_68": 4645, "ng:math:topology_68": 4646, "ng:math:category_68": 4647, "ng:math:lambda_68": 4648, "ng:math:sum_69": 4649, "ng:math:integral_69": 4650, "ng:math:tensor_69": 4651, "ng:math:matrix_69": 4652, "ng:math:function_69": 4653, "ng:math:topology_69": 4654, "ng:math:category_69": 4655, "ng:math:lambda_69": 4656, "ng:math:sum_70": 4657, "ng:math:integral_70": 4658, "ng:math:tensor_70": 4659, "ng:math:matrix_70": 4660, "ng:math:function_70": 4661, "ng:math:topology_70": 4662, "ng:math:category_70": 4663, "ng:math:lambda_70": 4664, "ng:math:sum_71": 4665, "ng:math:integral_71": 4666, "ng:math:tensor_71": 4667, "ng:math:matrix_71": 4668, "ng:math:function_71": 4669, "ng:math:topology_71": 4670, "ng:math:category_71": 4671, "ng:math:lambda_71": 4672, "ng:math:sum_72": 4673, "ng:math:integral_72": 4674, "ng:math:tensor_72": 4675, "ng:math:matrix_72": 4676, "ng:math:function_72": 4677, "ng:math:topology_72": 4678, "ng:math:category_72": 4679, "ng:math:lambda_72": 4680, "ng:math:sum_73": 4681, "ng:math:integral_73": 4682, "ng:math:tensor_73": 4683, "ng:math:matrix_73": 4684, "ng:math:function_73": 4685, "ng:math:topology_73": 4686, "ng:math:category_73": 4687, "ng:math:lambda_73": 4688, "ng:math:sum_74": 4689, "ng:math:integral_74": 4690, "ng:math:tensor_74": 4691, "ng:math:matrix_74": 4692, "ng:math:function_74": 4693, "ng:math:topology_74": 4694, "ng:math:category_74": 4695, "ng:math:lambda_74": 4696, "ng:math:sum_75": 4697, "ng:math:integral_75": 4698, "ng:math:tensor_75": 4699, "ng:math:matrix_75": 4700, "ng:math:function_75": 4701, "ng:math:topology_75": 4702, "ng:math:category_75": 4703, "ng:math:lambda_75": 4704, "ng:math:sum_76": 4705, "ng:math:integral_76": 4706, "ng:math:tensor_76": 4707, "ng:math:matrix_76": 4708, "ng:math:function_76": 4709, "ng:math:topology_76": 4710, "ng:math:category_76": 4711, "ng:math:lambda_76": 4712, "ng:math:sum_77": 4713, "ng:math:integral_77": 4714, "ng:math:tensor_77": 4715, "ng:math:matrix_77": 4716, "ng:math:function_77": 4717, "ng:math:topology_77": 4718, "ng:math:category_77": 4719, "ng:math:lambda_77": 4720, "ng:math:sum_78": 4721, "ng:math:integral_78": 4722, "ng:math:tensor_78": 4723, "ng:math:matrix_78": 4724, "ng:math:function_78": 4725, "ng:math:topology_78": 4726, "ng:math:category_78": 4727, "ng:math:lambda_78": 4728, "ng:math:sum_79": 4729, "ng:math:integral_79": 4730, "ng:math:tensor_79": 4731, "ng:math:matrix_79": 4732, "ng:math:function_79": 4733, "ng:math:topology_79": 4734, "ng:math:category_79": 4735, "ng:math:lambda_79": 4736, "ng:math:sum_80": 4737, "ng:math:integral_80": 4738, "ng:math:tensor_80": 4739, "ng:math:matrix_80": 4740, "ng:math:function_80": 4741, "ng:math:topology_80": 4742, "ng:math:category_80": 4743, "ng:math:lambda_80": 4744, "ng:math:sum_81": 4745, "ng:math:integral_81": 4746, "ng:math:tensor_81": 4747, "ng:math:matrix_81": 4748, "ng:math:function_81": 4749, "ng:math:topology_81": 4750, "ng:math:category_81": 4751, "ng:math:lambda_81": 4752, "ng:math:sum_82": 4753, "ng:math:integral_82": 4754, "ng:math:tensor_82": 4755, "ng:math:matrix_82": 4756, "ng:math:function_82": 4757, "ng:math:topology_82": 4758, "ng:math:lambda_82": 4759, "ng:math:sum_83": 4760, "ng:math:integral_83": 4761, "ng:math:tensor_83": 4762, "ng:math:matrix_83": 4763, "ng:math:function_83": 4764, "ng:math:topology_83": 4765, "ng:math:category_83": 4766, "ng:math:lambda_83": 4767, "ng:math:sum_84": 4768, "ng:math:integral_84": 4769, "ng:math:tensor_84": 4770, "ng:math:matrix_84": 4771, "ng:math:function_84": 4772, "ng:math:topology_84": 4773, "ng:math:category_84": 4774, "ng:math:lambda_84": 4775, "ng:math:sum_85": 4776, "ng:math:integral_85": 4777, "ng:math:tensor_85": 4778, "ng:math:matrix_85": 4779, "ng:math:function_85": 4780, "ng:math:topology_85": 4781, "ng:math:category_85": 4782, "ng:math:lambda_85": 4783, "ng:math:sum_86": 4784, "ng:math:integral_86": 4785, "ng:math:tensor_86": 4786, "ng:math:matrix_86": 4787, "ng:math:function_86": 4788, "ng:math:topology_86": 4789, "ng:math:category_86": 4790, "ng:math:lambda_86": 4791, "ng:math:sum_87": 4792, "ng:math:integral_87": 4793, "ng:math:tensor_87": 4794, "ng:math:matrix_87": 4795, "ng:math:function_87": 4796, "ng:math:topology_87": 4797, "ng:math:category_87": 4798, "ng:math:lambda_87": 4799, "ℌ": 4800, "⏿": 4801, "↳": 4802, "⇅": 4803, "⎪": 4804, "⪮": 4805, "⪭": 4806, "⎩": 4807, "⊣": 4808, "⮰": 4809, "⪬": 4810, "➧": 4811, "⫶": 4812, "⏢": 4813, "⎣": 4814, "⊫": 4815, "⎫": 4816, "➩": 4817, "➯": 4818, "⥲": 4819, "⮌": 4820, "⮩": 4821, "⮫": 4822, "⮮": 4823, "⎤": 4824, "➣": 4825, "⎦": 4826, "❮": 4827, "➥": 4828, "➦": 4829, "⮥": 4830, "⮦": 4831, "⎨": 4832, "❰": 4833, "➨": 4834, "⮧": 4835, "⭽": 4836, "⧼": 4837, "⩩": 4838, "⮤": 4839, "◢": 4840, "◼": 4841, "▶": 4842, "■": 4843, "◥": 4844, "◒": 4845, "⦻": 4846, "⨐": 4847, "∱": 4848, "❌": 4849, "⋓": 4850, "⬇": 4851, "✉": 4852, "⨙": 4853, "◙": 4854, "⩚": 4855, "⨀": 4856, "✟": 4857, "⦆": 4858, "⨽": 4859, "⬃": 4860, "◧": 4861, "▥": 4862, "⫗": 4863, "⩅": 4864, "◸": 4865, "✑": 4866, "❔": 4867, "□": 4868, "✍": 4869, "⨟": 4870, "⦁": 4871, "⤀": 4872, "⭝": 4873, "⨮": 4874, "≹": 4875, "▨": 4876, "⭊": 4877, "▴": 4878, "⭂": 4879, "⤄": 4880, "◟": 4881, "◝": 4882, "◞": 4883, "◮": 4884, "▵": 4885, "⍍": 4886, "⍟": 4887, "⋈": 4888, "⠳": 4889, "⢅": 4890, "⡩": 4891, "⢉": 4892, "⣀": 4893, "∋": 4894, "⒐": 4895, "—": 4896, " ": 4897, "⋟": 4898, "⋧": 4899, "∞": 4900, "Ꜳ": 4901, "ₔ": 4902, "𝛂": 4903, "𝐺": 4904, "𝑎": 4905, "⒙": 4906, "⒦": 4907, "⸩": 4908, "⋊": 4909, "⊿": 4910, "ↇ": 4911, "Ⅴ": 4912, "Ⅾ": 4913, "Ⅰ": 4914, "℠": 4915, "ⅼ": 4916, "⋤": 4917, "⊞": 4918, "≛": 4919, "⌯": 4920, " ": 4921, "⅝": 4922, "⋿": 4923, "𝝘": 4924, "ꭖ": 4925, "ⅽ": 4926, "𝕒": 4927, "𝜁": 4928, "𝞽": 4929, "ꬾ": 4930, "𝛺": 4931, "ꭞ": 4932, "𝗗": 4933, "𝗰": 4934, "𝚎": 4935, "ꟳ": 4936, "𝞤": 4937, "ng:meta:reflection": 4938, "ng:meta:metaprogramming": 4939, "ng:meta:composition": 4940, "ng:meta:reflection_1": 4941, "ng:meta:composition_1": 4942, "ng:meta:introspection_2": 4943, "ng:meta:abstraction_2": 4944, "ng:meta:composition_2": 4945, "ng:meta:inheritance_2": 4946, "ng:meta:metaprogramming_3": 4947, "ng:meta:abstraction_3": 4948, "ng:meta:inheritance_3": 4949, "ng:meta:introspection_4": 4950, "ng:meta:metaprogramming_4": 4951, "ng:meta:composition_4": 4952, "ng:meta:inheritance_4": 4953, "ng:meta:polymorphism_4": 4954, "ng:meta:reflection_5": 4955, "ng:meta:metaprogramming_5": 4956, "ng:meta:inheritance_5": 4957, "ng:meta:reflection_6": 4958, "ng:meta:abstraction_6": 4959, "ng:meta:reflection_7": 4960, "ng:meta:introspection_7": 4961, "ng:meta:metaprogramming_7": 4962, "ng:meta:reflection_8": 4963, "ng:meta:composition_8": 4964, "ng:meta:inheritance_8": 4965, "ng:meta:reflection_9": 4966, "ng:meta:introspection_9": 4967, "ng:meta:metaprogramming_9": 4968, "ng:meta:reflection_10": 4969, "ng:meta:introspection_10": 4970, "ng:meta:inheritance_10": 4971, "ng:meta:encapsulation_10": 4972, "ng:meta:metaprogramming_11": 4973, "ng:meta:abstraction_11": 4974, "ng:meta:composition_11": 4975, "ng:meta:inheritance_11": 4976, "ng:meta:polymorphism_11": 4977, "ng:meta:encapsulation_11": 4978, "ng:meta:reflection_12": 4979, "ng:meta:composition_12": 4980, "ng:meta:reflection_13": 4981, "ng:meta:introspection_13": 4982, "ng:meta:metaprogramming_13": 4983, "ng:meta:abstraction_13": 4984, "ng:meta:composition_13": 4985, "ng:meta:reflection_14": 4986, "ng:meta:introspection_14": 4987, "ng:meta:abstraction_14": 4988, "ng:meta:composition_14": 4989, "ng:meta:inheritance_14": 4990, "ng:meta:polymorphism_14": 4991, "ng:meta:encapsulation_14": 4992, "ng:meta:reflection_15": 4993, "ng:meta:metaprogramming_15": 4994, "ng:meta:abstraction_15": 4995, "ng:meta:composition_15": 4996, "ng:meta:inheritance_15": 4997, "ng:meta:polymorphism_15": 4998, "ng:meta:reflection_16": 4999, "ng:meta:abstraction_16": 5000, "ng:meta:inheritance_16": 5001, "ng:meta:polymorphism_16": 5002, "ng:meta:encapsulation_16": 5003, "ng:meta:reflection_17": 5004, "ng:meta:metaprogramming_17": 5005, "ng:meta:abstraction_17": 5006, "ng:meta:inheritance_17": 5007, "ng:meta:reflection_18": 5008, "ng:meta:metaprogramming_18": 5009, "ng:meta:abstraction_18": 5010, "ng:meta:inheritance_18": 5011, "ng:meta:polymorphism_18": 5012, "ng:meta:encapsulation_18": 5013, "ng:meta:introspection_19": 5014, "ng:meta:metaprogramming_19": 5015, "ng:meta:abstraction_19": 5016, "ng:meta:inheritance_19": 5017, "ng:meta:encapsulation_19": 5018, "ng:meta:metaprogramming_20": 5019, "ng:meta:composition_20": 5020, "ng:meta:encapsulation_20": 5021, "ng:meta:reflection_21": 5022, "ng:meta:introspection_21": 5023, "ng:meta:abstraction_21": 5024, "ng:meta:composition_21": 5025, "ng:meta:polymorphism_21": 5026, "ng:meta:reflection_24": 5027, "ng:meta:introspection_24": 5028, "ng:meta:metaprogramming_24": 5029, "ng:meta:abstraction_24": 5030, "ng:meta:composition_24": 5031, "ng:meta:inheritance_24": 5032, "ng:meta:polymorphism_24": 5033, "ng:meta:encapsulation_24": 5034, "ng:meta:reflection_25": 5035, "ng:meta:introspection_25": 5036, "ng:meta:metaprogramming_25": 5037, "ng:meta:abstraction_25": 5038, "ng:meta:composition_25": 5039, "ng:meta:inheritance_25": 5040, "ng:meta:polymorphism_25": 5041, "ng:meta:encapsulation_25": 5042, "ng:meta:reflection_26": 5043, "ng:meta:introspection_26": 5044, "ng:meta:metaprogramming_26": 5045, "ng:meta:abstraction_26": 5046, "ng:meta:composition_26": 5047, "ng:meta:inheritance_26": 5048, "ng:meta:polymorphism_26": 5049, "ng:meta:encapsulation_26": 5050, "ng:meta:reflection_27": 5051, "ng:meta:introspection_27": 5052, "ng:meta:metaprogramming_27": 5053, "ng:meta:abstraction_27": 5054, "ng:meta:composition_27": 5055, "ng:meta:inheritance_27": 5056, "ng:meta:polymorphism_27": 5057, "ng:meta:encapsulation_27": 5058, "ng:meta:reflection_28": 5059, "ng:meta:introspection_28": 5060, "ng:meta:metaprogramming_28": 5061, "ng:meta:abstraction_28": 5062, "ng:meta:composition_28": 5063, "ng:meta:inheritance_28": 5064, "ng:meta:polymorphism_28": 5065, "ng:meta:encapsulation_28": 5066, "ng:meta:reflection_29": 5067, "ng:meta:introspection_29": 5068, "ng:meta:metaprogramming_29": 5069, "ng:meta:abstraction_29": 5070, "ng:meta:composition_29": 5071, "ng:meta:inheritance_29": 5072, "ng:meta:polymorphism_29": 5073, "ng:meta:encapsulation_29": 5074, "ng:meta:reflection_30": 5075, "ng:meta:introspection_30": 5076, "ng:meta:metaprogramming_30": 5077, "ng:meta:abstraction_30": 5078, "ng:meta:composition_30": 5079, "ng:meta:inheritance_30": 5080, "ng:meta:polymorphism_30": 5081, "ng:meta:encapsulation_30": 5082, "ng:meta:reflection_31": 5083, "ng:meta:introspection_31": 5084, "ng:meta:metaprogramming_31": 5085, "ng:meta:abstraction_31": 5086, "ng:meta:composition_31": 5087, "ng:meta:inheritance_31": 5088, "ng:meta:polymorphism_31": 5089, "ng:meta:encapsulation_31": 5090, "ng:meta:reflection_32": 5091, "ng:meta:introspection_32": 5092, "ng:meta:metaprogramming_32": 5093, "ng:meta:abstraction_32": 5094, "ng:meta:composition_32": 5095, "ng:meta:inheritance_32": 5096, "ng:meta:polymorphism_32": 5097, "ng:meta:encapsulation_32": 5098, "ng:meta:reflection_33": 5099, "ng:meta:introspection_33": 5100, "ng:meta:metaprogramming_33": 5101, "ng:meta:abstraction_33": 5102, "ng:meta:composition_33": 5103, "ng:meta:inheritance_33": 5104, "ng:meta:polymorphism_33": 5105, "ng:meta:encapsulation_33": 5106, "ng:meta:reflection_34": 5107, "ng:meta:introspection_34": 5108, "ng:meta:metaprogramming_34": 5109, "ng:meta:abstraction_34": 5110, "ng:meta:composition_34": 5111, "ng:meta:inheritance_34": 5112, "ng:meta:polymorphism_34": 5113, "ng:meta:encapsulation_34": 5114, "ng:meta:reflection_35": 5115, "ng:meta:introspection_35": 5116, "ng:meta:metaprogramming_35": 5117, "ng:meta:abstraction_35": 5118, "ng:meta:composition_35": 5119, "ng:meta:inheritance_35": 5120, "ng:meta:polymorphism_35": 5121, "ng:meta:encapsulation_35": 5122, "ng:meta:reflection_36": 5123, "ng:meta:introspection_36": 5124, "ng:meta:metaprogramming_36": 5125, "ng:meta:abstraction_36": 5126, "ng:meta:composition_36": 5127, "ng:meta:inheritance_36": 5128, "ng:meta:polymorphism_36": 5129, "ng:meta:encapsulation_36": 5130, "ng:meta:reflection_37": 5131, "ng:meta:introspection_37": 5132, "ng:meta:metaprogramming_37": 5133, "ng:meta:abstraction_37": 5134, "ng:meta:composition_37": 5135, "ng:meta:inheritance_37": 5136, "ng:meta:polymorphism_37": 5137, "ng:meta:encapsulation_37": 5138, "ng:meta:reflection_38": 5139, "ng:meta:introspection_38": 5140, "ng:meta:metaprogramming_38": 5141, "ng:meta:abstraction_38": 5142, "ng:meta:composition_38": 5143, "ng:meta:inheritance_38": 5144, "ng:meta:polymorphism_38": 5145, "ng:meta:encapsulation_38": 5146, "ng:meta:reflection_39": 5147, "ng:meta:introspection_39": 5148, "ng:meta:metaprogramming_39": 5149, "ng:meta:abstraction_39": 5150, "ng:meta:composition_39": 5151, "ng:meta:inheritance_39": 5152, "ng:meta:polymorphism_39": 5153, "ng:meta:encapsulation_39": 5154, "ng:meta:reflection_40": 5155, "ng:meta:introspection_40": 5156, "ng:meta:metaprogramming_40": 5157, "ng:meta:abstraction_40": 5158, "ng:meta:composition_40": 5159, "ng:meta:inheritance_40": 5160, "ng:meta:polymorphism_40": 5161, "ng:meta:encapsulation_40": 5162, "ng:meta:reflection_41": 5163, "ng:meta:introspection_41": 5164, "ng:meta:metaprogramming_41": 5165, "ng:meta:composition_41": 5166, "ng:meta:inheritance_41": 5167, "ng:meta:polymorphism_41": 5168, "ng:meta:encapsulation_41": 5169, "ng:meta:reflection_42": 5170, "ng:meta:introspection_42": 5171, "ng:meta:metaprogramming_42": 5172, "ng:meta:abstraction_42": 5173, "ng:meta:composition_42": 5174, "ng:meta:inheritance_42": 5175, "ng:meta:polymorphism_42": 5176, "ng:meta:encapsulation_42": 5177, "ng:meta:reflection_43": 5178, "ng:meta:introspection_43": 5179, "ng:meta:metaprogramming_43": 5180, "ng:meta:abstraction_43": 5181, "ng:meta:composition_43": 5182, "ng:meta:inheritance_43": 5183, "ng:meta:polymorphism_43": 5184, "ng:meta:encapsulation_43": 5185, "ng:meta:reflection_44": 5186, "ng:meta:introspection_44": 5187, "ng:meta:metaprogramming_44": 5188, "ng:meta:abstraction_44": 5189, "ng:meta:composition_44": 5190, "ng:meta:inheritance_44": 5191, "ng:meta:polymorphism_44": 5192, "ng:meta:encapsulation_44": 5193, "ng:meta:reflection_45": 5194, "ng:meta:introspection_45": 5195, "ng:meta:metaprogramming_45": 5196, "ng:meta:abstraction_45": 5197, "ng:meta:composition_45": 5198, "ng:meta:inheritance_45": 5199, "ng:meta:polymorphism_45": 5200, "ng:meta:encapsulation_45": 5201, "ng:meta:reflection_46": 5202, "ng:meta:introspection_46": 5203, "ng:meta:metaprogramming_46": 5204, "ng:meta:abstraction_46": 5205, "ng:meta:composition_46": 5206, "ng:meta:inheritance_46": 5207, "ng:meta:polymorphism_46": 5208, "ng:meta:encapsulation_46": 5209, "ng:meta:reflection_47": 5210, "ng:meta:introspection_47": 5211, "ng:meta:metaprogramming_47": 5212, "ng:meta:abstraction_47": 5213, "ng:meta:composition_47": 5214, "ng:meta:inheritance_47": 5215, "ng:meta:polymorphism_47": 5216, "ng:meta:encapsulation_47": 5217, "ng:meta:reflection_48": 5218, "ng:meta:introspection_48": 5219, "ng:meta:metaprogramming_48": 5220, "ng:meta:abstraction_48": 5221, "ng:meta:composition_48": 5222, "ng:meta:inheritance_48": 5223, "ng:meta:polymorphism_48": 5224, "ng:meta:encapsulation_48": 5225, "ng:meta:reflection_49": 5226, "ng:meta:introspection_49": 5227, "ng:meta:metaprogramming_49": 5228, "ng:meta:abstraction_49": 5229, "ng:meta:composition_49": 5230, "ng:meta:inheritance_49": 5231, "ng:meta:polymorphism_49": 5232, "ng:meta:encapsulation_49": 5233, "ng:meta:reflection_50": 5234, "ng:meta:introspection_50": 5235, "ng:meta:metaprogramming_50": 5236, "ng:meta:abstraction_50": 5237, "ng:meta:composition_50": 5238, "ng:meta:inheritance_50": 5239, "ng:meta:polymorphism_50": 5240, "ng:meta:encapsulation_50": 5241, "ng:meta:reflection_51": 5242, "ng:meta:introspection_51": 5243, "ng:meta:metaprogramming_51": 5244, "ng:meta:abstraction_51": 5245, "ng:meta:composition_51": 5246, "ng:meta:inheritance_51": 5247, "ng:meta:polymorphism_51": 5248, "ng:meta:encapsulation_51": 5249, "ng:meta:reflection_52": 5250, "ng:meta:introspection_52": 5251, "ng:meta:metaprogramming_52": 5252, "ng:meta:abstraction_52": 5253, "ng:meta:composition_52": 5254, "ng:meta:inheritance_52": 5255, "ng:meta:polymorphism_52": 5256, "ng:meta:encapsulation_52": 5257, "ng:meta:reflection_53": 5258, "ng:meta:introspection_53": 5259, "ng:meta:metaprogramming_53": 5260, "ng:meta:abstraction_53": 5261, "ng:meta:composition_53": 5262, "ng:meta:inheritance_53": 5263, "ng:meta:polymorphism_53": 5264, "ng:meta:encapsulation_53": 5265, "ng:meta:reflection_54": 5266, "ng:meta:introspection_54": 5267, "ng:meta:metaprogramming_54": 5268, "ng:meta:abstraction_54": 5269, "ng:meta:composition_54": 5270, "ng:meta:inheritance_54": 5271, "ng:meta:polymorphism_54": 5272, "ng:meta:encapsulation_54": 5273, "ng:meta:reflection_55": 5274, "ng:meta:introspection_55": 5275, "ng:meta:metaprogramming_55": 5276, "ng:meta:abstraction_55": 5277, "ng:meta:composition_55": 5278, "ng:meta:inheritance_55": 5279, "ng:meta:polymorphism_55": 5280, "ng:meta:encapsulation_55": 5281, "ng:meta:reflection_56": 5282, "ng:meta:introspection_56": 5283, "ng:meta:metaprogramming_56": 5284, "ng:meta:abstraction_56": 5285, "ng:meta:composition_56": 5286, "ng:meta:inheritance_56": 5287, "ng:meta:polymorphism_56": 5288, "ng:meta:encapsulation_56": 5289, "ng:meta:reflection_57": 5290, "ng:meta:introspection_57": 5291, "ng:meta:metaprogramming_57": 5292, "ng:meta:abstraction_57": 5293, "ng:meta:composition_57": 5294, "ng:meta:inheritance_57": 5295, "ng:meta:polymorphism_57": 5296, "ng:meta:encapsulation_57": 5297, "ng:meta:reflection_58": 5298, "ng:meta:introspection_58": 5299, "ng:meta:metaprogramming_58": 5300, "ng:meta:abstraction_58": 5301, "ng:meta:composition_58": 5302, "ng:meta:inheritance_58": 5303, "ng:meta:polymorphism_58": 5304, "ng:meta:encapsulation_58": 5305, "ng:meta:reflection_59": 5306, "ng:meta:introspection_59": 5307, "ng:meta:metaprogramming_59": 5308, "ng:meta:abstraction_59": 5309, "ng:meta:composition_59": 5310, "ng:meta:inheritance_59": 5311, "ng:meta:polymorphism_59": 5312, "ng:meta:encapsulation_59": 5313, "ng:meta:reflection_60": 5314, "ng:meta:introspection_60": 5315, "ng:meta:metaprogramming_60": 5316, "ng:meta:abstraction_60": 5317, "ng:meta:composition_60": 5318, "ng:meta:inheritance_60": 5319, "ng:meta:polymorphism_60": 5320, "ng:meta:encapsulation_60": 5321, "ng:meta:reflection_61": 5322, "ng:meta:introspection_61": 5323, "ng:meta:metaprogramming_61": 5324, "ng:meta:abstraction_61": 5325, "ng:meta:composition_61": 5326, "ng:meta:inheritance_61": 5327, "ng:meta:polymorphism_61": 5328, "ng:meta:encapsulation_61": 5329, "ng:meta:reflection_62": 5330, "ng:meta:introspection_62": 5331, "ng:meta:metaprogramming_62": 5332, "ng:meta:abstraction_62": 5333, "ng:meta:composition_62": 5334, "ng:meta:inheritance_62": 5335, "ng:meta:polymorphism_62": 5336, "ng:meta:encapsulation_62": 5337, "ng:meta:reflection_63": 5338, "ng:meta:introspection_63": 5339, "ng:meta:metaprogramming_63": 5340, "ng:meta:abstraction_63": 5341, "ng:meta:composition_63": 5342, "ng:meta:inheritance_63": 5343, "ng:meta:polymorphism_63": 5344, "ng:meta:encapsulation_63": 5345, "ng:meta:reflection_64": 5346, "ng:meta:introspection_64": 5347, "ng:meta:metaprogramming_64": 5348, "ng:meta:abstraction_64": 5349, "ng:meta:composition_64": 5350, "ng:meta:inheritance_64": 5351, "ng:meta:polymorphism_64": 5352, "ng:meta:encapsulation_64": 5353, "ng:meta:reflection_65": 5354, "ng:meta:introspection_65": 5355, "ng:meta:metaprogramming_65": 5356, "ng:meta:abstraction_65": 5357, "ng:meta:composition_65": 5358, "ng:meta:inheritance_65": 5359, "ng:meta:polymorphism_65": 5360, "ng:meta:encapsulation_65": 5361, "ng:meta:reflection_66": 5362, "ng:meta:introspection_66": 5363, "ng:meta:metaprogramming_66": 5364, "ng:meta:abstraction_66": 5365, "ng:meta:composition_66": 5366, "ng:meta:inheritance_66": 5367, "ng:meta:polymorphism_66": 5368, "ng:meta:encapsulation_66": 5369, "ng:meta:reflection_67": 5370, "ng:meta:introspection_67": 5371, "ng:meta:metaprogramming_67": 5372, "ng:meta:abstraction_67": 5373, "ng:meta:composition_67": 5374, "ng:meta:inheritance_67": 5375, "ng:meta:polymorphism_67": 5376, "ng:meta:encapsulation_67": 5377, "ng:meta:reflection_68": 5378, "ng:meta:introspection_68": 5379, "ng:meta:metaprogramming_68": 5380, "ng:meta:abstraction_68": 5381, "ng:meta:composition_68": 5382, "ng:meta:inheritance_68": 5383, "ng:meta:polymorphism_68": 5384, "ng:meta:encapsulation_68": 5385, "ng:meta:reflection_69": 5386, "ng:meta:introspection_69": 5387, "ng:meta:metaprogramming_69": 5388, "ng:meta:abstraction_69": 5389, "ng:meta:composition_69": 5390, "ng:meta:inheritance_69": 5391, "ng:meta:polymorphism_69": 5392, "ng:meta:encapsulation_69": 5393, "ng:meta:reflection_70": 5394, "ng:meta:introspection_70": 5395, "ng:meta:metaprogramming_70": 5396, "ng:meta:abstraction_70": 5397, "ng:meta:composition_70": 5398, "ng:meta:inheritance_70": 5399, "ng:meta:polymorphism_70": 5400, "ng:meta:encapsulation_70": 5401, "ng:meta:reflection_71": 5402, "ng:meta:introspection_71": 5403, "ng:meta:metaprogramming_71": 5404, "ng:meta:abstraction_71": 5405, "ng:meta:composition_71": 5406, "ng:meta:inheritance_71": 5407, "ng:meta:polymorphism_71": 5408, "ng:meta:encapsulation_71": 5409, "ng:meta:reflection_72": 5410, "ng:meta:introspection_72": 5411, "ng:meta:metaprogramming_72": 5412, "ng:meta:abstraction_72": 5413, "ng:meta:composition_72": 5414, "ng:meta:inheritance_72": 5415, "ng:meta:polymorphism_72": 5416, "ng:meta:encapsulation_72": 5417, "ng:meta:reflection_73": 5418, "ng:meta:introspection_73": 5419, "ng:meta:metaprogramming_73": 5420, "ng:meta:abstraction_73": 5421, "ng:meta:composition_73": 5422, "ng:meta:inheritance_73": 5423, "ng:meta:polymorphism_73": 5424, "ng:meta:encapsulation_73": 5425, "ng:meta:reflection_74": 5426, "ng:meta:introspection_74": 5427, "ng:meta:metaprogramming_74": 5428, "ng:meta:abstraction_74": 5429, "ng:meta:composition_74": 5430, "ng:meta:inheritance_74": 5431, "ng:meta:polymorphism_74": 5432, "ng:meta:encapsulation_74": 5433, "ng:meta:reflection_75": 5434, "ng:meta:introspection_75": 5435, "ng:meta:metaprogramming_75": 5436, "ng:meta:abstraction_75": 5437, "ng:meta:composition_75": 5438, "ng:meta:inheritance_75": 5439, "ng:meta:polymorphism_75": 5440, "ng:meta:encapsulation_75": 5441, "ng:meta:reflection_76": 5442, "ng:meta:introspection_76": 5443, "ng:meta:metaprogramming_76": 5444, "ng:meta:abstraction_76": 5445, "ng:meta:composition_76": 5446, "ng:meta:inheritance_76": 5447, "ng:meta:polymorphism_76": 5448, "ng:meta:encapsulation_76": 5449, "ng:meta:reflection_77": 5450, "ng:meta:introspection_77": 5451, "ng:meta:metaprogramming_77": 5452, "ng:meta:abstraction_77": 5453, "ng:meta:composition_77": 5454, "ng:meta:inheritance_77": 5455, "ng:meta:polymorphism_77": 5456, "ng:meta:encapsulation_77": 5457, "ng:meta:reflection_78": 5458, "ng:meta:introspection_78": 5459, "ng:meta:metaprogramming_78": 5460, "ng:meta:abstraction_78": 5461, "ng:meta:composition_78": 5462, "ng:meta:inheritance_78": 5463, "ng:meta:polymorphism_78": 5464, "ng:meta:encapsulation_78": 5465, "ng:meta:reflection_79": 5466, "ng:meta:introspection_79": 5467, "ng:meta:metaprogramming_79": 5468, "ng:meta:abstraction_79": 5469, "ng:meta:composition_79": 5470, "ng:meta:inheritance_79": 5471, "ng:meta:polymorphism_79": 5472, "ng:meta:encapsulation_79": 5473, "ng:meta:reflection_80": 5474, "ng:meta:introspection_80": 5475, "ng:meta:metaprogramming_80": 5476, "ng:meta:abstraction_80": 5477, "ng:meta:composition_80": 5478, "ng:meta:inheritance_80": 5479, "ng:meta:polymorphism_80": 5480, "ng:meta:encapsulation_80": 5481, "ng:meta:reflection_81": 5482, "ng:meta:introspection_81": 5483, "ng:meta:metaprogramming_81": 5484, "ng:meta:abstraction_81": 5485, "ng:meta:composition_81": 5486, "ng:meta:inheritance_81": 5487, "ng:meta:polymorphism_81": 5488, "ng:meta:encapsulation_81": 5489, "ng:meta:reflection_82": 5490, "ng:meta:introspection_82": 5491, "ng:meta:metaprogramming_82": 5492, "ng:meta:abstraction_82": 5493, "ng:meta:composition_82": 5494, "ng:meta:inheritance_82": 5495, "ng:meta:polymorphism_82": 5496, "ng:meta:encapsulation_82": 5497, "ng:meta:reflection_83": 5498, "ng:meta:introspection_83": 5499, "ng:meta:metaprogramming_83": 5500, "ng:meta:abstraction_83": 5501, "ng:meta:composition_83": 5502, "ng:meta:inheritance_83": 5503, "ng:meta:polymorphism_83": 5504, "ng:meta:encapsulation_83": 5505, "ng:meta:reflection_84": 5506, "ng:meta:introspection_84": 5507, "ng:meta:metaprogramming_84": 5508, "ng:meta:abstraction_84": 5509, "ng:meta:composition_84": 5510, "ng:meta:inheritance_84": 5511, "ng:meta:polymorphism_84": 5512, "ng:meta:encapsulation_84": 5513, "ng:meta:reflection_85": 5514, "ng:meta:introspection_85": 5515, "ng:meta:metaprogramming_85": 5516, "ng:meta:abstraction_85": 5517, "ng:meta:composition_85": 5518, "ng:meta:inheritance_85": 5519, "ng:meta:polymorphism_85": 5520, "ng:meta:encapsulation_85": 5521, "ng:meta:reflection_86": 5522, "ng:meta:introspection_86": 5523, "ng:meta:metaprogramming_86": 5524, "ng:meta:abstraction_86": 5525, "ng:meta:composition_86": 5526, "ng:meta:inheritance_86": 5527, "ng:meta:polymorphism_86": 5528, "ng:meta:encapsulation_86": 5529, "𝟈": 5530, "𝜬": 5531, "𝘆": 5532, "𝒒": 5533, "𝙺": 5534, "𝟆": 5535, "≌": 5536, "❖": 5537, "⬬": 5538, "⧑": 5539, "⩌": 5540, "⬘": 5541, "⬚": 5542, "⩏": 5543, "❈": 5544, "⯚": 5545, "⩟": 5546, "❎": 5547, "⤣": 5548, "⧛": 5549, "⭏": 5550, "⭎": 5551, "⧈": 5552, "⧋": 5553, "✌": 5554, "⥋": 5555, "⥒": 5556, "⨷": 5557, "⩣": 5558, "⭍": 5559, "✽": 5560, "⥅": 5561, "⥌": 5562, "⦭": 5563, "⧣": 5564, "⩋": 5565, "⩙": 5566, "⍏": 5567, "⤔": 5568, "⧏": 5569, "⍢": 5570, "❒": 5571, "❢": 5572, "❥": 5573, "⤡": 5574, "⧥": 5575, "⩝": 5576, "⍎": 5577, "❁": 5578, "⮘": 5579, "⮜": 5580, "ℷ": 5581, "↖": 5582, "ℓ": 5583, "↨": 5584, "↺": 5585, "⇌": 5586, "⏦": 5587, "⍬": 5588, "⏣": 5589, "⧭": 5590, "⫪": 5591, "⫫": 5592, "⯰": 5593, "⯱": 5594, "⏥": 5595, "⏳": 5596, "⯣": 5597, "⮺": 5598, "⯧": 5599, "⯩": 5600, "⯲": 5601, "⫧": 5602, "⩬": 5603, "⯪": 5604, "⯫": 5605, "⏲": 5606, "⫷": 5607, "⫣": 5608, "⫤": 5609, "⯬": 5610, "⯭": 5611, "⯹": 5612, "⫹": 5613, "❭": 5614, "⥭": 5615, "⩫": 5616, "⭫": 5617, "⏶": 5618, "⫥": 5619, "⫭": 5620, "⏯": 5621, "⫰": 5622, "⍭": 5623, "⭪": 5624, "⭬": 5625, "⏭": 5626, "ℵ": 5627, "⅀": 5628, "↦": 5629, "⇮": 5630, "∗": 5631, "●": 5632, "✒": 5633, "✀": 5634, "▮": 5635, "∌": 5636, "≝": 5637, "∀": 5638, "✖": 5639, "⨜": 5640, "◯": 5641, "⌈": 5642, "⤛": 5643, "◺": 5644, "◿": 5645, "≞": 5646, "⦝": 5647, "⋂": 5648, "⨉": 5649, "⨃": 5650, "⬉": 5651, "⌖": 5652, "⨖": 5653, "✋": 5654, "⧙": 5655, "⤍": 5656, "⤏": 5657, "⨳": 5658, "⬓": 5659, "▤": 5660, "◨": 5661, "⧇": 5662, "⌕": 5663, "⤺": 5664, "⧊": 5665, "⬍": 5666, "◠": 5667, "▽": 5668, "▭": 5669, "▻": 5670, "△": 5671, "⦂": 5672, "⨄": 5673, "◴": 5674, "⌸": 5675, "⋲": 5676, "⨾": 5677, "⤉": 5678, "⤗": 5679, "⭇": 5680, "⦬": 5681, "◜": 5682, "✸": 5683, "▢": 5684, "◫": 5685, "◱": 5686, "⤂": 5687, "⨅": 5688, "⦊": 5689, "⤸": 5690, "▹": 5691, "◶": 5692, "⦔": 5693, "⨵": 5694, "❋": 5695, "⬔": 5696, "◬": 5697, "⥄": 5698, "℁": 5699, "⏰": 5700, "≊": 5701, "Å": 5702, "⍕": 5703, "⍡": 5704, "③": 5705, "⊬": 5706, "↲": 5707, "⇊": 5708, "₠": 5709, "⌢": 5710, "‧": 5711, "∆": 5712, "↫": 5713, "⊍": 5714, "∐": 5715, "≵": 5716, "↸": 5717, "⒤": 5718, "₱": 5719, "≺": 5720, "⁆": 5721, "→": 5722, "Ⅺ": 5723, "∖": 5724, "⋼": 5725, "⋷": 5726, "ⅿ": 5727, "⊆": 5728, "₸": 5729, " ": 5730, "⅐": 5731, "⅜": 5732, "⊻": 5733, "ng:performance:optimization": 5734, "ng:performance:caching": 5735, "ng:performance:parallelization": 5736, "ng:performance:caching_1": 5737, "ng:performance:parallelization_2": 5738, "ng:performance:benchmarking_3": 5739, "ng:performance:scaling_3": 5740, "ng:performance:tuning_3": 5741, "ng:performance:scaling_4": 5742, "ng:performance:tuning_4": 5743, "ng:performance:optimization_5": 5744, "ng:performance:vectorization_5": 5745, "ng:performance:benchmarking_5": 5746, "ng:performance:tuning_5": 5747, "ng:performance:optimization_6": 5748, "ng:performance:caching_6": 5749, "ng:performance:parallelization_6": 5750, "ng:performance:vectorization_6": 5751, "ng:performance:profiling_6": 5752, "ng:performance:benchmarking_6": 5753, "ng:performance:scaling_6": 5754, "ng:performance:tuning_6": 5755, "ng:performance:optimization_7": 5756, "ng:performance:vectorization_7": 5757, "ng:performance:scaling_7": 5758, "ng:performance:optimization_8": 5759, "ng:performance:parallelization_8": 5760, "ng:performance:vectorization_8": 5761, "ng:performance:benchmarking_8": 5762, "ng:performance:caching_9": 5763, "ng:performance:tuning_9": 5764, "ng:performance:caching_10": 5765, "ng:performance:parallelization_10": 5766, "ng:performance:profiling_10": 5767, "ng:performance:caching_11": 5768, "ng:performance:vectorization_11": 5769, "ng:performance:scaling_11": 5770, "ng:performance:tuning_11": 5771, "ng:performance:optimization_12": 5772, "ng:performance:benchmarking_12": 5773, "ng:performance:optimization_13": 5774, "ng:performance:benchmarking_13": 5775, "ng:performance:tuning_13": 5776, "ng:performance:parallelization_14": 5777, "ng:performance:profiling_14": 5778, "ng:performance:benchmarking_14": 5779, "ng:performance:tuning_14": 5780, "ng:performance:optimization_15": 5781, "ng:performance:caching_15": 5782, "ng:performance:parallelization_15": 5783, "ng:performance:scaling_15": 5784, "ng:performance:optimization_16": 5785, "ng:performance:caching_16": 5786, "ng:performance:parallelization_16": 5787, "ng:performance:vectorization_16": 5788, "ng:performance:profiling_16": 5789, "ng:performance:scaling_16": 5790, "ng:performance:optimization_17": 5791, "ng:performance:caching_17": 5792, "ng:performance:parallelization_17": 5793, "ng:performance:vectorization_17": 5794, "ng:performance:benchmarking_17": 5795, "ng:performance:tuning_17": 5796, "ng:performance:optimization_18": 5797, "ng:performance:parallelization_18": 5798, "ng:performance:profiling_18": 5799, "ng:performance:benchmarking_18": 5800, "ng:performance:scaling_18": 5801, "ng:performance:caching_19": 5802, "ng:performance:vectorization_19": 5803, "ng:performance:benchmarking_19": 5804, "ng:performance:scaling_19": 5805, "ng:performance:optimization_20": 5806, "ng:performance:parallelization_20": 5807, "ng:performance:benchmarking_20": 5808, "ng:performance:scaling_20": 5809, "ng:performance:optimization_21": 5810, "ng:performance:caching_21": 5811, "ng:performance:vectorization_21": 5812, "ng:performance:benchmarking_21": 5813, "ng:performance:scaling_21": 5814, "ng:performance:tuning_21": 5815, "ng:performance:benchmarking_23": 5816, "ng:performance:optimization_24": 5817, "ng:performance:caching_24": 5818, "ng:performance:parallelization_24": 5819, "ng:performance:vectorization_24": 5820, "ng:performance:profiling_24": 5821, "ng:performance:benchmarking_24": 5822, "ng:performance:scaling_24": 5823, "ng:performance:tuning_24": 5824, "ng:performance:optimization_25": 5825, "ng:performance:caching_25": 5826, "ng:performance:parallelization_25": 5827, "ng:performance:vectorization_25": 5828, "ng:performance:profiling_25": 5829, "ng:performance:benchmarking_25": 5830, "ng:performance:scaling_25": 5831, "ng:performance:tuning_25": 5832, "ng:performance:optimization_26": 5833, "ng:performance:caching_26": 5834, "ng:performance:parallelization_26": 5835, "ng:performance:vectorization_26": 5836, "ng:performance:profiling_26": 5837, "ng:performance:benchmarking_26": 5838, "ng:performance:scaling_26": 5839, "ng:performance:tuning_26": 5840, "ng:performance:optimization_27": 5841, "ng:performance:caching_27": 5842, "ng:performance:parallelization_27": 5843, "ng:performance:vectorization_27": 5844, "ng:performance:benchmarking_27": 5845, "ng:performance:scaling_27": 5846, "ng:performance:tuning_27": 5847, "ng:performance:optimization_28": 5848, "ng:performance:caching_28": 5849, "ng:performance:parallelization_28": 5850, "ng:performance:vectorization_28": 5851, "ng:performance:profiling_28": 5852, "ng:performance:benchmarking_28": 5853, "ng:performance:scaling_28": 5854, "ng:performance:tuning_28": 5855, "ng:performance:optimization_29": 5856, "ng:performance:caching_29": 5857, "ng:performance:parallelization_29": 5858, "ng:performance:vectorization_29": 5859, "ng:performance:profiling_29": 5860, "ng:performance:benchmarking_29": 5861, "ng:performance:scaling_29": 5862, "ng:performance:tuning_29": 5863, "ng:performance:optimization_30": 5864, "ng:performance:caching_30": 5865, "ng:performance:parallelization_30": 5866, "ng:performance:vectorization_30": 5867, "ng:performance:profiling_30": 5868, "ng:performance:benchmarking_30": 5869, "ng:performance:scaling_30": 5870, "ng:performance:tuning_30": 5871, "ng:performance:optimization_31": 5872, "ng:performance:caching_31": 5873, "ng:performance:parallelization_31": 5874, "ng:performance:vectorization_31": 5875, "ng:performance:profiling_31": 5876, "ng:performance:benchmarking_31": 5877, "ng:performance:scaling_31": 5878, "ng:performance:tuning_31": 5879, "ng:performance:optimization_32": 5880, "ng:performance:caching_32": 5881, "ng:performance:parallelization_32": 5882, "ng:performance:vectorization_32": 5883, "ng:performance:profiling_32": 5884, "ng:performance:benchmarking_32": 5885, "ng:performance:scaling_32": 5886, "ng:performance:tuning_32": 5887, "ng:performance:optimization_33": 5888, "ng:performance:caching_33": 5889, "ng:performance:parallelization_33": 5890, "ng:performance:vectorization_33": 5891, "ng:performance:profiling_33": 5892, "ng:performance:benchmarking_33": 5893, "ng:performance:scaling_33": 5894, "ng:performance:optimization_34": 5895, "ng:performance:caching_34": 5896, "ng:performance:parallelization_34": 5897, "ng:performance:vectorization_34": 5898, "ng:performance:profiling_34": 5899, "ng:performance:benchmarking_34": 5900, "ng:performance:scaling_34": 5901, "ng:performance:tuning_34": 5902, "ng:performance:optimization_35": 5903, "ng:performance:caching_35": 5904, "ng:performance:parallelization_35": 5905, "ng:performance:vectorization_35": 5906, "ng:performance:profiling_35": 5907, "ng:performance:benchmarking_35": 5908, "ng:performance:scaling_35": 5909, "ng:performance:tuning_35": 5910, "ng:performance:optimization_36": 5911, "ng:performance:caching_36": 5912, "ng:performance:parallelization_36": 5913, "ng:performance:vectorization_36": 5914, "ng:performance:profiling_36": 5915, "ng:performance:benchmarking_36": 5916, "ng:performance:scaling_36": 5917, "ng:performance:tuning_36": 5918, "ng:performance:optimization_37": 5919, "ng:performance:caching_37": 5920, "ng:performance:parallelization_37": 5921, "ng:performance:vectorization_37": 5922, "ng:performance:profiling_37": 5923, "ng:performance:benchmarking_37": 5924, "ng:performance:scaling_37": 5925, "ng:performance:tuning_37": 5926, "ng:performance:optimization_38": 5927, "ng:performance:caching_38": 5928, "ng:performance:parallelization_38": 5929, "ng:performance:vectorization_38": 5930, "ng:performance:profiling_38": 5931, "ng:performance:benchmarking_38": 5932, "ng:performance:scaling_38": 5933, "ng:performance:tuning_38": 5934, "ng:performance:optimization_39": 5935, "ng:performance:caching_39": 5936, "ng:performance:parallelization_39": 5937, "ng:performance:vectorization_39": 5938, "ng:performance:profiling_39": 5939, "ng:performance:scaling_39": 5940, "ng:performance:tuning_39": 5941, "ng:performance:optimization_40": 5942, "ng:performance:caching_40": 5943, "ng:performance:parallelization_40": 5944, "ng:performance:vectorization_40": 5945, "ng:performance:profiling_40": 5946, "ng:performance:benchmarking_40": 5947, "ng:performance:scaling_40": 5948, "ng:performance:tuning_40": 5949, "ng:performance:optimization_41": 5950, "ng:performance:caching_41": 5951, "ng:performance:parallelization_41": 5952, "ng:performance:vectorization_41": 5953, "ng:performance:profiling_41": 5954, "ng:performance:benchmarking_41": 5955, "ng:performance:scaling_41": 5956, "ng:performance:tuning_41": 5957, "ng:performance:optimization_42": 5958, "ng:performance:caching_42": 5959, "ng:performance:parallelization_42": 5960, "ng:performance:vectorization_42": 5961, "ng:performance:profiling_42": 5962, "ng:performance:benchmarking_42": 5963, "ng:performance:scaling_42": 5964, "ng:performance:tuning_42": 5965, "ng:performance:optimization_43": 5966, "ng:performance:caching_43": 5967, "ng:performance:parallelization_43": 5968, "ng:performance:vectorization_43": 5969, "ng:performance:profiling_43": 5970, "ng:performance:benchmarking_43": 5971, "ng:performance:scaling_43": 5972, "ng:performance:tuning_43": 5973, "ng:performance:optimization_44": 5974, "ng:performance:caching_44": 5975, "ng:performance:parallelization_44": 5976, "ng:performance:vectorization_44": 5977, "ng:performance:profiling_44": 5978, "ng:performance:benchmarking_44": 5979, "ng:performance:scaling_44": 5980, "ng:performance:tuning_44": 5981, "ng:performance:optimization_45": 5982, "ng:performance:caching_45": 5983, "ng:performance:parallelization_45": 5984, "ng:performance:vectorization_45": 5985, "ng:performance:profiling_45": 5986, "ng:performance:benchmarking_45": 5987, "ng:performance:scaling_45": 5988, "ng:performance:tuning_45": 5989, "ng:performance:optimization_46": 5990, "ng:performance:caching_46": 5991, "ng:performance:vectorization_46": 5992, "ng:performance:profiling_46": 5993, "ng:performance:benchmarking_46": 5994, "ng:performance:scaling_46": 5995, "ng:performance:tuning_46": 5996, "ng:performance:optimization_47": 5997, "ng:performance:caching_47": 5998, "ng:performance:parallelization_47": 5999, "ng:performance:vectorization_47": 6000, "ng:performance:profiling_47": 6001, "ng:performance:benchmarking_47": 6002, "ng:performance:scaling_47": 6003, "ng:performance:tuning_47": 6004, "ng:performance:optimization_48": 6005, "ng:performance:caching_48": 6006, "ng:performance:parallelization_48": 6007, "ng:performance:vectorization_48": 6008, "ng:performance:profiling_48": 6009, "ng:performance:benchmarking_48": 6010, "ng:performance:scaling_48": 6011, "ng:performance:tuning_48": 6012, "ng:performance:optimization_49": 6013, "ng:performance:caching_49": 6014, "ng:performance:parallelization_49": 6015, "ng:performance:vectorization_49": 6016, "ng:performance:profiling_49": 6017, "ng:performance:benchmarking_49": 6018, "ng:performance:scaling_49": 6019, "ng:performance:tuning_49": 6020, "ng:performance:caching_50": 6021, "ng:performance:parallelization_50": 6022, "ng:performance:vectorization_50": 6023, "ng:performance:profiling_50": 6024, "ng:performance:benchmarking_50": 6025, "ng:performance:scaling_50": 6026, "ng:performance:tuning_50": 6027, "ng:performance:optimization_51": 6028, "ng:performance:caching_51": 6029, "ng:performance:parallelization_51": 6030, "ng:performance:vectorization_51": 6031, "ng:performance:profiling_51": 6032, "ng:performance:benchmarking_51": 6033, "ng:performance:scaling_51": 6034, "ng:performance:tuning_51": 6035, "ng:performance:optimization_52": 6036, "ng:performance:caching_52": 6037, "ng:performance:parallelization_52": 6038, "ng:performance:vectorization_52": 6039, "ng:performance:profiling_52": 6040, "ng:performance:benchmarking_52": 6041, "ng:performance:scaling_52": 6042, "ng:performance:tuning_52": 6043, "ng:performance:optimization_53": 6044, "ng:performance:caching_53": 6045, "ng:performance:parallelization_53": 6046, "ng:performance:vectorization_53": 6047, "ng:performance:profiling_53": 6048, "ng:performance:benchmarking_53": 6049, "ng:performance:scaling_53": 6050, "ng:performance:tuning_53": 6051, "ng:performance:optimization_54": 6052, "ng:performance:caching_54": 6053, "ng:performance:parallelization_54": 6054, "ng:performance:vectorization_54": 6055, "ng:performance:profiling_54": 6056, "ng:performance:benchmarking_54": 6057, "ng:performance:scaling_54": 6058, "ng:performance:tuning_54": 6059, "ng:performance:optimization_55": 6060, "ng:performance:caching_55": 6061, "ng:performance:parallelization_55": 6062, "ng:performance:vectorization_55": 6063, "ng:performance:profiling_55": 6064, "ng:performance:benchmarking_55": 6065, "ng:performance:scaling_55": 6066, "ng:performance:tuning_55": 6067, "ng:performance:optimization_56": 6068, "ng:performance:caching_56": 6069, "ng:performance:parallelization_56": 6070, "ng:performance:vectorization_56": 6071, "ng:performance:profiling_56": 6072, "ng:performance:benchmarking_56": 6073, "ng:performance:scaling_56": 6074, "ng:performance:tuning_56": 6075, "ng:performance:optimization_57": 6076, "ng:performance:caching_57": 6077, "ng:performance:parallelization_57": 6078, "ng:performance:vectorization_57": 6079, "ng:performance:profiling_57": 6080, "ng:performance:benchmarking_57": 6081, "ng:performance:scaling_57": 6082, "ng:performance:tuning_57": 6083, "ng:performance:optimization_58": 6084, "ng:performance:caching_58": 6085, "ng:performance:parallelization_58": 6086, "ng:performance:profiling_58": 6087, "ng:performance:benchmarking_58": 6088, "ng:performance:scaling_58": 6089, "ng:performance:tuning_58": 6090, "ng:performance:optimization_59": 6091, "ng:performance:caching_59": 6092, "ng:performance:parallelization_59": 6093, "ng:performance:vectorization_59": 6094, "ng:performance:profiling_59": 6095, "ng:performance:benchmarking_59": 6096, "ng:performance:scaling_59": 6097, "ng:performance:tuning_59": 6098, "ng:performance:optimization_60": 6099, "ng:performance:caching_60": 6100, "ng:performance:parallelization_60": 6101, "ng:performance:vectorization_60": 6102, "ng:performance:profiling_60": 6103, "ng:performance:benchmarking_60": 6104, "ng:performance:scaling_60": 6105, "ng:performance:tuning_60": 6106, "ng:performance:optimization_61": 6107, "ng:performance:caching_61": 6108, "ng:performance:parallelization_61": 6109, "ng:performance:vectorization_61": 6110, "ng:performance:profiling_61": 6111, "ng:performance:benchmarking_61": 6112, "ng:performance:scaling_61": 6113, "ng:performance:tuning_61": 6114, "ng:performance:optimization_62": 6115, "ng:performance:caching_62": 6116, "ng:performance:parallelization_62": 6117, "ng:performance:vectorization_62": 6118, "ng:performance:profiling_62": 6119, "ng:performance:benchmarking_62": 6120, "ng:performance:scaling_62": 6121, "ng:performance:tuning_62": 6122, "ng:performance:optimization_63": 6123, "ng:performance:caching_63": 6124, "ng:performance:parallelization_63": 6125, "ng:performance:vectorization_63": 6126, "ng:performance:profiling_63": 6127, "ng:performance:benchmarking_63": 6128, "ng:performance:scaling_63": 6129, "ng:performance:tuning_63": 6130, "ng:performance:optimization_64": 6131, "ng:performance:caching_64": 6132, "ng:performance:parallelization_64": 6133, "ng:performance:vectorization_64": 6134, "ng:performance:profiling_64": 6135, "ng:performance:benchmarking_64": 6136, "ng:performance:scaling_64": 6137, "ng:performance:tuning_64": 6138, "ng:performance:optimization_65": 6139, "ng:performance:caching_65": 6140, "ng:performance:parallelization_65": 6141, "ng:performance:vectorization_65": 6142, "ng:performance:profiling_65": 6143, "ng:performance:benchmarking_65": 6144, "ng:performance:scaling_65": 6145, "ng:performance:tuning_65": 6146, "ng:performance:optimization_66": 6147, "ng:performance:caching_66": 6148, "ng:performance:parallelization_66": 6149, "ng:performance:vectorization_66": 6150, "ng:performance:profiling_66": 6151, "ng:performance:benchmarking_66": 6152, "ng:performance:scaling_66": 6153, "ng:performance:tuning_66": 6154, "ng:performance:optimization_67": 6155, "ng:performance:caching_67": 6156, "ng:performance:parallelization_67": 6157, "ng:performance:vectorization_67": 6158, "ng:performance:profiling_67": 6159, "ng:performance:benchmarking_67": 6160, "ng:performance:scaling_67": 6161, "ng:performance:tuning_67": 6162, "ng:performance:optimization_68": 6163, "ng:performance:caching_68": 6164, "ng:performance:parallelization_68": 6165, "ng:performance:vectorization_68": 6166, "ng:performance:profiling_68": 6167, "ng:performance:benchmarking_68": 6168, "ng:performance:scaling_68": 6169, "ng:performance:tuning_68": 6170, "ng:performance:optimization_69": 6171, "ng:performance:caching_69": 6172, "ng:performance:parallelization_69": 6173, "ng:performance:vectorization_69": 6174, "ng:performance:profiling_69": 6175, "ng:performance:benchmarking_69": 6176, "ng:performance:scaling_69": 6177, "ng:performance:tuning_69": 6178, "ng:performance:optimization_70": 6179, "ng:performance:caching_70": 6180, "ng:performance:parallelization_70": 6181, "ng:performance:vectorization_70": 6182, "ng:performance:profiling_70": 6183, "ng:performance:benchmarking_70": 6184, "ng:performance:scaling_70": 6185, "ng:performance:tuning_70": 6186, "ng:performance:optimization_71": 6187, "ng:performance:caching_71": 6188, "ng:performance:parallelization_71": 6189, "ng:performance:vectorization_71": 6190, "ng:performance:profiling_71": 6191, "ng:performance:benchmarking_71": 6192, "ng:performance:scaling_71": 6193, "ng:performance:tuning_71": 6194, "ng:performance:optimization_72": 6195, "ng:performance:caching_72": 6196, "ng:performance:parallelization_72": 6197, "ng:performance:vectorization_72": 6198, "ng:performance:profiling_72": 6199, "ng:performance:benchmarking_72": 6200, "ng:performance:scaling_72": 6201, "ng:performance:tuning_72": 6202, "ng:performance:optimization_73": 6203, "ng:performance:caching_73": 6204, "ng:performance:parallelization_73": 6205, "ng:performance:vectorization_73": 6206, "ng:performance:profiling_73": 6207, "ng:performance:benchmarking_73": 6208, "ng:performance:scaling_73": 6209, "ng:performance:tuning_73": 6210, "ng:performance:optimization_74": 6211, "ng:performance:caching_74": 6212, "ng:performance:vectorization_74": 6213, "ng:performance:profiling_74": 6214, "ng:performance:benchmarking_74": 6215, "ng:performance:scaling_74": 6216, "ng:performance:tuning_74": 6217, "ng:performance:optimization_75": 6218, "ng:performance:caching_75": 6219, "ng:performance:parallelization_75": 6220, "ng:performance:vectorization_75": 6221, "ng:performance:profiling_75": 6222, "ng:performance:benchmarking_75": 6223, "ng:performance:scaling_75": 6224, "ng:performance:tuning_75": 6225, "ng:performance:optimization_76": 6226, "ng:performance:caching_76": 6227, "ng:performance:parallelization_76": 6228, "ng:performance:vectorization_76": 6229, "ng:performance:profiling_76": 6230, "ng:performance:benchmarking_76": 6231, "ng:performance:scaling_76": 6232, "ng:performance:tuning_76": 6233, "ng:performance:optimization_77": 6234, "ng:performance:caching_77": 6235, "ng:performance:parallelization_77": 6236, "ng:performance:vectorization_77": 6237, "ng:performance:profiling_77": 6238, "ng:performance:benchmarking_77": 6239, "ng:performance:scaling_77": 6240, "ng:performance:tuning_77": 6241, "ng:performance:optimization_78": 6242, "ng:performance:caching_78": 6243, "ng:performance:parallelization_78": 6244, "ng:performance:vectorization_78": 6245, "ng:performance:profiling_78": 6246, "ng:performance:benchmarking_78": 6247, "ng:performance:scaling_78": 6248, "ng:performance:tuning_78": 6249, "ng:performance:optimization_79": 6250, "ng:performance:caching_79": 6251, "ng:performance:parallelization_79": 6252, "ng:performance:vectorization_79": 6253, "ng:performance:profiling_79": 6254, "ng:performance:benchmarking_79": 6255, "ng:performance:scaling_79": 6256, "ng:performance:tuning_79": 6257, "ng:performance:optimization_80": 6258, "ng:performance:caching_80": 6259, "ng:performance:parallelization_80": 6260, "ng:performance:vectorization_80": 6261, "ng:performance:profiling_80": 6262, "ng:performance:benchmarking_80": 6263, "ng:performance:scaling_80": 6264, "ng:performance:tuning_80": 6265, "ng:performance:optimization_81": 6266, "ng:performance:caching_81": 6267, "ng:performance:parallelization_81": 6268, "ng:performance:vectorization_81": 6269, "ng:performance:profiling_81": 6270, "ng:performance:benchmarking_81": 6271, "ng:performance:scaling_81": 6272, "ng:performance:tuning_81": 6273, "ng:performance:optimization_82": 6274, "ng:performance:caching_82": 6275, "ng:performance:parallelization_82": 6276, "ng:performance:vectorization_82": 6277, "ng:performance:profiling_82": 6278, "ng:performance:benchmarking_82": 6279, "ng:performance:scaling_82": 6280, "ng:performance:tuning_82": 6281, "ng:performance:optimization_83": 6282, "ng:performance:caching_83": 6283, "ng:performance:parallelization_83": 6284, "ng:performance:vectorization_83": 6285, "ng:performance:profiling_83": 6286, "ng:performance:benchmarking_83": 6287, "ng:performance:scaling_83": 6288, "ng:performance:tuning_83": 6289, "ng:performance:optimization_84": 6290, "ng:performance:caching_84": 6291, "ng:performance:parallelization_84": 6292, "ng:performance:vectorization_84": 6293, "ng:performance:profiling_84": 6294, "ng:performance:benchmarking_84": 6295, "ng:performance:scaling_84": 6296, "ng:performance:tuning_84": 6297, "ng:performance:optimization_85": 6298, "ng:performance:caching_85": 6299, "ng:performance:parallelization_85": 6300, "ng:performance:vectorization_85": 6301, "ng:performance:profiling_85": 6302, "ng:performance:benchmarking_85": 6303, "ng:performance:scaling_85": 6304, "ng:performance:tuning_85": 6305, "ng:performance:optimization_86": 6306, "ng:performance:caching_86": 6307, "ng:performance:parallelization_86": 6308, "ng:performance:vectorization_86": 6309, "ng:performance:profiling_86": 6310, "ng:performance:benchmarking_86": 6311, "ng:performance:scaling_86": 6312, "ng:performance:tuning_86": 6313, "⎵": 6314, "➰": 6315, "⪻": 6316, "⎺": 6317, "⊸": 6318, "⪳": 6319, "⎷": 6320, "⮱": 6321, "⮴": 6322, "⮵": 6323, "⎹": 6324, "⪶": 6325, "⎳": 6326, "⎲": 6327, "⎴": 6328, "⪰": 6329, "➸": 6330, "⪷": 6331, "⎰": 6332, "⪯": 6333, "⊴": 6334, "➱": 6335, "⪙": 6336, "➹": 6337, "➺": 6338, "➻": 6339, "⪹": 6340, "⪺": 6341, "⮻": 6342, "⎱": 6343, "➲": 6344, "➳": 6345, "⪱": 6346, "⎶": 6347, "➶": 6348, "⮉": 6349, "⇖": 6350, "⇨": 6351, "ℯ": 6352, "⇺": 6353, "🟗": 6354, "🟕": 6355, "🟠": 6356, "🟣": 6357, "🟡": 6358, "🟘": 6359, "🟙": 6360, "🠀": 6361, "🠁": 6362, "🠄": 6363, "🠠": 6364, "🠡": 6365, "🠤": 6366, "🠥": 6367, "🠧": 6368, "⌺": 6369, "⍅": 6370, "⍌": 6371, "⍪": 6372, "⊛": 6373, "⊝": 6374, "⋏": 6375, "≏": 6376, "⌱": 6377, "⋫": 6378, "∔": 6379, "⋶": 6380, "⎆": 6381, "∺": 6382, "≡": 6383, "⊷": 6384, "⊺": 6385, "‘": 6386, "⇦": 6387, "₼": 6388, "∇": 6389, "⊽": 6390, "⍻": 6391, "⑶": 6392, "℟": 6393, "‷": 6394, "’": 6395, "ℱ": 6396, "⋴": 6397, "ⅷ": 6398, "ⅻ": 6399, "⌰": 6400, "™": 6401, "⊪": 6402, "∪": 6403, "⋙": 6404, "⅙": 6405, "ng:quantum:entanglement": 6406, "ng:quantum:correction_1": 6407, "ng:quantum:algorithm_2": 6408, "ng:quantum:superposition_3": 6409, "ng:quantum:decoherence_3": 6410, "ng:quantum:algorithm_3": 6411, "ng:quantum:superposition_4": 6412, "ng:quantum:entanglement_4": 6413, "ng:quantum:decoherence_4": 6414, "ng:quantum:measurement_4": 6415, "ng:quantum:gate_4": 6416, "ng:quantum:algorithm_4": 6417, "ng:quantum:error_4": 6418, "ng:quantum:superposition_5": 6419, "ng:quantum:measurement_5": 6420, "ng:quantum:correction_5": 6421, "ng:quantum:algorithm_6": 6422, "ng:quantum:error_6": 6423, "ng:quantum:decoherence_7": 6424, "ng:quantum:measurement_7": 6425, "ng:quantum:error_7": 6426, "ng:quantum:entanglement_8": 6427, "ng:quantum:decoherence_8": 6428, "ng:quantum:measurement_8": 6429, "ng:quantum:error_8": 6430, "ng:quantum:correction_8": 6431, "ng:quantum:decoherence_9": 6432, "ng:quantum:algorithm_9": 6433, "ng:quantum:decoherence_10": 6434, "ng:quantum:measurement_10": 6435, "ng:quantum:gate_10": 6436, "ng:quantum:error_10": 6437, "ng:quantum:correction_10": 6438, "ng:quantum:measurement_11": 6439, "ng:quantum:error_11": 6440, "ng:quantum:measurement_12": 6441, "ng:quantum:gate_12": 6442, "ng:quantum:error_12": 6443, "ng:quantum:correction_12": 6444, "ng:quantum:measurement_13": 6445, "ng:quantum:superposition_14": 6446, "ng:quantum:measurement_14": 6447, "ng:quantum:gate_14": 6448, "ng:quantum:error_14": 6449, "ng:quantum:correction_14": 6450, "ng:quantum:superposition_15": 6451, "ng:quantum:entanglement_15": 6452, "ng:quantum:decoherence_15": 6453, "ng:quantum:measurement_15": 6454, "ng:quantum:gate_15": 6455, "ng:quantum:algorithm_15": 6456, "ng:quantum:error_15": 6457, "ng:quantum:entanglement_16": 6458, "ng:quantum:measurement_16": 6459, "ng:quantum:gate_16": 6460, "ng:quantum:algorithm_16": 6461, "ng:quantum:superposition_17": 6462, "ng:quantum:decoherence_17": 6463, "ng:quantum:measurement_17": 6464, "ng:quantum:error_17": 6465, "ng:quantum:correction_17": 6466, "ng:quantum:superposition_18": 6467, "ng:quantum:measurement_18": 6468, "ng:quantum:algorithm_18": 6469, "ng:quantum:correction_18": 6470, "ng:quantum:decoherence_19": 6471, "ng:quantum:measurement_19": 6472, "ng:quantum:gate_19": 6473, "ng:quantum:algorithm_19": 6474, "ng:quantum:error_19": 6475, "ng:quantum:superposition_20": 6476, "ng:quantum:decoherence_20": 6477, "ng:quantum:algorithm_20": 6478, "ng:quantum:decoherence_21": 6479, "ng:quantum:measurement_21": 6480, "ng:quantum:gate_21": 6481, "ng:quantum:algorithm_21": 6482, "ng:quantum:error_21": 6483, "ng:quantum:superposition_24": 6484, "ng:quantum:entanglement_24": 6485, "ng:quantum:decoherence_24": 6486, "ng:quantum:measurement_24": 6487, "ng:quantum:gate_24": 6488, "ng:quantum:algorithm_24": 6489, "ng:quantum:error_24": 6490, "ng:quantum:correction_24": 6491, "ng:quantum:superposition_25": 6492, "ng:quantum:entanglement_25": 6493, "ng:quantum:decoherence_25": 6494, "ng:quantum:measurement_25": 6495, "ng:quantum:gate_25": 6496, "ng:quantum:algorithm_25": 6497, "ng:quantum:error_25": 6498, "ng:quantum:correction_25": 6499, "ng:quantum:superposition_26": 6500, "ng:quantum:entanglement_26": 6501, "ng:quantum:decoherence_26": 6502, "ng:quantum:measurement_26": 6503, "ng:quantum:gate_26": 6504, "ng:quantum:algorithm_26": 6505, "ng:quantum:error_26": 6506, "ng:quantum:correction_26": 6507, "ng:quantum:superposition_27": 6508, "ng:quantum:entanglement_27": 6509, "ng:quantum:decoherence_27": 6510, "ng:quantum:measurement_27": 6511, "ng:quantum:gate_27": 6512, "ng:quantum:algorithm_27": 6513, "ng:quantum:error_27": 6514, "ng:quantum:correction_27": 6515, "ng:quantum:superposition_28": 6516, "ng:quantum:entanglement_28": 6517, "ng:quantum:decoherence_28": 6518, "ng:quantum:measurement_28": 6519, "ng:quantum:gate_28": 6520, "ng:quantum:algorithm_28": 6521, "ng:quantum:error_28": 6522, "ng:quantum:correction_28": 6523, "ng:quantum:superposition_29": 6524, "ng:quantum:entanglement_29": 6525, "ng:quantum:decoherence_29": 6526, "ng:quantum:measurement_29": 6527, "ng:quantum:gate_29": 6528, "ng:quantum:algorithm_29": 6529, "ng:quantum:error_29": 6530, "ng:quantum:correction_29": 6531, "ng:quantum:superposition_30": 6532, "ng:quantum:entanglement_30": 6533, "ng:quantum:decoherence_30": 6534, "ng:quantum:measurement_30": 6535, "ng:quantum:gate_30": 6536, "ng:quantum:algorithm_30": 6537, "ng:quantum:error_30": 6538, "ng:quantum:correction_30": 6539, "ng:quantum:superposition_31": 6540, "ng:quantum:entanglement_31": 6541, "ng:quantum:decoherence_31": 6542, "ng:quantum:measurement_31": 6543, "ng:quantum:gate_31": 6544, "ng:quantum:algorithm_31": 6545, "ng:quantum:error_31": 6546, "ng:quantum:correction_31": 6547, "ng:quantum:superposition_32": 6548, "ng:quantum:entanglement_32": 6549, "ng:quantum:decoherence_32": 6550, "ng:quantum:measurement_32": 6551, "ng:quantum:gate_32": 6552, "ng:quantum:algorithm_32": 6553, "ng:quantum:error_32": 6554, "ng:quantum:correction_32": 6555, "ng:quantum:superposition_33": 6556, "ng:quantum:entanglement_33": 6557, "ng:quantum:decoherence_33": 6558, "ng:quantum:measurement_33": 6559, "ng:quantum:gate_33": 6560, "ng:quantum:algorithm_33": 6561, "ng:quantum:error_33": 6562, "ng:quantum:correction_33": 6563, "ng:quantum:superposition_34": 6564, "ng:quantum:entanglement_34": 6565, "ng:quantum:decoherence_34": 6566, "ng:quantum:measurement_34": 6567, "ng:quantum:gate_34": 6568, "ng:quantum:algorithm_34": 6569, "ng:quantum:error_34": 6570, "ng:quantum:correction_34": 6571, "ng:quantum:superposition_35": 6572, "ng:quantum:entanglement_35": 6573, "ng:quantum:decoherence_35": 6574, "ng:quantum:measurement_35": 6575, "ng:quantum:gate_35": 6576, "ng:quantum:error_35": 6577, "ng:quantum:correction_35": 6578, "ng:quantum:superposition_36": 6579, "ng:quantum:entanglement_36": 6580, "ng:quantum:decoherence_36": 6581, "ng:quantum:measurement_36": 6582, "ng:quantum:gate_36": 6583, "ng:quantum:algorithm_36": 6584, "ng:quantum:error_36": 6585, "ng:quantum:correction_36": 6586, "ng:quantum:superposition_37": 6587, "ng:quantum:decoherence_37": 6588, "ng:quantum:measurement_37": 6589, "ng:quantum:gate_37": 6590, "ng:quantum:algorithm_37": 6591, "ng:quantum:error_37": 6592, "ng:quantum:correction_37": 6593, "ng:quantum:superposition_38": 6594, "ng:quantum:entanglement_38": 6595, "ng:quantum:decoherence_38": 6596, "ng:quantum:measurement_38": 6597, "ng:quantum:gate_38": 6598, "ng:quantum:algorithm_38": 6599, "ng:quantum:error_38": 6600, "ng:quantum:correction_38": 6601, "ng:quantum:superposition_39": 6602, "ng:quantum:entanglement_39": 6603, "ng:quantum:decoherence_39": 6604, "ng:quantum:measurement_39": 6605, "ng:quantum:gate_39": 6606, "ng:quantum:algorithm_39": 6607, "ng:quantum:error_39": 6608, "ng:quantum:correction_39": 6609, "ng:quantum:superposition_40": 6610, "ng:quantum:entanglement_40": 6611, "ng:quantum:decoherence_40": 6612, "ng:quantum:measurement_40": 6613, "ng:quantum:gate_40": 6614, "ng:quantum:algorithm_40": 6615, "ng:quantum:error_40": 6616, "ng:quantum:correction_40": 6617, "ng:quantum:superposition_41": 6618, "ng:quantum:entanglement_41": 6619, "ng:quantum:decoherence_41": 6620, "ng:quantum:measurement_41": 6621, "ng:quantum:gate_41": 6622, "ng:quantum:algorithm_41": 6623, "ng:quantum:error_41": 6624, "ng:quantum:correction_41": 6625, "ng:quantum:superposition_42": 6626, "ng:quantum:entanglement_42": 6627, "ng:quantum:decoherence_42": 6628, "ng:quantum:measurement_42": 6629, "ng:quantum:gate_42": 6630, "ng:quantum:algorithm_42": 6631, "ng:quantum:error_42": 6632, "ng:quantum:correction_42": 6633, "ng:quantum:superposition_43": 6634, "ng:quantum:entanglement_43": 6635, "ng:quantum:decoherence_43": 6636, "ng:quantum:measurement_43": 6637, "ng:quantum:gate_43": 6638, "ng:quantum:algorithm_43": 6639, "ng:quantum:error_43": 6640, "ng:quantum:correction_43": 6641, "ng:quantum:superposition_44": 6642, "ng:quantum:entanglement_44": 6643, "ng:quantum:decoherence_44": 6644, "ng:quantum:measurement_44": 6645, "ng:quantum:gate_44": 6646, "ng:quantum:algorithm_44": 6647, "ng:quantum:error_44": 6648, "ng:quantum:correction_44": 6649, "ng:quantum:superposition_45": 6650, "ng:quantum:entanglement_45": 6651, "ng:quantum:decoherence_45": 6652, "ng:quantum:measurement_45": 6653, "ng:quantum:gate_45": 6654, "ng:quantum:algorithm_45": 6655, "ng:quantum:error_45": 6656, "ng:quantum:correction_45": 6657, "ng:quantum:superposition_46": 6658, "ng:quantum:entanglement_46": 6659, "ng:quantum:decoherence_46": 6660, "ng:quantum:measurement_46": 6661, "ng:quantum:gate_46": 6662, "ng:quantum:algorithm_46": 6663, "ng:quantum:error_46": 6664, "ng:quantum:correction_46": 6665, "ng:quantum:superposition_47": 6666, "ng:quantum:entanglement_47": 6667, "ng:quantum:decoherence_47": 6668, "ng:quantum:measurement_47": 6669, "ng:quantum:gate_47": 6670, "ng:quantum:algorithm_47": 6671, "ng:quantum:error_47": 6672, "ng:quantum:correction_47": 6673, "ng:quantum:superposition_48": 6674, "ng:quantum:entanglement_48": 6675, "ng:quantum:decoherence_48": 6676, "ng:quantum:measurement_48": 6677, "ng:quantum:gate_48": 6678, "ng:quantum:algorithm_48": 6679, "ng:quantum:error_48": 6680, "ng:quantum:correction_48": 6681, "ng:quantum:superposition_49": 6682, "ng:quantum:entanglement_49": 6683, "ng:quantum:decoherence_49": 6684, "ng:quantum:measurement_49": 6685, "ng:quantum:gate_49": 6686, "ng:quantum:algorithm_49": 6687, "ng:quantum:error_49": 6688, "ng:quantum:correction_49": 6689, "ng:quantum:superposition_50": 6690, "ng:quantum:entanglement_50": 6691, "ng:quantum:decoherence_50": 6692, "ng:quantum:measurement_50": 6693, "ng:quantum:gate_50": 6694, "ng:quantum:algorithm_50": 6695, "ng:quantum:error_50": 6696, "ng:quantum:correction_50": 6697, "ng:quantum:superposition_51": 6698, "ng:quantum:entanglement_51": 6699, "ng:quantum:decoherence_51": 6700, "ng:quantum:measurement_51": 6701, "ng:quantum:gate_51": 6702, "ng:quantum:algorithm_51": 6703, "ng:quantum:error_51": 6704, "ng:quantum:correction_51": 6705, "ng:quantum:superposition_52": 6706, "ng:quantum:entanglement_52": 6707, "ng:quantum:decoherence_52": 6708, "ng:quantum:measurement_52": 6709, "ng:quantum:gate_52": 6710, "ng:quantum:algorithm_52": 6711, "ng:quantum:error_52": 6712, "ng:quantum:correction_52": 6713, "ng:quantum:superposition_53": 6714, "ng:quantum:entanglement_53": 6715, "ng:quantum:decoherence_53": 6716, "ng:quantum:measurement_53": 6717, "ng:quantum:gate_53": 6718, "ng:quantum:algorithm_53": 6719, "ng:quantum:error_53": 6720, "ng:quantum:correction_53": 6721, "ng:quantum:superposition_54": 6722, "ng:quantum:entanglement_54": 6723, "ng:quantum:decoherence_54": 6724, "ng:quantum:measurement_54": 6725, "ng:quantum:gate_54": 6726, "ng:quantum:algorithm_54": 6727, "ng:quantum:error_54": 6728, "ng:quantum:correction_54": 6729, "ng:quantum:superposition_55": 6730, "ng:quantum:entanglement_55": 6731, "ng:quantum:decoherence_55": 6732, "ng:quantum:measurement_55": 6733, "ng:quantum:gate_55": 6734, "ng:quantum:algorithm_55": 6735, "ng:quantum:error_55": 6736, "ng:quantum:correction_55": 6737, "ng:quantum:superposition_56": 6738, "ng:quantum:entanglement_56": 6739, "ng:quantum:decoherence_56": 6740, "ng:quantum:measurement_56": 6741, "ng:quantum:gate_56": 6742, "ng:quantum:algorithm_56": 6743, "ng:quantum:error_56": 6744, "ng:quantum:correction_56": 6745, "ng:quantum:superposition_57": 6746, "ng:quantum:entanglement_57": 6747, "ng:quantum:decoherence_57": 6748, "ng:quantum:measurement_57": 6749, "ng:quantum:gate_57": 6750, "ng:quantum:algorithm_57": 6751, "ng:quantum:error_57": 6752, "ng:quantum:correction_57": 6753, "ng:quantum:superposition_58": 6754, "ng:quantum:entanglement_58": 6755, "ng:quantum:decoherence_58": 6756, "ng:quantum:measurement_58": 6757, "ng:quantum:gate_58": 6758, "ng:quantum:algorithm_58": 6759, "ng:quantum:error_58": 6760, "ng:quantum:correction_58": 6761, "ng:quantum:superposition_59": 6762, "ng:quantum:entanglement_59": 6763, "ng:quantum:decoherence_59": 6764, "ng:quantum:measurement_59": 6765, "ng:quantum:gate_59": 6766, "ng:quantum:algorithm_59": 6767, "ng:quantum:error_59": 6768, "ng:quantum:correction_59": 6769, "ng:quantum:superposition_60": 6770, "ng:quantum:entanglement_60": 6771, "ng:quantum:decoherence_60": 6772, "ng:quantum:measurement_60": 6773, "ng:quantum:gate_60": 6774, "ng:quantum:algorithm_60": 6775, "ng:quantum:error_60": 6776, "ng:quantum:correction_60": 6777, "ng:quantum:superposition_61": 6778, "ng:quantum:entanglement_61": 6779, "ng:quantum:decoherence_61": 6780, "ng:quantum:measurement_61": 6781, "ng:quantum:gate_61": 6782, "ng:quantum:algorithm_61": 6783, "ng:quantum:error_61": 6784, "ng:quantum:correction_61": 6785, "ng:quantum:superposition_62": 6786, "ng:quantum:entanglement_62": 6787, "ng:quantum:decoherence_62": 6788, "ng:quantum:measurement_62": 6789, "ng:quantum:gate_62": 6790, "ng:quantum:algorithm_62": 6791, "ng:quantum:error_62": 6792, "ng:quantum:correction_62": 6793, "ng:quantum:superposition_63": 6794, "ng:quantum:entanglement_63": 6795, "ng:quantum:decoherence_63": 6796, "ng:quantum:measurement_63": 6797, "ng:quantum:gate_63": 6798, "ng:quantum:algorithm_63": 6799, "ng:quantum:error_63": 6800, "ng:quantum:correction_63": 6801, "ng:quantum:superposition_64": 6802, "ng:quantum:entanglement_64": 6803, "ng:quantum:decoherence_64": 6804, "ng:quantum:measurement_64": 6805, "ng:quantum:gate_64": 6806, "ng:quantum:algorithm_64": 6807, "ng:quantum:error_64": 6808, "ng:quantum:correction_64": 6809, "ng:quantum:superposition_65": 6810, "ng:quantum:entanglement_65": 6811, "ng:quantum:decoherence_65": 6812, "ng:quantum:measurement_65": 6813, "ng:quantum:gate_65": 6814, "ng:quantum:algorithm_65": 6815, "ng:quantum:error_65": 6816, "ng:quantum:correction_65": 6817, "ng:quantum:superposition_66": 6818, "ng:quantum:entanglement_66": 6819, "ng:quantum:decoherence_66": 6820, "ng:quantum:measurement_66": 6821, "ng:quantum:gate_66": 6822, "ng:quantum:algorithm_66": 6823, "ng:quantum:error_66": 6824, "ng:quantum:correction_66": 6825, "ng:quantum:superposition_67": 6826, "ng:quantum:entanglement_67": 6827, "ng:quantum:decoherence_67": 6828, "ng:quantum:measurement_67": 6829, "ng:quantum:gate_67": 6830, "ng:quantum:algorithm_67": 6831, "ng:quantum:error_67": 6832, "ng:quantum:correction_67": 6833, "ng:quantum:superposition_68": 6834, "ng:quantum:entanglement_68": 6835, "ng:quantum:decoherence_68": 6836, "ng:quantum:measurement_68": 6837, "ng:quantum:gate_68": 6838, "ng:quantum:algorithm_68": 6839, "ng:quantum:error_68": 6840, "ng:quantum:correction_68": 6841, "ng:quantum:superposition_69": 6842, "ng:quantum:entanglement_69": 6843, "ng:quantum:decoherence_69": 6844, "ng:quantum:measurement_69": 6845, "ng:quantum:gate_69": 6846, "ng:quantum:algorithm_69": 6847, "ng:quantum:error_69": 6848, "ng:quantum:correction_69": 6849, "ng:quantum:superposition_70": 6850, "ng:quantum:entanglement_70": 6851, "ng:quantum:decoherence_70": 6852, "ng:quantum:measurement_70": 6853, "ng:quantum:gate_70": 6854, "ng:quantum:algorithm_70": 6855, "ng:quantum:error_70": 6856, "ng:quantum:correction_70": 6857, "ng:quantum:superposition_71": 6858, "ng:quantum:entanglement_71": 6859, "ng:quantum:decoherence_71": 6860, "ng:quantum:measurement_71": 6861, "ng:quantum:gate_71": 6862, "ng:quantum:algorithm_71": 6863, "ng:quantum:error_71": 6864, "ng:quantum:correction_71": 6865, "ng:quantum:superposition_72": 6866, "ng:quantum:entanglement_72": 6867, "ng:quantum:decoherence_72": 6868, "ng:quantum:measurement_72": 6869, "ng:quantum:gate_72": 6870, "ng:quantum:algorithm_72": 6871, "ng:quantum:error_72": 6872, "ng:quantum:correction_72": 6873, "ng:quantum:superposition_73": 6874, "ng:quantum:entanglement_73": 6875, "ng:quantum:decoherence_73": 6876, "ng:quantum:measurement_73": 6877, "ng:quantum:gate_73": 6878, "ng:quantum:algorithm_73": 6879, "ng:quantum:correction_73": 6880, "ng:quantum:superposition_74": 6881, "ng:quantum:entanglement_74": 6882, "ng:quantum:decoherence_74": 6883, "ng:quantum:measurement_74": 6884, "ng:quantum:gate_74": 6885, "ng:quantum:algorithm_74": 6886, "ng:quantum:error_74": 6887, "ng:quantum:correction_74": 6888, "ng:quantum:superposition_75": 6889, "ng:quantum:entanglement_75": 6890, "ng:quantum:decoherence_75": 6891, "ng:quantum:measurement_75": 6892, "ng:quantum:gate_75": 6893, "ng:quantum:algorithm_75": 6894, "ng:quantum:error_75": 6895, "ng:quantum:correction_75": 6896, "ng:quantum:superposition_76": 6897, "ng:quantum:entanglement_76": 6898, "ng:quantum:decoherence_76": 6899, "ng:quantum:measurement_76": 6900, "ng:quantum:gate_76": 6901, "ng:quantum:algorithm_76": 6902, "ng:quantum:error_76": 6903, "ng:quantum:correction_76": 6904, "ng:quantum:superposition_77": 6905, "ng:quantum:entanglement_77": 6906, "ng:quantum:decoherence_77": 6907, "ng:quantum:measurement_77": 6908, "ng:quantum:gate_77": 6909, "ng:quantum:algorithm_77": 6910, "ng:quantum:error_77": 6911, "ng:quantum:correction_77": 6912, "ng:quantum:superposition_78": 6913, "ng:quantum:entanglement_78": 6914, "ng:quantum:decoherence_78": 6915, "ng:quantum:measurement_78": 6916, "ng:quantum:gate_78": 6917, "ng:quantum:algorithm_78": 6918, "ng:quantum:error_78": 6919, "ng:quantum:correction_78": 6920, "ng:quantum:superposition_79": 6921, "ng:quantum:entanglement_79": 6922, "ng:quantum:decoherence_79": 6923, "ng:quantum:measurement_79": 6924, "ng:quantum:gate_79": 6925, "ng:quantum:algorithm_79": 6926, "ng:quantum:error_79": 6927, "ng:quantum:correction_79": 6928, "ng:quantum:superposition_80": 6929, "ng:quantum:entanglement_80": 6930, "ng:quantum:decoherence_80": 6931, "ng:quantum:measurement_80": 6932, "ng:quantum:gate_80": 6933, "ng:quantum:algorithm_80": 6934, "ng:quantum:error_80": 6935, "ng:quantum:correction_80": 6936, "ng:quantum:superposition_81": 6937, "ng:quantum:entanglement_81": 6938, "ng:quantum:decoherence_81": 6939, "ng:quantum:measurement_81": 6940, "ng:quantum:gate_81": 6941, "ng:quantum:algorithm_81": 6942, "ng:quantum:error_81": 6943, "ng:quantum:correction_81": 6944, "ng:quantum:superposition_82": 6945, "ng:quantum:entanglement_82": 6946, "ng:quantum:decoherence_82": 6947, "ng:quantum:measurement_82": 6948, "ng:quantum:gate_82": 6949, "ng:quantum:algorithm_82": 6950, "ng:quantum:error_82": 6951, "ng:quantum:correction_82": 6952, "ng:quantum:superposition_83": 6953, "ng:quantum:entanglement_83": 6954, "ng:quantum:decoherence_83": 6955, "ng:quantum:measurement_83": 6956, "ng:quantum:gate_83": 6957, "ng:quantum:algorithm_83": 6958, "ng:quantum:error_83": 6959, "ng:quantum:correction_83": 6960, "ng:quantum:superposition_84": 6961, "ng:quantum:entanglement_84": 6962, "ng:quantum:decoherence_84": 6963, "ng:quantum:measurement_84": 6964, "ng:quantum:gate_84": 6965, "ng:quantum:algorithm_84": 6966, "ng:quantum:error_84": 6967, "ng:quantum:correction_84": 6968, "ng:quantum:superposition_85": 6969, "ng:quantum:entanglement_85": 6970, "ng:quantum:decoherence_85": 6971, "ng:quantum:measurement_85": 6972, "ng:quantum:gate_85": 6973, "ng:quantum:algorithm_85": 6974, "ng:quantum:error_85": 6975, "ng:quantum:correction_85": 6976, "ng:quantum:superposition_86": 6977, "ng:quantum:entanglement_86": 6978, "ng:quantum:decoherence_86": 6979, "ng:quantum:measurement_86": 6980, "ng:quantum:gate_86": 6981, "ng:quantum:algorithm_86": 6982, "ng:quantum:error_86": 6983, "ng:quantum:correction_86": 6984, "⫱": 6985, "⫝̸": 6986, "≤": 6987, "⩤": 6988, "⍥": 6989, "⥥": 6990, "⥰": 6991, "⭤": 6992, "⭸": 6993, "⥤": 6994, "⪪": 6995, "⎑": 6996, "⯋": 6997, "⫑": 6998, "⫐": 6999, "⫒": 7000, "⋒": 7001, "⋑": 7002, "⏏": 7003, "⋗": 7004, "⯒": 7005, "⋖": 7006, "⯌": 7007, "⏑": 7008, "⏒": 7009, "⏔": 7010, "⏖": 7011, "⏕": 7012, "⯔": 7013, "⋍": 7014, "⋌": 7015, "⏍": 7016, "⫓": 7017, "⫋": 7018, "⫖": 7019, "⫆": 7020, "⯑": 7021, "⫾": 7022, "⯢": 7023, "⏊": 7024, "➮": 7025, "⫊": 7026, "➭": 7027, "⥨": 7028, "⫎": 7029, "⮏": 7030, "⯍": 7031, "⯏": 7032, "⏌": 7033, "⮣": 7034, "⯆": 7035, "⏇": 7036, "⏈": 7037, "⏉": 7038, "⫇": 7039, "⭯": 7040, "⯇": 7041, "⯈": 7042, "∠": 7043, "⍁": 7044, "≬": 7045, "⏷": 7046, "₵": 7047, "⑧": 7048, "⑲": 7049, "⊚": 7050, "⋎": 7051, "∤": 7052, "⋹": 7053, " ": 7054, "−": 7055, "⋁": 7056, "≇": 7057, "⇄": 7058, "ↀ": 7059, "ⁿ": 7060, "⅘": 7061, "⅛": 7062, "⅖": 7063, "ng:security:nonrepudiation": 7064, "ng:security:authorization_1": 7065, "ng:security:integrity_1": 7066, "ng:security:authorization_3": 7067, "ng:security:nonrepudiation_3": 7068, "ng:security:audit_3": 7069, "ng:security:authentication_4": 7070, "ng:security:authorization_4": 7071, "ng:security:integrity_4": 7072, "ng:security:confidentiality_4": 7073, "ng:security:nonrepudiation_4": 7074, "ng:security:authentication_5": 7075, "ng:security:integrity_5": 7076, "ng:security:confidentiality_5": 7077, "ng:security:encryption_6": 7078, "ng:security:authentication_6": 7079, "ng:security:audit_6": 7080, "ng:security:compliance_6": 7081, "ng:security:nonrepudiation_7": 7082, "ng:security:compliance_7": 7083, "ng:security:encryption_8": 7084, "ng:security:authorization_8": 7085, "ng:security:nonrepudiation_8": 7086, "ng:security:encryption_9": 7087, "ng:security:integrity_9": 7088, "ng:security:audit_9": 7089, "ng:security:compliance_9": 7090, "ng:security:encryption_10": 7091, "ng:security:nonrepudiation_10": 7092, "ng:security:authentication_11": 7093, "ng:security:confidentiality_11": 7094, "ng:security:nonrepudiation_11": 7095, "ng:security:compliance_11": 7096, "ng:security:authentication_12": 7097, "ng:security:integrity_12": 7098, "ng:security:confidentiality_12": 7099, "ng:security:encryption_13": 7100, "ng:security:authentication_13": 7101, "ng:security:integrity_13": 7102, "ng:security:confidentiality_13": 7103, "ng:security:nonrepudiation_13": 7104, "ng:security:audit_13": 7105, "ng:security:compliance_13": 7106, "ng:security:encryption_14": 7107, "ng:security:authorization_14": 7108, "ng:security:integrity_14": 7109, "ng:security:confidentiality_14": 7110, "ng:security:nonrepudiation_14": 7111, "ng:security:encryption_15": 7112, "ng:security:authorization_15": 7113, "ng:security:confidentiality_15": 7114, "ng:security:nonrepudiation_15": 7115, "ng:security:audit_15": 7116, "ng:security:compliance_15": 7117, "ng:security:encryption_16": 7118, "ng:security:authentication_16": 7119, "ng:security:integrity_16": 7120, "ng:security:confidentiality_16": 7121, "ng:security:nonrepudiation_16": 7122, "ng:security:encryption_17": 7123, "ng:security:authentication_17": 7124, "ng:security:confidentiality_17": 7125, "ng:security:nonrepudiation_17": 7126, "ng:security:audit_17": 7127, "ng:security:compliance_17": 7128, "ng:security:integrity_18": 7129, "ng:security:audit_18": 7130, "ng:security:encryption_19": 7131, "ng:security:integrity_19": 7132, "ng:security:confidentiality_19": 7133, "ng:security:audit_19": 7134, "ng:security:compliance_19": 7135, "ng:security:encryption_20": 7136, "ng:security:integrity_20": 7137, "ng:security:confidentiality_20": 7138, "ng:security:audit_20": 7139, "ng:security:authentication_21": 7140, "ng:security:authorization_21": 7141, "ng:security:integrity_21": 7142, "ng:security:audit_21": 7143, "ng:security:encryption_24": 7144, "ng:security:authentication_24": 7145, "ng:security:authorization_24": 7146, "ng:security:integrity_24": 7147, "ng:security:confidentiality_24": 7148, "ng:security:nonrepudiation_24": 7149, "ng:security:audit_24": 7150, "ng:security:compliance_24": 7151, "ng:security:encryption_25": 7152, "ng:security:authentication_25": 7153, "ng:security:authorization_25": 7154, "ng:security:integrity_25": 7155, "ng:security:confidentiality_25": 7156, "ng:security:nonrepudiation_25": 7157, "ng:security:audit_25": 7158, "ng:security:compliance_25": 7159, "ng:security:encryption_26": 7160, "ng:security:authentication_26": 7161, "ng:security:authorization_26": 7162, "ng:security:integrity_26": 7163, "ng:security:confidentiality_26": 7164, "ng:security:nonrepudiation_26": 7165, "ng:security:audit_26": 7166, "ng:security:compliance_26": 7167, "ng:security:encryption_27": 7168, "ng:security:authentication_27": 7169, "ng:security:authorization_27": 7170, "ng:security:integrity_27": 7171, "ng:security:confidentiality_27": 7172, "ng:security:nonrepudiation_27": 7173, "ng:security:audit_27": 7174, "ng:security:compliance_27": 7175, "ng:security:encryption_28": 7176, "ng:security:authentication_28": 7177, "ng:security:authorization_28": 7178, "ng:security:integrity_28": 7179, "ng:security:confidentiality_28": 7180, "ng:security:nonrepudiation_28": 7181, "ng:security:audit_28": 7182, "ng:security:compliance_28": 7183, "ng:security:encryption_29": 7184, "ng:security:authentication_29": 7185, "ng:security:authorization_29": 7186, "ng:security:integrity_29": 7187, "ng:security:confidentiality_29": 7188, "ng:security:nonrepudiation_29": 7189, "ng:security:audit_29": 7190, "ng:security:compliance_29": 7191, "ng:security:encryption_30": 7192, "ng:security:authentication_30": 7193, "ng:security:authorization_30": 7194, "ng:security:integrity_30": 7195, "ng:security:confidentiality_30": 7196, "ng:security:nonrepudiation_30": 7197, "ng:security:audit_30": 7198, "ng:security:compliance_30": 7199, "ng:security:encryption_31": 7200, "ng:security:authentication_31": 7201, "ng:security:authorization_31": 7202, "ng:security:integrity_31": 7203, "ng:security:confidentiality_31": 7204, "ng:security:nonrepudiation_31": 7205, "ng:security:audit_31": 7206, "ng:security:compliance_31": 7207, "ng:security:encryption_32": 7208, "ng:security:authentication_32": 7209, "ng:security:authorization_32": 7210, "ng:security:integrity_32": 7211, "ng:security:confidentiality_32": 7212, "ng:security:nonrepudiation_32": 7213, "ng:security:audit_32": 7214, "ng:security:compliance_32": 7215, "ng:security:authentication_33": 7216, "ng:security:authorization_33": 7217, "ng:security:integrity_33": 7218, "ng:security:confidentiality_33": 7219, "ng:security:nonrepudiation_33": 7220, "ng:security:audit_33": 7221, "ng:security:compliance_33": 7222, "ng:security:encryption_34": 7223, "ng:security:authentication_34": 7224, "ng:security:authorization_34": 7225, "ng:security:integrity_34": 7226, "ng:security:confidentiality_34": 7227, "ng:security:nonrepudiation_34": 7228, "ng:security:audit_34": 7229, "ng:security:compliance_34": 7230, "ng:security:encryption_35": 7231, "ng:security:authentication_35": 7232, "ng:security:authorization_35": 7233, "ng:security:integrity_35": 7234, "ng:security:confidentiality_35": 7235, "ng:security:nonrepudiation_35": 7236, "ng:security:audit_35": 7237, "ng:security:compliance_35": 7238, "ng:security:encryption_36": 7239, "ng:security:authentication_36": 7240, "ng:security:authorization_36": 7241, "ng:security:integrity_36": 7242, "ng:security:confidentiality_36": 7243, "ng:security:nonrepudiation_36": 7244, "ng:security:audit_36": 7245, "ng:security:compliance_36": 7246, "ng:security:encryption_37": 7247, "ng:security:authentication_37": 7248, "ng:security:authorization_37": 7249, "ng:security:integrity_37": 7250, "ng:security:confidentiality_37": 7251, "ng:security:nonrepudiation_37": 7252, "ng:security:audit_37": 7253, "ng:security:compliance_37": 7254, "ng:security:encryption_38": 7255, "ng:security:authentication_38": 7256, "ng:security:authorization_38": 7257, "ng:security:integrity_38": 7258, "ng:security:confidentiality_38": 7259, "ng:security:nonrepudiation_38": 7260, "ng:security:audit_38": 7261, "ng:security:compliance_38": 7262, "ng:security:encryption_39": 7263, "ng:security:authentication_39": 7264, "ng:security:authorization_39": 7265, "ng:security:integrity_39": 7266, "ng:security:confidentiality_39": 7267, "ng:security:nonrepudiation_39": 7268, "ng:security:audit_39": 7269, "ng:security:compliance_39": 7270, "ng:security:encryption_40": 7271, "ng:security:authentication_40": 7272, "ng:security:authorization_40": 7273, "ng:security:integrity_40": 7274, "ng:security:confidentiality_40": 7275, "ng:security:nonrepudiation_40": 7276, "ng:security:audit_40": 7277, "ng:security:compliance_40": 7278, "ng:security:encryption_41": 7279, "ng:security:authentication_41": 7280, "ng:security:authorization_41": 7281, "ng:security:integrity_41": 7282, "ng:security:confidentiality_41": 7283, "ng:security:nonrepudiation_41": 7284, "ng:security:audit_41": 7285, "ng:security:compliance_41": 7286, "ng:security:encryption_42": 7287, "ng:security:authentication_42": 7288, "ng:security:authorization_42": 7289, "ng:security:integrity_42": 7290, "ng:security:confidentiality_42": 7291, "ng:security:nonrepudiation_42": 7292, "ng:security:audit_42": 7293, "ng:security:compliance_42": 7294, "ng:security:encryption_43": 7295, "ng:security:authentication_43": 7296, "ng:security:authorization_43": 7297, "ng:security:integrity_43": 7298, "ng:security:confidentiality_43": 7299, "ng:security:nonrepudiation_43": 7300, "ng:security:audit_43": 7301, "ng:security:compliance_43": 7302, "ng:security:encryption_44": 7303, "ng:security:authentication_44": 7304, "ng:security:authorization_44": 7305, "ng:security:integrity_44": 7306, "ng:security:confidentiality_44": 7307, "ng:security:nonrepudiation_44": 7308, "ng:security:audit_44": 7309, "ng:security:compliance_44": 7310, "ng:security:encryption_45": 7311, "ng:security:authorization_45": 7312, "ng:security:integrity_45": 7313, "ng:security:confidentiality_45": 7314, "ng:security:nonrepudiation_45": 7315, "ng:security:audit_45": 7316, "ng:security:compliance_45": 7317, "ng:security:encryption_46": 7318, "ng:security:authentication_46": 7319, "ng:security:authorization_46": 7320, "ng:security:integrity_46": 7321, "ng:security:confidentiality_46": 7322, "ng:security:nonrepudiation_46": 7323, "ng:security:audit_46": 7324, "ng:security:compliance_46": 7325, "ng:security:encryption_47": 7326, "ng:security:authentication_47": 7327, "ng:security:authorization_47": 7328, "ng:security:integrity_47": 7329, "ng:security:confidentiality_47": 7330, "ng:security:nonrepudiation_47": 7331, "ng:security:audit_47": 7332, "ng:security:compliance_47": 7333, "ng:security:encryption_48": 7334, "ng:security:authentication_48": 7335, "ng:security:authorization_48": 7336, "ng:security:integrity_48": 7337, "ng:security:confidentiality_48": 7338, "ng:security:nonrepudiation_48": 7339, "ng:security:audit_48": 7340, "ng:security:compliance_48": 7341, "ng:security:encryption_49": 7342, "ng:security:authentication_49": 7343, "ng:security:authorization_49": 7344, "ng:security:integrity_49": 7345, "ng:security:confidentiality_49": 7346, "ng:security:nonrepudiation_49": 7347, "ng:security:audit_49": 7348, "ng:security:compliance_49": 7349, "ng:security:encryption_50": 7350, "ng:security:authentication_50": 7351, "ng:security:authorization_50": 7352, "ng:security:integrity_50": 7353, "ng:security:confidentiality_50": 7354, "ng:security:nonrepudiation_50": 7355, "ng:security:audit_50": 7356, "ng:security:compliance_50": 7357, "ng:security:encryption_51": 7358, "ng:security:authentication_51": 7359, "ng:security:authorization_51": 7360, "ng:security:integrity_51": 7361, "ng:security:confidentiality_51": 7362, "ng:security:nonrepudiation_51": 7363, "ng:security:audit_51": 7364, "ng:security:compliance_51": 7365, "ng:security:encryption_52": 7366, "ng:security:authentication_52": 7367, "ng:security:authorization_52": 7368, "ng:security:integrity_52": 7369, "ng:security:confidentiality_52": 7370, "ng:security:nonrepudiation_52": 7371, "ng:security:audit_52": 7372, "ng:security:compliance_52": 7373, "ng:security:encryption_53": 7374, "ng:security:authentication_53": 7375, "ng:security:authorization_53": 7376, "ng:security:integrity_53": 7377, "ng:security:confidentiality_53": 7378, "ng:security:nonrepudiation_53": 7379, "ng:security:audit_53": 7380, "ng:security:compliance_53": 7381, "ng:security:encryption_54": 7382, "ng:security:authentication_54": 7383, "ng:security:authorization_54": 7384, "ng:security:integrity_54": 7385, "ng:security:confidentiality_54": 7386, "ng:security:nonrepudiation_54": 7387, "ng:security:audit_54": 7388, "ng:security:compliance_54": 7389, "ng:security:encryption_55": 7390, "ng:security:authentication_55": 7391, "ng:security:authorization_55": 7392, "ng:security:integrity_55": 7393, "ng:security:confidentiality_55": 7394, "ng:security:nonrepudiation_55": 7395, "ng:security:audit_55": 7396, "ng:security:encryption_56": 7397, "ng:security:authentication_56": 7398, "ng:security:authorization_56": 7399, "ng:security:integrity_56": 7400, "ng:security:confidentiality_56": 7401, "ng:security:nonrepudiation_56": 7402, "ng:security:audit_56": 7403, "ng:security:compliance_56": 7404, "ng:security:encryption_57": 7405, "ng:security:authentication_57": 7406, "ng:security:authorization_57": 7407, "ng:security:integrity_57": 7408, "ng:security:confidentiality_57": 7409, "ng:security:nonrepudiation_57": 7410, "ng:security:audit_57": 7411, "ng:security:compliance_57": 7412, "ng:security:encryption_58": 7413, "ng:security:authentication_58": 7414, "ng:security:authorization_58": 7415, "ng:security:integrity_58": 7416, "ng:security:confidentiality_58": 7417, "ng:security:nonrepudiation_58": 7418, "ng:security:audit_58": 7419, "ng:security:compliance_58": 7420, "ng:security:encryption_59": 7421, "ng:security:authentication_59": 7422, "ng:security:authorization_59": 7423, "ng:security:integrity_59": 7424, "ng:security:confidentiality_59": 7425, "ng:security:nonrepudiation_59": 7426, "ng:security:audit_59": 7427, "ng:security:compliance_59": 7428, "ng:security:encryption_60": 7429, "ng:security:authentication_60": 7430, "ng:security:authorization_60": 7431, "ng:security:integrity_60": 7432, "ng:security:confidentiality_60": 7433, "ng:security:nonrepudiation_60": 7434, "ng:security:audit_60": 7435, "ng:security:compliance_60": 7436, "ng:security:encryption_61": 7437, "ng:security:authentication_61": 7438, "ng:security:authorization_61": 7439, "ng:security:integrity_61": 7440, "ng:security:confidentiality_61": 7441, "ng:security:nonrepudiation_61": 7442, "ng:security:audit_61": 7443, "ng:security:compliance_61": 7444, "ng:security:encryption_62": 7445, "ng:security:authentication_62": 7446, "ng:security:authorization_62": 7447, "ng:security:integrity_62": 7448, "ng:security:confidentiality_62": 7449, "ng:security:nonrepudiation_62": 7450, "ng:security:audit_62": 7451, "ng:security:compliance_62": 7452, "ng:security:encryption_63": 7453, "ng:security:authentication_63": 7454, "ng:security:authorization_63": 7455, "ng:security:integrity_63": 7456, "ng:security:confidentiality_63": 7457, "ng:security:nonrepudiation_63": 7458, "ng:security:audit_63": 7459, "ng:security:compliance_63": 7460, "ng:security:encryption_64": 7461, "ng:security:authentication_64": 7462, "ng:security:authorization_64": 7463, "ng:security:integrity_64": 7464, "ng:security:confidentiality_64": 7465, "ng:security:nonrepudiation_64": 7466, "ng:security:audit_64": 7467, "ng:security:compliance_64": 7468, "ng:security:encryption_65": 7469, "ng:security:authentication_65": 7470, "ng:security:authorization_65": 7471, "ng:security:integrity_65": 7472, "ng:security:confidentiality_65": 7473, "ng:security:nonrepudiation_65": 7474, "ng:security:audit_65": 7475, "ng:security:compliance_65": 7476, "ng:security:encryption_66": 7477, "ng:security:authentication_66": 7478, "ng:security:authorization_66": 7479, "ng:security:integrity_66": 7480, "ng:security:confidentiality_66": 7481, "ng:security:nonrepudiation_66": 7482, "ng:security:audit_66": 7483, "ng:security:compliance_66": 7484, "ng:security:encryption_67": 7485, "ng:security:authentication_67": 7486, "ng:security:authorization_67": 7487, "ng:security:integrity_67": 7488, "ng:security:confidentiality_67": 7489, "ng:security:nonrepudiation_67": 7490, "ng:security:audit_67": 7491, "ng:security:compliance_67": 7492, "ng:security:encryption_68": 7493, "ng:security:authentication_68": 7494, "ng:security:authorization_68": 7495, "ng:security:integrity_68": 7496, "ng:security:confidentiality_68": 7497, "ng:security:nonrepudiation_68": 7498, "ng:security:audit_68": 7499, "ng:security:compliance_68": 7500, "ng:security:encryption_69": 7501, "ng:security:authentication_69": 7502, "ng:security:authorization_69": 7503, "ng:security:integrity_69": 7504, "ng:security:confidentiality_69": 7505, "ng:security:nonrepudiation_69": 7506, "ng:security:audit_69": 7507, "ng:security:compliance_69": 7508, "ng:security:encryption_70": 7509, "ng:security:authentication_70": 7510, "ng:security:authorization_70": 7511, "ng:security:integrity_70": 7512, "ng:security:confidentiality_70": 7513, "ng:security:nonrepudiation_70": 7514, "ng:security:audit_70": 7515, "ng:security:compliance_70": 7516, "ng:security:encryption_71": 7517, "ng:security:authentication_71": 7518, "ng:security:authorization_71": 7519, "ng:security:integrity_71": 7520, "ng:security:confidentiality_71": 7521, "ng:security:nonrepudiation_71": 7522, "ng:security:audit_71": 7523, "ng:security:compliance_71": 7524, "ng:security:encryption_72": 7525, "ng:security:authentication_72": 7526, "ng:security:authorization_72": 7527, "ng:security:integrity_72": 7528, "ng:security:confidentiality_72": 7529, "ng:security:nonrepudiation_72": 7530, "ng:security:audit_72": 7531, "ng:security:compliance_72": 7532, "ng:security:encryption_73": 7533, "ng:security:authentication_73": 7534, "ng:security:authorization_73": 7535, "ng:security:integrity_73": 7536, "ng:security:confidentiality_73": 7537, "ng:security:nonrepudiation_73": 7538, "ng:security:audit_73": 7539, "ng:security:compliance_73": 7540, "ng:security:encryption_74": 7541, "ng:security:authentication_74": 7542, "ng:security:authorization_74": 7543, "ng:security:integrity_74": 7544, "ng:security:confidentiality_74": 7545, "ng:security:nonrepudiation_74": 7546, "ng:security:audit_74": 7547, "ng:security:compliance_74": 7548, "ng:security:encryption_75": 7549, "ng:security:authentication_75": 7550, "ng:security:authorization_75": 7551, "ng:security:integrity_75": 7552, "ng:security:confidentiality_75": 7553, "ng:security:nonrepudiation_75": 7554, "ng:security:audit_75": 7555, "ng:security:compliance_75": 7556, "ng:security:encryption_76": 7557, "ng:security:authentication_76": 7558, "ng:security:authorization_76": 7559, "ng:security:integrity_76": 7560, "ng:security:confidentiality_76": 7561, "ng:security:nonrepudiation_76": 7562, "ng:security:audit_76": 7563, "ng:security:compliance_76": 7564, "ng:security:encryption_77": 7565, "ng:security:authentication_77": 7566, "ng:security:authorization_77": 7567, "ng:security:integrity_77": 7568, "ng:security:confidentiality_77": 7569, "ng:security:nonrepudiation_77": 7570, "ng:security:audit_77": 7571, "ng:security:compliance_77": 7572, "ng:security:encryption_78": 7573, "ng:security:authentication_78": 7574, "ng:security:authorization_78": 7575, "ng:security:integrity_78": 7576, "ng:security:confidentiality_78": 7577, "ng:security:nonrepudiation_78": 7578, "ng:security:audit_78": 7579, "ng:security:compliance_78": 7580, "ng:security:encryption_79": 7581, "ng:security:authentication_79": 7582, "ng:security:authorization_79": 7583, "ng:security:integrity_79": 7584, "ng:security:confidentiality_79": 7585, "ng:security:nonrepudiation_79": 7586, "ng:security:audit_79": 7587, "ng:security:compliance_79": 7588, "ng:security:encryption_80": 7589, "ng:security:authentication_80": 7590, "ng:security:authorization_80": 7591, "ng:security:integrity_80": 7592, "ng:security:confidentiality_80": 7593, "ng:security:nonrepudiation_80": 7594, "ng:security:audit_80": 7595, "ng:security:compliance_80": 7596, "ng:security:encryption_81": 7597, "ng:security:authentication_81": 7598, "ng:security:authorization_81": 7599, "ng:security:integrity_81": 7600, "ng:security:confidentiality_81": 7601, "ng:security:nonrepudiation_81": 7602, "ng:security:audit_81": 7603, "ng:security:compliance_81": 7604, "ng:security:encryption_82": 7605, "ng:security:authentication_82": 7606, "ng:security:authorization_82": 7607, "ng:security:integrity_82": 7608, "ng:security:confidentiality_82": 7609, "ng:security:nonrepudiation_82": 7610, "ng:security:audit_82": 7611, "ng:security:compliance_82": 7612, "ng:security:encryption_83": 7613, "ng:security:authentication_83": 7614, "ng:security:authorization_83": 7615, "ng:security:integrity_83": 7616, "ng:security:confidentiality_83": 7617, "ng:security:nonrepudiation_83": 7618, "ng:security:audit_83": 7619, "ng:security:compliance_83": 7620, "ng:security:encryption_84": 7621, "ng:security:authentication_84": 7622, "ng:security:authorization_84": 7623, "ng:security:integrity_84": 7624, "ng:security:confidentiality_84": 7625, "ng:security:nonrepudiation_84": 7626, "ng:security:audit_84": 7627, "ng:security:compliance_84": 7628, "ng:security:encryption_85": 7629, "ng:security:authentication_85": 7630, "ng:security:authorization_85": 7631, "ng:security:integrity_85": 7632, "ng:security:confidentiality_85": 7633, "ng:security:nonrepudiation_85": 7634, "ng:security:audit_85": 7635, "ng:security:compliance_85": 7636, "ng:security:encryption_86": 7637, "ng:security:authentication_86": 7638, "ng:security:authorization_86": 7639, "ng:security:integrity_86": 7640, "ng:security:confidentiality_86": 7641, "ng:security:nonrepudiation_86": 7642, "ng:security:audit_86": 7643, "ng:security:compliance_86": 7644, "⣔": 7645, "⡀": 7646, "𝐋": 7647, "𝒹": 7648, "𝓏": 7649, "⹁": 7650, "𝖸": 7651, "𝕱": 7652, "𝗇": 7653, "Ꝗ": 7654, "𝞗": 7655, "⟢": 7656, "𝘶": 7657, "𝒀": 7658, "𝔻": 7659, "𝟬": 7660, "𝓝": 7661, "⸪": 7662, "𝞝": 7663, "⇠": 7664, "↪": 7665, "⇲": 7666, "↼": 7667, "⇕": 7668, "↟": 7669, "⇧": 7670, "⇹": 7671, "↱": 7672, "◆": 7673, "⬢": 7674, "▲": 7675, "◑": 7676, "◍": 7677, "⬌": 7678, "⦃": 7679, "⤙": 7680, "⩑": 7681, "⨆": 7682, "⌅": 7683, "⬂": 7684, "⦡": 7685, "⬆": 7686, "▫": 7687, "⥆": 7688, "▧": 7689, "▿": 7690, "◚": 7691, "◵": 7692, "⩠": 7693, "◩": 7694, "⥗": 7695, "⦉": 7696, "⦼": 7697, "⧞": 7698, "℀": 7699, "ℶ": 7700, "ℤ": 7701, "ⅉ": 7702, "↧": 7703, "⇝": 7704, "↕": 7705, "⇋": 7706, "⇯": 7707, "⧪": 7708, "❦": 7709, "⧦": 7710, "≩": 7711, "≧": 7712, "≨": 7713, "≦": 7714, "❧": 7715, "⧧": 7716, "⩪": 7717, "⭥": 7718, "❪": 7719, "⭩": 7720, "⍧": 7721, "⥧": 7722, "⥶": 7723, "⍦": 7724, "⩥": 7725, "⍩": 7726, "➪": 7727, "⍨": 7728, "❨": 7729, "⭧": 7730, "⭨": 7731, "⥦": 7732, "⫴": 7733, "ⅆ": 7734, "↤": 7735, "⇚": 7736, "ℏ": 7737, "⇾": 7738, "ℳ": 7739, "℡": 7740, "↶": 7741, "⡃": 7742, "⡮": 7743, "⢈": 7744, "⠐": 7745, "ⷲ": 7746, "ꬵ": 7747, "𝟏": 7748, "𝞚": 7749, "𝚶": 7750, "𝝚": 7751, "𝒊": 7752, "ⱴ": 7753, "𝞂": 7754, "𝞼": 7755, "𝓹": 7756, "𝑨": 7757, "℆": 7758, "⇣": 7759, "↛": 7760, "⅏": 7761, "⇑": 7762, "⇵": 7763, "⩽": 7764, "⯓": 7765, "≼": 7766, "≻": 7767, "⩻": 7768, "⭻": 7769, "⩼": 7770, "⍼": 7771, "❼": 7772, "❻": 7773, "⥻": 7774, "⩺": 7775, "⭺": 7776, "❽": 7777, " ": 7778, "!": 7779, "\"": 7780, "#": 7781, "$": 7782, "%": 7783, "&": 7784, "'": 7785, "(": 7786, ")": 7787, "*": 7788, "+": 7789, ",": 7790, "-": 7791, ".": 7792, "/": 7793, "0": 7794, "1": 7795, "2": 7796, "3": 7797, "4": 7798, "5": 7799, "6": 7800, "7": 7801, "8": 7802, "9": 7803, ":": 7804, ";": 7805, "<": 7806, "=": 7807, ">": 7808, "?": 7809, "@": 7810, "A": 7811, "B": 7812, "C": 7813, "D": 7814, "E": 7815, "F": 7816, "G": 7817, "H": 7818, "I": 7819, "J": 7820, "K": 7821, "L": 7822, "M": 7823, "N": 7824, "O": 7825, "P": 7826, "Q": 7827, "R": 7828, "S": 7829, "T": 7830, "U": 7831, "V": 7832, "W": 7833, "X": 7834, "Y": 7835, "Z": 7836, "[": 7837, "\\": 7838, "]": 7839, "^": 7840, "_": 7841, "`": 7842, "a": 7843, "b": 7844, "c": 7845, "d": 7846, "e": 7847, "f": 7848, "g": 7849, "h": 7850, "i": 7851, "j": 7852, "k": 7853, "l": 7854, "m": 7855, "n": 7856, "o": 7857, "p": 7858, "q": 7859, "r": 7860, "s": 7861, "t": 7862, "u": 7863, "v": 7864, "w": 7865, "x": 7866, "y": 7867, "z": 7868, "{": 7869, "|": 7870, "}": 7871, "~": 7872, "the": 7873, "an": 7874, "and": 7875, "or": 7876, "but": 7877, "in": 7878, "on": 7879, "at": 7880, "to": 7881, "for": 7882, "of": 7883, "with": 7884, "by": 7885, "is": 7886, "are": 7887, "was": 7888, "were": 7889, "be": 7890, "been": 7891, "being": 7892, "have": 7893, "has": 7894, "had": 7895, "do": 7896, "does": 7897, "did": 7898, "if": 7899, "then": 7900, "else": 7901, "while": 7902, "return": 7903, "function": 7904, "class": 7905, "def": 7906, "import": 7907, "from": 7908, "create": 7909, "process": 7910, "data": 7911, "result": 7912, "value": 7913, "item": 7914, "list": 7915, "dict": 7916, "set": 7917, "true": 7918, "false": 7919}