{"version": "1.0", "truncation": null, "padding": null, "added_tokens": [{"id": 0, "content": "[UNK]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 1, "content": "[PAD]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 2, "content": "[CLS]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 3, "content": "[SEP]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}, {"id": 4, "content": "[MASK]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false, "special": true}], "normalizer": null, "pre_tokenizer": {"type": "Whitespace"}, "post_processor": {"type": "TemplateProcessing", "single": [{"SpecialToken": {"id": "[CLS]", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "[SEP]", "type_id": 0}}], "pair": [{"SpecialToken": {"id": "[CLS]", "type_id": 0}}, {"Sequence": {"id": "A", "type_id": 0}}, {"SpecialToken": {"id": "[SEP]", "type_id": 0}}, {"Sequence": {"id": "B", "type_id": 1}}, {"SpecialToken": {"id": "[SEP]", "type_id": 1}}], "special_tokens": {"[CLS]": {"id": "[CLS]", "ids": [2], "tokens": ["[CLS]"]}, "[SEP]": {"id": "[SEP]", "ids": [3], "tokens": ["[SEP]"]}}}, "decoder": null, "model": {"type": "WordPiece", "unk_token": "[UNK]", "continuing_subword_prefix": "##", "max_input_chars_per_word": 100, "vocab": {"[UNK]": 0, "[PAD]": 1, "[CLS]": 2, "[SEP]": 3, "[MASK]": 4, "<NG_START>": 5, "<NG_END>": 6, "<NG_THINK>": 7, "<NG_REASON>": 8, "<NG_MEMORY>": 9, "<NG_VALIDATE>": 10, "<NG_ERROR>": 11, "<NG_CORRECT>": 12, "a": 13, "b": 14, "c": 15, "d": 16, "e": 17, "f": 18, "g": 19, "h": 20, "i": 21, "j": 22, "k": 23, "l": 24, "m": 25, "n": 26, "o": 27, "p": 28, "q": 29, "r": 30, "s": 31, "t": 32, "u": 33, "v": 34, "w": 35, "x": 36, "y": 37, "z": 38, "A": 39, "B": 40, "C": 41, "D": 42, "E": 43, "F": 44, "G": 45, "H": 46, "I": 47, "J": 48, "K": 49, "L": 50, "M": 51, "N": 52, "O": 53, "P": 54, "Q": 55, "R": 56, "S": 57, "T": 58, "U": 59, "V": 60, "W": 61, "X": 62, "Y": 63, "Z": 64, "0": 65, "1": 66, "2": 67, "3": 68, "4": 69, "5": 70, "6": 71, "7": 72, "8": 73, "9": 74, ".": 75, ",": 76, "!": 77, "?": 78, ";": 79, ":": 80, "(": 81, ")": 82, "[": 83, "]": 84, "{": 85, "}": 86, "\"": 87, "'": 88, "-": 89, "_": 90, "/": 91, "\\": 92, "@": 93, "#": 94, "$": 95, "%": 96, "^": 97, "&": 98, "*": 99, "+": 100, "=": 101, "<": 102, ">": 103, "|": 104, "`": 105, "~": 106, "⦟": 111, "⌮": 112, "⨑": 113, "≅": 114, "✗": 115, "⧹": 116, "✿": 117, "⬤": 118, "⬛": 119, "⬧": 120, "✂": 121, "⬮": 122, "⨸": 123, "⧁": 124, "⧀": 125, "⦹": 126, "⦶": 127, "✪": 128, "⥁": 129, "⫏": 130, "⌲": 131, "∮": 132, "⌵": 133, "⌭": 134, "⦙": 135, "⩎": 136, "⫬": 137, "⤓": 138, "✴": 139, "✵": 140, "✳": 141, "⌫": 142, "⌦": 143, "⧳": 144, "✥": 145, "✢": 146, "⪥": 147, "❤": 148, "✔": 149, "❆": 150, "⭙": 151, "❗": 152, "❚": 153, "∻": 154, "⬣": 155, "⨚": 156, "⨼": 157, "⩄": 158, "⩃": 159, "⦓": 160, "⯨": 161, "⧘": 162, "⤌": 163, "⭅": 164, "❘": 165, "⬳": 166, "✠": 167, "⦛": 168, "⨺": 169, "⨩": 170, "⨫": 171, "⨬": 172, "≂": 173, "⨂": 174, "⯜": 175, "⤤": 176, "⬀": 177, "✲": 178, "✫": 179, "⌥": 180, "∥": 181, "✏": 182, "⯛": 183, "✯": 184, "⨭": 185, "⨹": 186, "⨥": 187, "≟": 188, "✊": 189, "⮐": 190, "⦣": 191, "⮶": 192, "⮷": 193, "〉": 194, "⥇": 195, "⭆": 196, "✞": 197, "⫟": 198, "⧢": 199, "✶": 200, "✺": 201, "⌳": 202, "⩘": 203, "⩡": 204, "⌣": 205, "❄": 206, "⤦": 207, "❇": 208, "⦠": 209, "⫍": 210, "⬒": 211, "✡": 212, "✩": 213, "∴": 214, "⬱": 215, "❅": 216, "⧕": 217, "⯗": 218, "∭": 219, "⩁": 220, "⭡": 221, "⯦": 222, "○": 223, "✧": 224, "⬡": 225, "⬭": 226, "◁": 227, "⬨": 228, "⬯": 229, "⬞": 230, "≀": 231, "⌧": 232, "⤨": 233, "❫": 234, "⭌": 235, "⥐": 236, "⍃": 237, "✾": 238, "⤾": 239, "⤿": 240, "⧍": 241, "⭠": 242, "⍂": 243, "⍣": 244, "❊": 245, "⧃": 246, "⭁": 247, "⌹": 248, "❃": 249, "❜": 250, "⤱": 251, "⩊": 252, "⍀": 253, "➬": 254, "⨣": 255, "⩍": 256, "⭀": 257, "⌤": 258, "⥣": 259, "⦜": 260, "⭄": 261, "❝": 262, "⤕": 263, "⥚": 264, "⩐": 265, "⋚": 266, "⥢": 267, "⮟": 268, "⌼": 269, "⍙": 270, "❐": 271, "⤻": 272, "⥙": 273, "⦴": 274, "⨻": 275, "⬻": 276, "⍒": 277, "⥂": 278, "⦩": 279, "⪒": 280, "⮙": 281, "❑": 282, "⤪": 283, "⦪": 284, "⌿": 285, "⥀": 286, "⥉": 287, "⥘": 288, "⪨": 289, "⭼": 290, "⤳": 291, "⥫": 292, "⦲": 293, "⫉": 294, "⬸": 295, "➞": 296, "⭭": 297, "⨴": 298, "⬵": 299, "⍆": 300, "⍘": 301, "❠": 302, "❡": 303, "⤑": 304, "⤢": 305, "⥛": 306, "⥝": 307, "⥡": 308, "⦐": 309, "⦖": 310, "⦮": 311, "⦺": 312, "⧩": 313, "⧷": 314, "⭈": 315, "⮈": 316, "⯅": 317, "⯮": 318, "✼": 319, "⤰": 320, "⥸": 321, "⦯": 322, "⧂": 323, "⫄": 324, "⤷": 325, "⥟": 326, "⌷": 327, "✷": 328, "⤲": 329, "⤶": 330, "⨰": 331, "⬶": 332, "⢟": 333, "⡨": 334, "ꭐ": 335, "⟷": 336, "𝚳": 337, "𝛘": 338, "𝐡": 339, "𝑜": 340, "𝒪": 341, "⟖": 342, "𝟫": 343, "ꜥ": 344, "𝗀": 345, "ⷸ": 346, "𝛽": 347, "𝓔": 348, "Ꞔ": 349, "𝒍": 350, "𝚢": 351, "𝘉": 352, "𝕃": 353, "𝘓": 354, "𝝭": 355, "𝕚": 356, "⌽": 357, "⍚": 358, "⍠": 359, "⍤": 360, "≐": 361, "≆": 362, "⏟": 363, "⑮": 364, "⑰": 365, "⊕": 366, "∁": 367, "⋺": 368, "⒌": 369, "∕": 370, "⋐": 371, "⊤": 372, "↯": 373, "–": 374, "⋜": 376, "≕": 377, "₣": 378, "≎": 379, "⋛": 380, "≥": 381, "⎀": 382, "⎮": 383, "∩": 384, "₭": 385, "⋋": 386, "⇜": 387, "∡": 388, "∓": 389, "⊭": 390, "⊶": 391, "∂": 392, "⏽": 393, "∶": 394, "⏎": 395, "”": 396, "ⅸ": 397, "⊐": 398, "⏤": 399, "∄": 400, "⌜": 401, "⏠": 402, "≋": 403, "⎌": 404, "⋰": 405, "⋘": 406, "ng:ai:embedding": 407, "ng:ai:embedding_5": 408, "ng:ai:embedding_6": 409, "ng:ai:embedding_7": 410, "ng:ai:embedding_8": 411, "ng:ai:embedding_10": 412, "ng:ai:embedding_12": 413, "ng:ai:embedding_15": 414, "ng:ai:embedding_16": 415, "ng:ai:embedding_17": 416, "ng:ai:embedding_20": 417, "ng:ai:embedding_24": 418, "ng:ai:embedding_25": 419, "ng:ai:embedding_26": 420, "ng:ai:embedding_27": 421, "ng:ai:embedding_28": 422, "ng:ai:embedding_29": 423, "ng:ai:embedding_30": 424, "ng:ai:embedding_31": 425, "ng:ai:embedding_32": 426, "ng:ai:embedding_33": 427, "ng:ai:embedding_34": 428, "ng:ai:embedding_35": 429, "ng:ai:embedding_36": 430, "ng:ai:embedding_37": 431, "ng:ai:embedding_38": 432, "ng:ai:embedding_39": 433, "ng:ai:embedding_40": 434, "ng:ai:embedding_41": 435, "ng:ai:embedding_42": 436, "ng:ai:embedding_43": 437, "ng:ai:embedding_44": 438, "ng:ai:embedding_45": 439, "ng:ai:embedding_46": 440, "ng:ai:embedding_47": 441, "ng:ai:embedding_48": 442, "ng:ai:embedding_49": 443, "ng:ai:embedding_50": 444, "ng:ai:embedding_51": 445, "ng:ai:embedding_52": 446, "ng:ai:embedding_53": 447, "ng:ai:embedding_54": 448, "ng:ai:embedding_55": 449, "ng:ai:embedding_56": 450, "ng:ai:embedding_57": 451, "ng:ai:embedding_58": 452, "ng:ai:embedding_59": 453, "ng:ai:embedding_60": 454, "ng:ai:embedding_61": 455, "ng:ai:embedding_62": 456, "ng:ai:embedding_63": 457, "ng:ai:embedding_64": 458, "ng:ai:embedding_65": 459, "ng:ai:embedding_66": 460, "ng:ai:embedding_67": 461, "ng:ai:embedding_68": 462, "ng:ai:embedding_69": 463, "ng:ai:embedding_70": 464, "ng:ai:embedding_71": 465, "ng:ai:embedding_72": 466, "ng:ai:embedding_73": 467, "ng:ai:embedding_74": 468, "ng:ai:embedding_75": 469, "ng:ai:embedding_76": 470, "ng:ai:embedding_77": 471, "ng:ai:embedding_78": 472, "ng:ai:embedding_79": 473, "ng:ai:embedding_80": 474, "ng:ai:embedding_81": 475, "ng:ai:embedding_82": 476, "ng:ai:embedding_83": 477, "ng:ai:embedding_84": 478, "ng:ai:embedding_85": 479, "ng:ai:embedding_86": 480, "ng:ai:embedding_87": 481, "ng:ai:reasoning": 482, "ng:ai:agent_1": 483, "ng:ai:reasoning_1": 484, "ng:ai:planner_3": 485, "ng:ai:attention_4": 486, "ng:ai:transformer_4": 487, "ng:ai:neural_4": 488, "ng:ai:agent_5": 489, "ng:ai:planner_5": 490, "ng:ai:neural_5": 491, "ng:ai:planner_6": 492, "ng:ai:gradient_6": 493, "ng:ai:transformer_6": 494, "ng:ai:neural_6": 495, "ng:ai:agent_7": 496, "ng:ai:planner_7": 497, "ng:ai:gradient_7": 498, "ng:ai:neural_7": 499, "ng:ai:agent_8": 500, "ng:ai:planner_8": 501, "ng:ai:reasoning_8": 502, "ng:ai:gradient_8": 503, "ng:ai:attention_8": 504, "ng:ai:neural_8": 505, "ng:ai:agent_9": 506, "ng:ai:planner_9": 507, "ng:ai:gradient_9": 508, "ng:ai:agent_10": 509, "ng:ai:reasoning_10": 510, "ng:ai:attention_10": 511, "ng:ai:transformer_10": 512, "ng:ai:attention_11": 513, "ng:ai:neural_11": 514, "ng:ai:agent_12": 515, "ng:ai:planner_12": 516, "ng:ai:reasoning_12": 517, "ng:ai:transformer_12": 518, "ng:ai:reasoning_13": 519, "ng:ai:gradient_13": 520, "ng:ai:neural_13": 521, "ng:ai:planner_14": 522, "ng:ai:reasoning_14": 523, "ng:ai:gradient_14": 524, "ng:ai:attention_14": 525, "ng:ai:neural_14": 526, "ng:ai:agent_15": 527, "ng:ai:planner_15": 528, "ng:ai:reasoning_15": 529, "ng:ai:gradient_15": 530, "ng:ai:attention_15": 531, "ng:ai:planner_16": 532, "ng:ai:reasoning_16": 533, "ng:ai:gradient_16": 534, "ng:ai:attention_16": 535, "ng:ai:transformer_16": 536, "ng:ai:agent_17": 537, "ng:ai:reasoning_17": 538, "ng:ai:attention_17": 539, "ng:ai:neural_17": 540, "ng:ai:agent_18": 541, "ng:ai:reasoning_18": 542, "ng:ai:gradient_18": 543, "ng:ai:attention_18": 544, "ng:ai:planner_19": 545, "ng:ai:reasoning_19": 546, "ng:ai:gradient_19": 547, "ng:ai:attention_19": 548, "ng:ai:transformer_19": 549, "ng:ai:agent_20": 550, "ng:ai:planner_20": 551, "ng:ai:agent_21": 552, "ng:ai:reasoning_21": 553, "ng:ai:gradient_21": 554, "ng:ai:planner_22": 555, "ng:ai:attention_22": 556, "ng:ai:neural_22": 557, "ng:ai:agent_24": 558, "ng:ai:planner_24": 559, "ng:ai:reasoning_24": 560, "ng:ai:gradient_24": 561, "ng:ai:attention_24": 562, "ng:ai:transformer_24": 563, "ng:ai:neural_24": 564, "ng:ai:agent_25": 565, "ng:ai:planner_25": 566, "ng:ai:reasoning_25": 567, "ng:ai:gradient_25": 568, "ng:ai:attention_25": 569, "ng:ai:transformer_25": 570, "ng:ai:neural_25": 571, "ng:ai:agent_26": 572, "ng:ai:planner_26": 573, "ng:ai:reasoning_26": 574, "ng:ai:gradient_26": 575, "ng:ai:attention_26": 576, "ng:ai:transformer_26": 577, "ng:ai:neural_26": 578, "ng:ai:agent_27": 579, "ng:ai:planner_27": 580, "ng:ai:reasoning_27": 581, "ng:ai:gradient_27": 582, "ng:ai:attention_27": 583, "ng:ai:transformer_27": 584, "ng:ai:neural_27": 585, "ng:ai:agent_28": 586, "ng:ai:planner_28": 587, "ng:ai:reasoning_28": 588, "ng:ai:gradient_28": 589, "ng:ai:attention_28": 590, "ng:ai:transformer_28": 591, "ng:ai:neural_28": 592, "ng:ai:agent_29": 593, "ng:ai:planner_29": 594, "ng:ai:reasoning_29": 595, "ng:ai:gradient_29": 596, "ng:ai:attention_29": 597, "ng:ai:transformer_29": 598, "ng:ai:neural_29": 599, "ng:ai:agent_30": 600, "ng:ai:planner_30": 601, "ng:ai:reasoning_30": 602, "ng:ai:gradient_30": 603, "ng:ai:attention_30": 604, "ng:ai:transformer_30": 605, "ng:ai:neural_30": 606, "ng:ai:agent_31": 607, "ng:ai:planner_31": 608, "ng:ai:reasoning_31": 609, "ng:ai:gradient_31": 610, "ng:ai:attention_31": 611, "ng:ai:transformer_31": 612, "ng:ai:neural_31": 613, "ng:ai:agent_32": 614, "ng:ai:planner_32": 615, "ng:ai:reasoning_32": 616, "ng:ai:gradient_32": 617, "ng:ai:attention_32": 618, "ng:ai:transformer_32": 619, "ng:ai:neural_32": 620, "ng:ai:agent_33": 621, "ng:ai:planner_33": 622, "ng:ai:reasoning_33": 623, "ng:ai:gradient_33": 624, "ng:ai:attention_33": 625, "ng:ai:transformer_33": 626, "ng:ai:neural_33": 627, "ng:ai:agent_34": 628, "ng:ai:planner_34": 629, "ng:ai:reasoning_34": 630, "ng:ai:gradient_34": 631, "ng:ai:attention_34": 632, "ng:ai:transformer_34": 633, "ng:ai:neural_34": 634, "ng:ai:agent_35": 635, "ng:ai:planner_35": 636, "ng:ai:reasoning_35": 637, "ng:ai:gradient_35": 638, "ng:ai:attention_35": 639, "ng:ai:transformer_35": 640, "ng:ai:neural_35": 641, "ng:ai:agent_36": 642, "ng:ai:planner_36": 643, "ng:ai:reasoning_36": 644, "ng:ai:gradient_36": 645, "ng:ai:attention_36": 646, "ng:ai:transformer_36": 647, "ng:ai:neural_36": 648, "ng:ai:agent_37": 649, "ng:ai:planner_37": 650, "ng:ai:reasoning_37": 651, "ng:ai:gradient_37": 652, "ng:ai:attention_37": 653, "ng:ai:transformer_37": 654, "ng:ai:neural_37": 655, "ng:ai:agent_38": 656, "ng:ai:planner_38": 657, "ng:ai:reasoning_38": 658, "ng:ai:gradient_38": 659, "ng:ai:attention_38": 660, "ng:ai:transformer_38": 661, "ng:ai:neural_38": 662, "ng:ai:agent_39": 663, "ng:ai:planner_39": 664, "ng:ai:reasoning_39": 665, "ng:ai:gradient_39": 666, "ng:ai:attention_39": 667, "ng:ai:transformer_39": 668, "ng:ai:neural_39": 669, "ng:ai:agent_40": 670, "ng:ai:planner_40": 671, "ng:ai:reasoning_40": 672, "ng:ai:gradient_40": 673, "ng:ai:attention_40": 674, "ng:ai:transformer_40": 675, "ng:ai:neural_40": 676, "ng:ai:agent_41": 677, "ng:ai:planner_41": 678, "ng:ai:reasoning_41": 679, "ng:ai:gradient_41": 680, "ng:ai:attention_41": 681, "ng:ai:transformer_41": 682, "ng:ai:neural_41": 683, "ng:ai:agent_42": 684, "ng:ai:planner_42": 685, "ng:ai:reasoning_42": 686, "ng:ai:gradient_42": 687, "ng:ai:attention_42": 688, "ng:ai:transformer_42": 689, "ng:ai:neural_42": 690, "ng:ai:agent_43": 691, "ng:ai:planner_43": 692, "ng:ai:reasoning_43": 693, "ng:ai:gradient_43": 694, "ng:ai:attention_43": 695, "ng:ai:transformer_43": 696, "ng:ai:neural_43": 697, "ng:ai:agent_44": 698, "ng:ai:planner_44": 699, "ng:ai:reasoning_44": 700, "ng:ai:gradient_44": 701, "ng:ai:attention_44": 702, "ng:ai:transformer_44": 703, "ng:ai:neural_44": 704, "ng:ai:agent_45": 705, "ng:ai:planner_45": 706, "ng:ai:reasoning_45": 707, "ng:ai:gradient_45": 708, "ng:ai:attention_45": 709, "ng:ai:transformer_45": 710, "ng:ai:neural_45": 711, "ng:ai:agent_46": 712, "ng:ai:planner_46": 713, "ng:ai:reasoning_46": 714, "ng:ai:gradient_46": 715, "ng:ai:attention_46": 716, "ng:ai:transformer_46": 717, "ng:ai:neural_46": 718, "ng:ai:agent_47": 719, "ng:ai:planner_47": 720, "ng:ai:reasoning_47": 721, "ng:ai:gradient_47": 722, "ng:ai:attention_47": 723, "ng:ai:transformer_47": 724, "ng:ai:neural_47": 725, "ng:ai:agent_48": 726, "ng:ai:planner_48": 727, "ng:ai:reasoning_48": 728, "ng:ai:gradient_48": 729, "ng:ai:attention_48": 730, "ng:ai:transformer_48": 731, "ng:ai:neural_48": 732, "ng:ai:agent_49": 733, "ng:ai:planner_49": 734, "ng:ai:reasoning_49": 735, "ng:ai:gradient_49": 736, "ng:ai:attention_49": 737, "ng:ai:transformer_49": 738, "ng:ai:neural_49": 739, "ng:ai:agent_50": 740, "ng:ai:planner_50": 741, "ng:ai:reasoning_50": 742, "ng:ai:gradient_50": 743, "ng:ai:attention_50": 744, "ng:ai:transformer_50": 745, "ng:ai:neural_50": 746, "ng:ai:agent_51": 747, "ng:ai:planner_51": 748, "ng:ai:reasoning_51": 749, "ng:ai:gradient_51": 750, "ng:ai:attention_51": 751, "ng:ai:transformer_51": 752, "ng:ai:neural_51": 753, "ng:ai:agent_52": 754, "ng:ai:planner_52": 755, "ng:ai:reasoning_52": 756, "ng:ai:gradient_52": 757, "ng:ai:attention_52": 758, "ng:ai:transformer_52": 759, "ng:ai:neural_52": 760, "ng:ai:agent_53": 761, "ng:ai:planner_53": 762, "ng:ai:reasoning_53": 763, "ng:ai:gradient_53": 764, "ng:ai:attention_53": 765, "ng:ai:transformer_53": 766, "ng:ai:neural_53": 767, "ng:ai:agent_54": 768, "ng:ai:planner_54": 769, "ng:ai:reasoning_54": 770, "ng:ai:gradient_54": 771, "ng:ai:attention_54": 772, "ng:ai:transformer_54": 773, "ng:ai:neural_54": 774, "ng:ai:agent_55": 775, "ng:ai:planner_55": 776, "ng:ai:reasoning_55": 777, "ng:ai:gradient_55": 778, "ng:ai:attention_55": 779, "ng:ai:transformer_55": 780, "ng:ai:neural_55": 781, "ng:ai:agent_56": 782, "ng:ai:planner_56": 783, "ng:ai:reasoning_56": 784, "ng:ai:gradient_56": 785, "ng:ai:attention_56": 786, "ng:ai:transformer_56": 787, "ng:ai:neural_56": 788, "ng:ai:agent_57": 789, "ng:ai:planner_57": 790, "ng:ai:reasoning_57": 791, "ng:ai:gradient_57": 792, "ng:ai:attention_57": 793, "ng:ai:transformer_57": 794, "ng:ai:neural_57": 795, "ng:ai:agent_58": 796, "ng:ai:planner_58": 797, "ng:ai:reasoning_58": 798, "ng:ai:gradient_58": 799, "ng:ai:attention_58": 800, "ng:ai:transformer_58": 801, "ng:ai:neural_58": 802, "ng:ai:agent_59": 803, "ng:ai:planner_59": 804, "ng:ai:reasoning_59": 805, "ng:ai:gradient_59": 806, "ng:ai:attention_59": 807, "ng:ai:transformer_59": 808, "ng:ai:neural_59": 809, "ng:ai:agent_60": 810, "ng:ai:planner_60": 811, "ng:ai:reasoning_60": 812, "ng:ai:gradient_60": 813, "ng:ai:attention_60": 814, "ng:ai:transformer_60": 815, "ng:ai:neural_60": 816, "ng:ai:agent_61": 817, "ng:ai:planner_61": 818, "ng:ai:reasoning_61": 819, "ng:ai:gradient_61": 820, "ng:ai:attention_61": 821, "ng:ai:transformer_61": 822, "ng:ai:neural_61": 823, "ng:ai:agent_62": 824, "ng:ai:planner_62": 825, "ng:ai:reasoning_62": 826, "ng:ai:gradient_62": 827, "ng:ai:attention_62": 828, "ng:ai:transformer_62": 829, "ng:ai:neural_62": 830, "ng:ai:agent_63": 831, "ng:ai:planner_63": 832, "ng:ai:reasoning_63": 833, "ng:ai:gradient_63": 834, "ng:ai:attention_63": 835, "ng:ai:transformer_63": 836, "ng:ai:neural_63": 837, "ng:ai:agent_64": 838, "ng:ai:planner_64": 839, "ng:ai:reasoning_64": 840, "ng:ai:gradient_64": 841, "ng:ai:attention_64": 842, "ng:ai:transformer_64": 843, "ng:ai:neural_64": 844, "ng:ai:agent_65": 845, "ng:ai:planner_65": 846, "ng:ai:reasoning_65": 847, "ng:ai:gradient_65": 848, "ng:ai:attention_65": 849, "ng:ai:transformer_65": 850, "ng:ai:neural_65": 851, "ng:ai:agent_66": 852, "ng:ai:planner_66": 853, "ng:ai:reasoning_66": 854, "ng:ai:gradient_66": 855, "ng:ai:attention_66": 856, "ng:ai:transformer_66": 857, "ng:ai:neural_66": 858, "ng:ai:agent_67": 859, "ng:ai:planner_67": 860, "ng:ai:reasoning_67": 861, "ng:ai:gradient_67": 862, "ng:ai:attention_67": 863, "ng:ai:transformer_67": 864, "ng:ai:neural_67": 865, "ng:ai:agent_68": 866, "ng:ai:planner_68": 867, "ng:ai:reasoning_68": 868, "ng:ai:gradient_68": 869, "ng:ai:attention_68": 870, "ng:ai:transformer_68": 871, "ng:ai:neural_68": 872, "ng:ai:agent_69": 873, "ng:ai:planner_69": 874, "ng:ai:reasoning_69": 875, "ng:ai:gradient_69": 876, "ng:ai:attention_69": 877, "ng:ai:transformer_69": 878, "ng:ai:neural_69": 879, "ng:ai:agent_70": 880, "ng:ai:planner_70": 881, "ng:ai:reasoning_70": 882, "ng:ai:gradient_70": 883, "ng:ai:attention_70": 884, "ng:ai:transformer_70": 885, "ng:ai:neural_70": 886, "ng:ai:agent_71": 887, "ng:ai:planner_71": 888, "ng:ai:reasoning_71": 889, "ng:ai:gradient_71": 890, "ng:ai:attention_71": 891, "ng:ai:transformer_71": 892, "ng:ai:neural_71": 893, "ng:ai:agent_72": 894, "ng:ai:planner_72": 895, "ng:ai:reasoning_72": 896, "ng:ai:gradient_72": 897, "ng:ai:attention_72": 898, "ng:ai:transformer_72": 899, "ng:ai:neural_72": 900, "ng:ai:agent_73": 901, "ng:ai:planner_73": 902, "ng:ai:reasoning_73": 903, "ng:ai:gradient_73": 904, "ng:ai:attention_73": 905, "ng:ai:transformer_73": 906, "ng:ai:neural_73": 907, "ng:ai:agent_74": 908, "ng:ai:planner_74": 909, "ng:ai:reasoning_74": 910, "ng:ai:gradient_74": 911, "ng:ai:attention_74": 912, "ng:ai:transformer_74": 913, "ng:ai:neural_74": 914, "ng:ai:agent_75": 915, "ng:ai:planner_75": 916, "ng:ai:reasoning_75": 917, "ng:ai:gradient_75": 918, "ng:ai:attention_75": 919, "ng:ai:transformer_75": 920, "ng:ai:neural_75": 921, "ng:ai:agent_76": 922, "ng:ai:planner_76": 923, "ng:ai:reasoning_76": 924, "ng:ai:gradient_76": 925, "ng:ai:attention_76": 926, "ng:ai:transformer_76": 927, "ng:ai:neural_76": 928, "ng:ai:agent_77": 929, "ng:ai:planner_77": 930, "ng:ai:reasoning_77": 931, "ng:ai:gradient_77": 932, "ng:ai:attention_77": 933, "ng:ai:transformer_77": 934, "ng:ai:neural_77": 935, "ng:ai:agent_78": 936, "ng:ai:planner_78": 937, "ng:ai:reasoning_78": 938, "ng:ai:gradient_78": 939, "ng:ai:attention_78": 940, "ng:ai:transformer_78": 941, "ng:ai:neural_78": 942, "ng:ai:agent_79": 943, "ng:ai:planner_79": 944, "ng:ai:reasoning_79": 945, "ng:ai:gradient_79": 946, "ng:ai:attention_79": 947, "ng:ai:transformer_79": 948, "ng:ai:neural_79": 949, "ng:ai:agent_80": 950, "ng:ai:planner_80": 951, "ng:ai:reasoning_80": 952, "ng:ai:gradient_80": 953, "ng:ai:attention_80": 954, "ng:ai:transformer_80": 955, "ng:ai:neural_80": 956, "ng:ai:agent_81": 957, "ng:ai:planner_81": 958, "ng:ai:reasoning_81": 959, "ng:ai:gradient_81": 960, "ng:ai:attention_81": 961, "ng:ai:transformer_81": 962, "ng:ai:neural_81": 963, "ng:ai:agent_82": 964, "ng:ai:planner_82": 965, "ng:ai:reasoning_82": 966, "ng:ai:gradient_82": 967, "ng:ai:attention_82": 968, "ng:ai:transformer_82": 969, "ng:ai:neural_82": 970, "ng:ai:agent_83": 971, "ng:ai:planner_83": 972, "ng:ai:reasoning_83": 973, "ng:ai:gradient_83": 974, "ng:ai:attention_83": 975, "ng:ai:transformer_83": 976, "ng:ai:neural_83": 977, "ng:ai:agent_84": 978, "ng:ai:planner_84": 979, "ng:ai:reasoning_84": 980, "ng:ai:gradient_84": 981, "ng:ai:attention_84": 982, "ng:ai:transformer_84": 983, "ng:ai:neural_84": 984, "ng:ai:agent_85": 985, "ng:ai:planner_85": 986, "ng:ai:reasoning_85": 987, "ng:ai:gradient_85": 988, "ng:ai:attention_85": 989, "ng:ai:transformer_85": 990, "ng:ai:neural_85": 991, "ng:ai:agent_86": 992, "ng:ai:planner_86": 993, "ng:ai:reasoning_86": 994, "ng:ai:gradient_86": 995, "ng:ai:attention_86": 996, "ng:ai:transformer_86": 997, "ng:ai:neural_86": 998, "ng:ai:agent_87": 999, "ng:ai:planner_87": 1000, "ng:ai:reasoning_87": 1001, "ng:ai:gradient_87": 1002, "ng:ai:attention_87": 1003, "ng:ai:transformer_87": 1004, "ng:ai:neural_87": 1005, "℻": 1006, "⇐": 1007, "⇴": 1008, "↬": 1009, "↾": 1010, "ℙ": 1011, "ℽ": 1012, "⇤": 1013, "↜": 1014, "⇶": 1015, "⣲": 1016, "⣸": 1017, "⠨": 1018, "ⷩ": 1019, "ⷡ": 1020, "Ɡ": 1021, "ꞏ": 1022, "⸨": 1023, "𝒞": 1024, "𝒴": 1025, "⸬": 1026, "𝔙": 1027, "𝘥": 1028, "ꟃ": 1029, "𝔅": 1030, "𝕫": 1031, "𝟷": 1032, "𝖦": 1033, "𝑷": 1034, "⇇": 1035, "ℎ": 1036, "↣": 1037, "↑": 1038, "ⅅ": 1039, "⅍": 1040, "ℨ": 1041, "№": 1042, "℺": 1043, "⇏": 1044, "⍺": 1045, "⧸": 1046, "➿": 1047, "≷": 1048, "➚": 1049, "≶": 1050, "⩹": 1051, "⧶": 1052, "⥹": 1053, "⯟": 1054, "⍷": 1055, "⭶": 1056, "≸": 1057, "❸": 1058, "⍶": 1059, "❶": 1060, "⭵": 1061, "❷": 1062, "⥷": 1063, "⫲": 1064, "❹": 1065, "❺": 1066, "⥺": 1067, "⭹": 1068, "⍹": 1069, "⩸": 1070, "🚛": 1071, "🚗": 1072, "🚼": 1073, "🛄": 1074, "🛀": 1075, "🛁": 1076, "🛉": 1077, "🚸": 1078, "🛈": 1079, "🛋": 1080, "🛃": 1081, "🚚": 1082, "🚪": 1083, "🛊": 1084, "🛅": 1085, "🚹": 1086, "🚝": 1087, "🚞": 1088, "🚫": 1089, "🚭": 1090, "🚘": 1091, "🚖": 1092, "🛂": 1093, "🛇": 1094, "🚙": 1095, "🚻": 1096, "🚿": 1097, "🚬": 1098, "🚕": 1099, "🚽": 1100, "🚜": 1101, "🛆": 1102, "🚩": 1103, "🚾": 1104, "🚺": 1105, "②": 1106, "⑬": 1107, "⇫": 1108, "⇻": 1109, "⋩": 1110, "⍄": 1111, "⍋": 1112, "⋄": 1113, "‡": 1114, "ℝ": 1115, "∎": 1117, "∜": 1118, "≑": 1119, "―": 1120, "⇔": 1121, "∑": 1122, "⑻": 1123, "⑸": 1124, "≾": 1125, "∷": 1126, "⊾": 1127, "ⅴ": 1128, "⏱": 1129, "⁸": 1130, "⇳": 1131, "⇡": 1132, "⅑": 1133, "ng:code:optimize": 1134, "ng:code:async_1": 1135, "ng:code:async_2": 1136, "ng:code:pattern_2": 1137, "ng:code:refactor_2": 1138, "ng:code:pattern_3": 1139, "ng:code:closure_4": 1140, "ng:code:recursion_4": 1141, "ng:code:ast_4": 1142, "ng:code:loop_4": 1143, "ng:code:async_4": 1144, "ng:code:pattern_4": 1145, "ng:code:refactor_4": 1146, "ng:code:optimize_4": 1147, "ng:code:loop_5": 1148, "ng:code:async_5": 1149, "ng:code:pattern_5": 1150, "ng:code:closure_6": 1151, "ng:code:loop_6": 1152, "ng:code:pattern_6": 1153, "ng:code:closure_7": 1154, "ng:code:ast_7": 1155, "ng:code:loop_7": 1156, "ng:code:optimize_7": 1157, "ng:code:recursion_8": 1158, "ng:code:ast_8": 1159, "ng:code:loop_8": 1160, "ng:code:async_8": 1161, "ng:code:optimize_8": 1162, "ng:code:pattern_9": 1163, "ng:code:loop_10": 1164, "ng:code:pattern_10": 1165, "ng:code:refactor_10": 1166, "ng:code:ast_11": 1167, "ng:code:loop_11": 1168, "ng:code:async_11": 1169, "ng:code:pattern_11": 1170, "ng:code:recursion_12": 1171, "ng:code:async_12": 1172, "ng:code:closure_13": 1173, "ng:code:ast_13": 1174, "ng:code:loop_13": 1175, "ng:code:refactor_13": 1176, "ng:code:closure_14": 1177, "ng:code:recursion_14": 1178, "ng:code:ast_14": 1179, "ng:code:async_14": 1180, "ng:code:pattern_14": 1181, "ng:code:refactor_14": 1182, "ng:code:recursion_15": 1183, "ng:code:ast_15": 1184, "ng:code:loop_15": 1185, "ng:code:async_15": 1186, "ng:code:closure_16": 1187, "ng:code:recursion_16": 1188, "ng:code:loop_16": 1189, "ng:code:async_16": 1190, "ng:code:pattern_16": 1191, "ng:code:refactor_16": 1192, "ng:code:closure_17": 1193, "ng:code:recursion_17": 1194, "ng:code:pattern_17": 1195, "ng:code:refactor_17": 1196, "ng:code:optimize_17": 1197, "ng:code:closure_18": 1198, "ng:code:ast_18": 1199, "ng:code:refactor_18": 1200, "ng:code:optimize_18": 1201, "ng:code:recursion_19": 1202, "ng:code:ast_19": 1203, "ng:code:loop_19": 1204, "ng:code:async_19": 1205, "ng:code:pattern_19": 1206, "ng:code:closure_20": 1207, "ng:code:recursion_20": 1208, "ng:code:ast_20": 1209, "ng:code:pattern_20": 1210, "ng:code:closure_21": 1211, "ng:code:loop_21": 1212, "ng:code:recursion_22": 1213, "ng:code:ast_22": 1214, "ng:code:loop_22": 1215, "ng:code:async_22": 1216, "ng:code:async_23": 1217, "ng:code:closure_24": 1218, "ng:code:recursion_24": 1219, "ng:code:ast_24": 1220, "ng:code:loop_24": 1221, "ng:code:async_24": 1222, "ng:code:pattern_24": 1223, "ng:code:refactor_24": 1224, "ng:code:optimize_24": 1225, "ng:code:closure_25": 1226, "ng:code:recursion_25": 1227, "ng:code:ast_25": 1228, "ng:code:loop_25": 1229, "ng:code:async_25": 1230, "ng:code:pattern_25": 1231, "ng:code:refactor_25": 1232, "ng:code:optimize_25": 1233, "ng:code:closure_26": 1234, "ng:code:recursion_26": 1235, "ng:code:ast_26": 1236, "ng:code:loop_26": 1237, "ng:code:async_26": 1238, "ng:code:pattern_26": 1239, "ng:code:refactor_26": 1240, "ng:code:optimize_26": 1241, "ng:code:closure_27": 1242, "ng:code:recursion_27": 1243, "ng:code:ast_27": 1244, "ng:code:loop_27": 1245, "ng:code:async_27": 1246, "ng:code:pattern_27": 1247, "ng:code:refactor_27": 1248, "ng:code:optimize_27": 1249, "ng:code:closure_28": 1250, "ng:code:recursion_28": 1251, "ng:code:ast_28": 1252, "ng:code:loop_28": 1253, "ng:code:async_28": 1254, "ng:code:pattern_28": 1255, "ng:code:refactor_28": 1256, "ng:code:optimize_28": 1257, "ng:code:closure_29": 1258, "ng:code:ast_29": 1259, "ng:code:loop_29": 1260, "ng:code:async_29": 1261, "ng:code:pattern_29": 1262, "ng:code:refactor_29": 1263, "ng:code:optimize_29": 1264, "ng:code:closure_30": 1265, "ng:code:recursion_30": 1266, "ng:code:ast_30": 1267, "ng:code:loop_30": 1268, "ng:code:async_30": 1269, "ng:code:pattern_30": 1270, "ng:code:refactor_30": 1271, "ng:code:optimize_30": 1272, "ng:code:closure_31": 1273, "ng:code:recursion_31": 1274, "ng:code:ast_31": 1275, "ng:code:loop_31": 1276, "ng:code:async_31": 1277, "ng:code:pattern_31": 1278, "ng:code:refactor_31": 1279, "ng:code:optimize_31": 1280, "ng:code:closure_32": 1281, "ng:code:recursion_32": 1282, "ng:code:ast_32": 1283, "ng:code:loop_32": 1284, "ng:code:async_32": 1285, "ng:code:pattern_32": 1286, "ng:code:refactor_32": 1287, "ng:code:optimize_32": 1288, "ng:code:closure_33": 1289, "ng:code:recursion_33": 1290, "ng:code:ast_33": 1291, "ng:code:loop_33": 1292, "ng:code:async_33": 1293, "ng:code:pattern_33": 1294, "ng:code:refactor_33": 1295, "ng:code:optimize_33": 1296, "ng:code:closure_34": 1297, "ng:code:recursion_34": 1298, "ng:code:ast_34": 1299, "ng:code:loop_34": 1300, "ng:code:async_34": 1301, "ng:code:pattern_34": 1302, "ng:code:refactor_34": 1303, "ng:code:optimize_34": 1304, "ng:code:closure_35": 1305, "ng:code:recursion_35": 1306, "ng:code:ast_35": 1307, "ng:code:loop_35": 1308, "ng:code:async_35": 1309, "ng:code:pattern_35": 1310, "ng:code:refactor_35": 1311, "ng:code:optimize_35": 1312, "ng:code:closure_36": 1313, "ng:code:recursion_36": 1314, "ng:code:ast_36": 1315, "ng:code:loop_36": 1316, "ng:code:async_36": 1317, "ng:code:pattern_36": 1318, "ng:code:refactor_36": 1319, "ng:code:optimize_36": 1320, "ng:code:closure_37": 1321, "ng:code:recursion_37": 1322, "ng:code:ast_37": 1323, "ng:code:loop_37": 1324, "ng:code:async_37": 1325, "ng:code:pattern_37": 1326, "ng:code:refactor_37": 1327, "ng:code:optimize_37": 1328, "ng:code:closure_38": 1329, "ng:code:recursion_38": 1330, "ng:code:ast_38": 1331, "ng:code:loop_38": 1332, "ng:code:async_38": 1333, "ng:code:pattern_38": 1334, "ng:code:refactor_38": 1335, "ng:code:optimize_38": 1336, "ng:code:closure_39": 1337, "ng:code:recursion_39": 1338, "ng:code:ast_39": 1339, "ng:code:loop_39": 1340, "ng:code:async_39": 1341, "ng:code:pattern_39": 1342, "ng:code:refactor_39": 1343, "ng:code:optimize_39": 1344, "ng:code:closure_40": 1345, "ng:code:recursion_40": 1346, "ng:code:ast_40": 1347, "ng:code:loop_40": 1348, "ng:code:async_40": 1349, "ng:code:pattern_40": 1350, "ng:code:refactor_40": 1351, "ng:code:optimize_40": 1352, "ng:code:closure_41": 1353, "ng:code:recursion_41": 1354, "ng:code:ast_41": 1355, "ng:code:loop_41": 1356, "ng:code:async_41": 1357, "ng:code:pattern_41": 1358, "ng:code:refactor_41": 1359, "ng:code:optimize_41": 1360, "ng:code:closure_42": 1361, "ng:code:recursion_42": 1362, "ng:code:ast_42": 1363, "ng:code:loop_42": 1364, "ng:code:async_42": 1365, "ng:code:pattern_42": 1366, "ng:code:refactor_42": 1367, "ng:code:optimize_42": 1368, "ng:code:closure_43": 1369, "ng:code:recursion_43": 1370, "ng:code:ast_43": 1371, "ng:code:loop_43": 1372, "ng:code:async_43": 1373, "ng:code:pattern_43": 1374, "ng:code:refactor_43": 1375, "ng:code:optimize_43": 1376, "ng:code:closure_44": 1377, "ng:code:recursion_44": 1378, "ng:code:ast_44": 1379, "ng:code:loop_44": 1380, "ng:code:async_44": 1381, "ng:code:pattern_44": 1382, "ng:code:refactor_44": 1383, "ng:code:optimize_44": 1384, "ng:code:closure_45": 1385, "ng:code:recursion_45": 1386, "ng:code:ast_45": 1387, "ng:code:loop_45": 1388, "ng:code:async_45": 1389, "ng:code:pattern_45": 1390, "ng:code:refactor_45": 1391, "ng:code:optimize_45": 1392, "ng:code:closure_46": 1393, "ng:code:recursion_46": 1394, "ng:code:ast_46": 1395, "ng:code:loop_46": 1396, "ng:code:async_46": 1397, "ng:code:pattern_46": 1398, "ng:code:refactor_46": 1399, "ng:code:optimize_46": 1400, "ng:code:closure_47": 1401, "ng:code:recursion_47": 1402, "ng:code:ast_47": 1403, "ng:code:loop_47": 1404, "ng:code:async_47": 1405, "ng:code:pattern_47": 1406, "ng:code:refactor_47": 1407, "ng:code:optimize_47": 1408, "ng:code:closure_48": 1409, "ng:code:recursion_48": 1410, "ng:code:ast_48": 1411, "ng:code:loop_48": 1412, "ng:code:async_48": 1413, "ng:code:pattern_48": 1414, "ng:code:refactor_48": 1415, "ng:code:optimize_48": 1416, "ng:code:closure_49": 1417, "ng:code:recursion_49": 1418, "ng:code:ast_49": 1419, "ng:code:loop_49": 1420, "ng:code:async_49": 1421, "ng:code:pattern_49": 1422, "ng:code:refactor_49": 1423, "ng:code:optimize_49": 1424, "ng:code:closure_50": 1425, "ng:code:recursion_50": 1426, "ng:code:ast_50": 1427, "ng:code:loop_50": 1428, "ng:code:async_50": 1429, "ng:code:pattern_50": 1430, "ng:code:refactor_50": 1431, "ng:code:optimize_50": 1432, "ng:code:closure_51": 1433, "ng:code:recursion_51": 1434, "ng:code:ast_51": 1435, "ng:code:loop_51": 1436, "ng:code:async_51": 1437, "ng:code:pattern_51": 1438, "ng:code:refactor_51": 1439, "ng:code:optimize_51": 1440, "ng:code:closure_52": 1441, "ng:code:recursion_52": 1442, "ng:code:ast_52": 1443, "ng:code:loop_52": 1444, "ng:code:async_52": 1445, "ng:code:pattern_52": 1446, "ng:code:refactor_52": 1447, "ng:code:optimize_52": 1448, "ng:code:closure_53": 1449, "ng:code:recursion_53": 1450, "ng:code:ast_53": 1451, "ng:code:loop_53": 1452, "ng:code:async_53": 1453, "ng:code:pattern_53": 1454, "ng:code:refactor_53": 1455, "ng:code:optimize_53": 1456, "ng:code:closure_54": 1457, "ng:code:recursion_54": 1458, "ng:code:ast_54": 1459, "ng:code:loop_54": 1460, "ng:code:async_54": 1461, "ng:code:pattern_54": 1462, "ng:code:refactor_54": 1463, "ng:code:optimize_54": 1464, "ng:code:closure_55": 1465, "ng:code:recursion_55": 1466, "ng:code:ast_55": 1467, "ng:code:loop_55": 1468, "ng:code:async_55": 1469, "ng:code:pattern_55": 1470, "ng:code:refactor_55": 1471, "ng:code:optimize_55": 1472, "ng:code:closure_56": 1473, "ng:code:recursion_56": 1474, "ng:code:ast_56": 1475, "ng:code:loop_56": 1476, "ng:code:async_56": 1477, "ng:code:pattern_56": 1478, "ng:code:refactor_56": 1479, "ng:code:optimize_56": 1480, "ng:code:closure_57": 1481, "ng:code:recursion_57": 1482, "ng:code:ast_57": 1483, "ng:code:loop_57": 1484, "ng:code:async_57": 1485, "ng:code:pattern_57": 1486, "ng:code:refactor_57": 1487, "ng:code:optimize_57": 1488, "ng:code:closure_58": 1489, "ng:code:recursion_58": 1490, "ng:code:ast_58": 1491, "ng:code:loop_58": 1492, "ng:code:async_58": 1493, "ng:code:pattern_58": 1494, "ng:code:refactor_58": 1495, "ng:code:optimize_58": 1496, "ng:code:closure_59": 1497, "ng:code:recursion_59": 1498, "ng:code:ast_59": 1499, "ng:code:loop_59": 1500, "ng:code:async_59": 1501, "ng:code:pattern_59": 1502, "ng:code:refactor_59": 1503, "ng:code:optimize_59": 1504, "ng:code:closure_60": 1505, "ng:code:recursion_60": 1506, "ng:code:ast_60": 1507, "ng:code:loop_60": 1508, "ng:code:async_60": 1509, "ng:code:pattern_60": 1510, "ng:code:refactor_60": 1511, "ng:code:optimize_60": 1512, "ng:code:closure_61": 1513, "ng:code:recursion_61": 1514, "ng:code:ast_61": 1515, "ng:code:loop_61": 1516, "ng:code:async_61": 1517, "ng:code:pattern_61": 1518, "ng:code:refactor_61": 1519, "ng:code:optimize_61": 1520, "ng:code:closure_62": 1521, "ng:code:recursion_62": 1522, "ng:code:ast_62": 1523, "ng:code:loop_62": 1524, "ng:code:async_62": 1525, "ng:code:pattern_62": 1526, "ng:code:refactor_62": 1527, "ng:code:optimize_62": 1528, "ng:code:closure_63": 1529, "ng:code:recursion_63": 1530, "ng:code:ast_63": 1531, "ng:code:loop_63": 1532, "ng:code:async_63": 1533, "ng:code:pattern_63": 1534, "ng:code:refactor_63": 1535, "ng:code:optimize_63": 1536, "ng:code:closure_64": 1537, "ng:code:recursion_64": 1538, "ng:code:ast_64": 1539, "ng:code:loop_64": 1540, "ng:code:async_64": 1541, "ng:code:pattern_64": 1542, "ng:code:refactor_64": 1543, "ng:code:optimize_64": 1544, "ng:code:closure_65": 1545, "ng:code:recursion_65": 1546, "ng:code:ast_65": 1547, "ng:code:loop_65": 1548, "ng:code:async_65": 1549, "ng:code:pattern_65": 1550, "ng:code:refactor_65": 1551, "ng:code:optimize_65": 1552, "ng:code:closure_66": 1553, "ng:code:recursion_66": 1554, "ng:code:ast_66": 1555, "ng:code:loop_66": 1556, "ng:code:async_66": 1557, "ng:code:pattern_66": 1558, "ng:code:refactor_66": 1559, "ng:code:optimize_66": 1560, "ng:code:closure_67": 1561, "ng:code:recursion_67": 1562, "ng:code:ast_67": 1563, "ng:code:loop_67": 1564, "ng:code:async_67": 1565, "ng:code:pattern_67": 1566, "ng:code:refactor_67": 1567, "ng:code:optimize_67": 1568, "ng:code:closure_68": 1569, "ng:code:recursion_68": 1570, "ng:code:ast_68": 1571, "ng:code:loop_68": 1572, "ng:code:async_68": 1573, "ng:code:pattern_68": 1574, "ng:code:refactor_68": 1575, "ng:code:optimize_68": 1576, "ng:code:closure_69": 1577, "ng:code:recursion_69": 1578, "ng:code:loop_69": 1579, "ng:code:async_69": 1580, "ng:code:pattern_69": 1581, "ng:code:refactor_69": 1582, "ng:code:optimize_69": 1583, "ng:code:closure_70": 1584, "ng:code:recursion_70": 1585, "ng:code:ast_70": 1586, "ng:code:loop_70": 1587, "ng:code:async_70": 1588, "ng:code:pattern_70": 1589, "ng:code:refactor_70": 1590, "ng:code:optimize_70": 1591, "ng:code:closure_71": 1592, "ng:code:recursion_71": 1593, "ng:code:ast_71": 1594, "ng:code:loop_71": 1595, "ng:code:async_71": 1596, "ng:code:pattern_71": 1597, "ng:code:refactor_71": 1598, "ng:code:optimize_71": 1599, "ng:code:closure_72": 1600, "ng:code:recursion_72": 1601, "ng:code:ast_72": 1602, "ng:code:loop_72": 1603, "ng:code:async_72": 1604, "ng:code:pattern_72": 1605, "ng:code:refactor_72": 1606, "ng:code:optimize_72": 1607, "ng:code:closure_73": 1608, "ng:code:recursion_73": 1609, "ng:code:ast_73": 1610, "ng:code:loop_73": 1611, "ng:code:async_73": 1612, "ng:code:refactor_73": 1613, "ng:code:optimize_73": 1614, "ng:code:closure_74": 1615, "ng:code:recursion_74": 1616, "ng:code:ast_74": 1617, "ng:code:loop_74": 1618, "ng:code:async_74": 1619, "ng:code:pattern_74": 1620, "ng:code:refactor_74": 1621, "ng:code:optimize_74": 1622, "ng:code:closure_75": 1623, "ng:code:recursion_75": 1624, "ng:code:ast_75": 1625, "ng:code:loop_75": 1626, "ng:code:async_75": 1627, "ng:code:pattern_75": 1628, "ng:code:refactor_75": 1629, "ng:code:optimize_75": 1630, "ng:code:closure_76": 1631, "ng:code:recursion_76": 1632, "ng:code:ast_76": 1633, "ng:code:loop_76": 1634, "ng:code:async_76": 1635, "ng:code:pattern_76": 1636, "ng:code:refactor_76": 1637, "ng:code:optimize_76": 1638, "ng:code:closure_77": 1639, "ng:code:recursion_77": 1640, "ng:code:ast_77": 1641, "ng:code:loop_77": 1642, "ng:code:async_77": 1643, "ng:code:pattern_77": 1644, "ng:code:refactor_77": 1645, "ng:code:optimize_77": 1646, "ng:code:closure_78": 1647, "ng:code:recursion_78": 1648, "ng:code:ast_78": 1649, "ng:code:loop_78": 1650, "ng:code:async_78": 1651, "ng:code:pattern_78": 1652, "ng:code:refactor_78": 1653, "ng:code:optimize_78": 1654, "ng:code:closure_79": 1655, "ng:code:recursion_79": 1656, "ng:code:ast_79": 1657, "ng:code:loop_79": 1658, "ng:code:async_79": 1659, "ng:code:pattern_79": 1660, "ng:code:refactor_79": 1661, "ng:code:optimize_79": 1662, "ng:code:closure_80": 1663, "ng:code:recursion_80": 1664, "ng:code:ast_80": 1665, "ng:code:loop_80": 1666, "ng:code:async_80": 1667, "ng:code:pattern_80": 1668, "ng:code:refactor_80": 1669, "ng:code:optimize_80": 1670, "ng:code:closure_81": 1671, "ng:code:recursion_81": 1672, "ng:code:ast_81": 1673, "ng:code:loop_81": 1674, "ng:code:async_81": 1675, "ng:code:pattern_81": 1676, "ng:code:refactor_81": 1677, "ng:code:optimize_81": 1678, "ng:code:closure_82": 1679, "ng:code:recursion_82": 1680, "ng:code:ast_82": 1681, "ng:code:loop_82": 1682, "ng:code:async_82": 1683, "ng:code:pattern_82": 1684, "ng:code:refactor_82": 1685, "ng:code:optimize_82": 1686, "ng:code:closure_83": 1687, "ng:code:recursion_83": 1688, "ng:code:ast_83": 1689, "ng:code:loop_83": 1690, "ng:code:async_83": 1691, "ng:code:pattern_83": 1692, "ng:code:refactor_83": 1693, "ng:code:optimize_83": 1694, "ng:code:closure_84": 1695, "ng:code:recursion_84": 1696, "ng:code:ast_84": 1697, "ng:code:loop_84": 1698, "ng:code:async_84": 1699, "ng:code:pattern_84": 1700, "ng:code:refactor_84": 1701, "ng:code:optimize_84": 1702, "ng:code:closure_85": 1703, "ng:code:recursion_85": 1704, "ng:code:ast_85": 1705, "ng:code:loop_85": 1706, "ng:code:async_85": 1707, "ng:code:pattern_85": 1708, "ng:code:refactor_85": 1709, "ng:code:optimize_85": 1710, "ng:code:closure_86": 1711, "ng:code:recursion_86": 1712, "ng:code:ast_86": 1713, "ng:code:loop_86": 1714, "ng:code:async_86": 1715, "ng:code:pattern_86": 1716, "ng:code:refactor_86": 1717, "ng:code:optimize_86": 1718, "ng:code:closure_87": 1719, "ng:code:recursion_87": 1720, "ng:code:ast_87": 1721, "ng:code:loop_87": 1722, "ng:code:async_87": 1723, "ng:code:pattern_87": 1724, "ng:code:refactor_87": 1725, "ng:code:optimize_87": 1726, "⍑": 1727, "⍱": 1728, "⌒": 1729, "₳": 1730, "ℜ": 1731, "⌞": 1732, "⠃": 1733, "⡕": 1734, "⢮": 1735, "⊙": 1736, "∲": 1737, "⊵": 1738, "⋽": 1739, "⏋": 1740, "⊮": 1741, "‖": 1742, "ℕ": 1743, "⋵": 1744, "⋕": 1745, "≚": 1746, "⎽": 1747, "⌛": 1748, "≓": 1749, "∫": 1750, "ₛ": 1751, "↮": 1752, "⁅": 1753, "⎡": 1754, "↢": 1755, "⋦": 1756, "𝔪": 1757, "𝑍": 1758, "⋀": 1759, "⊈": 1760, "₪": 1761, "≉": 1762, "≠": 1763, "⋬": 1764, "≁": 1765, "⒔": 1766, "⒜": 1767, "⎭": 1769, "⸊": 1770, "⇢": 1771, "↠": 1772, "Ⅳ": 1773, "⊓": 1774, "⊏": 1775, "≣": 1776, "⊊": 1777, "≽": 1778, "⁵": 1779, "⊋": 1780, "₮": 1781, "⊥": 1782, "⋮": 1783, "ꝙ": 1784, "𝙯": 1785, "𝗙": 1786, "ꝿ": 1787, "𝝟": 1788, "ⱻ": 1789, "𝜱": 1790, "Ɫ": 1791, "ng:cognition:attention_1": 1792, "ng:cognition:bias_1": 1793, "ng:cognition:memory_2": 1794, "ng:cognition:metacognition_2": 1795, "ng:cognition:memory_3": 1796, "ng:cognition:attention_3": 1797, "ng:cognition:metacognition_3": 1798, "ng:cognition:qualia_3": 1799, "ng:cognition:attention_4": 1800, "ng:cognition:chunking_4": 1801, "ng:cognition:salience_4": 1802, "ng:cognition:metacognition_4": 1803, "ng:cognition:qualia_4": 1804, "ng:cognition:memory_5": 1805, "ng:cognition:bias_5": 1806, "ng:cognition:metacognition_5": 1807, "ng:cognition:consciousness_5": 1808, "ng:cognition:attention_6": 1809, "ng:cognition:chunking_6": 1810, "ng:cognition:metacognition_6": 1811, "ng:cognition:bias_7": 1812, "ng:cognition:consciousness_7": 1813, "ng:cognition:qualia_7": 1814, "ng:cognition:memory_8": 1815, "ng:cognition:bias_8": 1816, "ng:cognition:chunking_8": 1817, "ng:cognition:chunking_9": 1818, "ng:cognition:memory_10": 1819, "ng:cognition:chunking_10": 1820, "ng:cognition:qualia_10": 1821, "ng:cognition:chunking_11": 1822, "ng:cognition:metacognition_11": 1823, "ng:cognition:memory_12": 1824, "ng:cognition:attention_12": 1825, "ng:cognition:bias_12": 1826, "ng:cognition:attention_13": 1827, "ng:cognition:chunking_13": 1828, "ng:cognition:qualia_13": 1829, "ng:cognition:memory_14": 1830, "ng:cognition:attention_14": 1831, "ng:cognition:chunking_14": 1832, "ng:cognition:salience_14": 1833, "ng:cognition:consciousness_14": 1834, "ng:cognition:memory_15": 1835, "ng:cognition:salience_15": 1836, "ng:cognition:metacognition_15": 1837, "ng:cognition:qualia_15": 1838, "ng:cognition:memory_16": 1839, "ng:cognition:bias_16": 1840, "ng:cognition:chunking_16": 1841, "ng:cognition:salience_16": 1842, "ng:cognition:consciousness_16": 1843, "ng:cognition:memory_17": 1844, "ng:cognition:attention_17": 1845, "ng:cognition:bias_17": 1846, "ng:cognition:chunking_17": 1847, "ng:cognition:metacognition_17": 1848, "ng:cognition:consciousness_17": 1849, "ng:cognition:memory_18": 1850, "ng:cognition:chunking_18": 1851, "ng:cognition:metacognition_18": 1852, "ng:cognition:consciousness_18": 1853, "ng:cognition:memory_19": 1854, "ng:cognition:chunking_19": 1855, "ng:cognition:salience_19": 1856, "ng:cognition:metacognition_19": 1857, "ng:cognition:consciousness_19": 1858, "ng:cognition:chunking_20": 1859, "ng:cognition:metacognition_20": 1860, "ng:cognition:consciousness_20": 1861, "ng:cognition:qualia_20": 1862, "ng:cognition:chunking_21": 1863, "ng:cognition:salience_21": 1864, "ng:cognition:metacognition_21": 1865, "ng:cognition:qualia_21": 1866, "ng:cognition:memory_22": 1867, "ng:cognition:bias_22": 1868, "ng:cognition:salience_22": 1869, "ng:cognition:consciousness_22": 1870, "ng:cognition:memory_24": 1871, "ng:cognition:attention_24": 1872, "ng:cognition:bias_24": 1873, "ng:cognition:chunking_24": 1874, "ng:cognition:salience_24": 1875, "ng:cognition:metacognition_24": 1876, "ng:cognition:consciousness_24": 1877, "ng:cognition:qualia_24": 1878, "ng:cognition:memory_25": 1879, "ng:cognition:attention_25": 1880, "ng:cognition:bias_25": 1881, "ng:cognition:chunking_25": 1882, "ng:cognition:salience_25": 1883, "ng:cognition:metacognition_25": 1884, "ng:cognition:consciousness_25": 1885, "ng:cognition:qualia_25": 1886, "ng:cognition:memory_26": 1887, "ng:cognition:attention_26": 1888, "ng:cognition:bias_26": 1889, "ng:cognition:chunking_26": 1890, "ng:cognition:salience_26": 1891, "ng:cognition:metacognition_26": 1892, "ng:cognition:consciousness_26": 1893, "ng:cognition:qualia_26": 1894, "ng:cognition:memory_27": 1895, "ng:cognition:attention_27": 1896, "ng:cognition:bias_27": 1897, "ng:cognition:chunking_27": 1898, "ng:cognition:salience_27": 1899, "ng:cognition:metacognition_27": 1900, "ng:cognition:consciousness_27": 1901, "ng:cognition:qualia_27": 1902, "ng:cognition:memory_28": 1903, "ng:cognition:attention_28": 1904, "ng:cognition:bias_28": 1905, "ng:cognition:chunking_28": 1906, "ng:cognition:salience_28": 1907, "ng:cognition:metacognition_28": 1908, "ng:cognition:consciousness_28": 1909, "ng:cognition:qualia_28": 1910, "ng:cognition:memory_29": 1911, "ng:cognition:attention_29": 1912, "ng:cognition:bias_29": 1913, "ng:cognition:chunking_29": 1914, "ng:cognition:salience_29": 1915, "ng:cognition:metacognition_29": 1916, "ng:cognition:consciousness_29": 1917, "ng:cognition:qualia_29": 1918, "ng:cognition:memory_30": 1919, "ng:cognition:attention_30": 1920, "ng:cognition:bias_30": 1921, "ng:cognition:chunking_30": 1922, "ng:cognition:salience_30": 1923, "ng:cognition:metacognition_30": 1924, "ng:cognition:consciousness_30": 1925, "ng:cognition:qualia_30": 1926, "ng:cognition:memory_31": 1927, "ng:cognition:attention_31": 1928, "ng:cognition:bias_31": 1929, "ng:cognition:chunking_31": 1930, "ng:cognition:salience_31": 1931, "ng:cognition:metacognition_31": 1932, "ng:cognition:consciousness_31": 1933, "ng:cognition:qualia_31": 1934, "ng:cognition:memory_32": 1935, "ng:cognition:attention_32": 1936, "ng:cognition:bias_32": 1937, "ng:cognition:chunking_32": 1938, "ng:cognition:salience_32": 1939, "ng:cognition:metacognition_32": 1940, "ng:cognition:consciousness_32": 1941, "ng:cognition:qualia_32": 1942, "ng:cognition:memory_33": 1943, "ng:cognition:attention_33": 1944, "ng:cognition:bias_33": 1945, "ng:cognition:chunking_33": 1946, "ng:cognition:salience_33": 1947, "ng:cognition:metacognition_33": 1948, "ng:cognition:consciousness_33": 1949, "ng:cognition:qualia_33": 1950, "ng:cognition:memory_34": 1951, "ng:cognition:attention_34": 1952, "ng:cognition:bias_34": 1953, "ng:cognition:chunking_34": 1954, "ng:cognition:salience_34": 1955, "ng:cognition:metacognition_34": 1956, "ng:cognition:consciousness_34": 1957, "ng:cognition:qualia_34": 1958, "ng:cognition:memory_35": 1959, "ng:cognition:attention_35": 1960, "ng:cognition:bias_35": 1961, "ng:cognition:chunking_35": 1962, "ng:cognition:salience_35": 1963, "ng:cognition:metacognition_35": 1964, "ng:cognition:consciousness_35": 1965, "ng:cognition:qualia_35": 1966, "ng:cognition:memory_36": 1967, "ng:cognition:attention_36": 1968, "ng:cognition:bias_36": 1969, "ng:cognition:chunking_36": 1970, "ng:cognition:salience_36": 1971, "ng:cognition:metacognition_36": 1972, "ng:cognition:consciousness_36": 1973, "ng:cognition:qualia_36": 1974, "ng:cognition:memory_37": 1975, "ng:cognition:attention_37": 1976, "ng:cognition:bias_37": 1977, "ng:cognition:chunking_37": 1978, "ng:cognition:salience_37": 1979, "ng:cognition:metacognition_37": 1980, "ng:cognition:consciousness_37": 1981, "ng:cognition:qualia_37": 1982, "ng:cognition:memory_38": 1983, "ng:cognition:attention_38": 1984, "ng:cognition:bias_38": 1985, "ng:cognition:chunking_38": 1986, "ng:cognition:salience_38": 1987, "ng:cognition:metacognition_38": 1988, "ng:cognition:consciousness_38": 1989, "ng:cognition:qualia_38": 1990, "ng:cognition:memory_39": 1991, "ng:cognition:attention_39": 1992, "ng:cognition:bias_39": 1993, "ng:cognition:chunking_39": 1994, "ng:cognition:salience_39": 1995, "ng:cognition:metacognition_39": 1996, "ng:cognition:consciousness_39": 1997, "ng:cognition:qualia_39": 1998, "ng:cognition:memory_40": 1999, "ng:cognition:attention_40": 2000, "ng:cognition:bias_40": 2001, "ng:cognition:chunking_40": 2002, "ng:cognition:salience_40": 2003, "ng:cognition:metacognition_40": 2004, "ng:cognition:consciousness_40": 2005, "ng:cognition:qualia_40": 2006, "ng:cognition:memory_41": 2007, "ng:cognition:attention_41": 2008, "ng:cognition:bias_41": 2009, "ng:cognition:chunking_41": 2010, "ng:cognition:salience_41": 2011, "ng:cognition:metacognition_41": 2012, "ng:cognition:consciousness_41": 2013, "ng:cognition:qualia_41": 2014, "ng:cognition:memory_42": 2015, "ng:cognition:attention_42": 2016, "ng:cognition:bias_42": 2017, "ng:cognition:chunking_42": 2018, "ng:cognition:salience_42": 2019, "ng:cognition:metacognition_42": 2020, "ng:cognition:consciousness_42": 2021, "ng:cognition:qualia_42": 2022, "ng:cognition:memory_43": 2023, "ng:cognition:attention_43": 2024, "ng:cognition:bias_43": 2025, "ng:cognition:chunking_43": 2026, "ng:cognition:salience_43": 2027, "ng:cognition:metacognition_43": 2028, "ng:cognition:consciousness_43": 2029, "ng:cognition:qualia_43": 2030, "ng:cognition:memory_44": 2031, "ng:cognition:attention_44": 2032, "ng:cognition:bias_44": 2033, "ng:cognition:chunking_44": 2034, "ng:cognition:salience_44": 2035, "ng:cognition:metacognition_44": 2036, "ng:cognition:consciousness_44": 2037, "ng:cognition:qualia_44": 2038, "ng:cognition:memory_45": 2039, "ng:cognition:attention_45": 2040, "ng:cognition:bias_45": 2041, "ng:cognition:chunking_45": 2042, "ng:cognition:salience_45": 2043, "ng:cognition:metacognition_45": 2044, "ng:cognition:consciousness_45": 2045, "ng:cognition:qualia_45": 2046, "ng:cognition:memory_46": 2047, "ng:cognition:attention_46": 2048, "ng:cognition:bias_46": 2049, "ng:cognition:chunking_46": 2050, "ng:cognition:salience_46": 2051, "ng:cognition:metacognition_46": 2052, "ng:cognition:consciousness_46": 2053, "ng:cognition:qualia_46": 2054, "ng:cognition:memory_47": 2055, "ng:cognition:attention_47": 2056, "ng:cognition:bias_47": 2057, "ng:cognition:chunking_47": 2058, "ng:cognition:salience_47": 2059, "ng:cognition:metacognition_47": 2060, "ng:cognition:consciousness_47": 2061, "ng:cognition:qualia_47": 2062, "ng:cognition:memory_48": 2063, "ng:cognition:attention_48": 2064, "ng:cognition:bias_48": 2065, "ng:cognition:chunking_48": 2066, "ng:cognition:salience_48": 2067, "ng:cognition:metacognition_48": 2068, "ng:cognition:consciousness_48": 2069, "ng:cognition:qualia_48": 2070, "ng:cognition:memory_49": 2071, "ng:cognition:attention_49": 2072, "ng:cognition:bias_49": 2073, "ng:cognition:chunking_49": 2074, "ng:cognition:salience_49": 2075, "ng:cognition:metacognition_49": 2076, "ng:cognition:consciousness_49": 2077, "ng:cognition:qualia_49": 2078, "ng:cognition:memory_50": 2079, "ng:cognition:attention_50": 2080, "ng:cognition:bias_50": 2081, "ng:cognition:chunking_50": 2082, "ng:cognition:salience_50": 2083, "ng:cognition:metacognition_50": 2084, "ng:cognition:consciousness_50": 2085, "ng:cognition:qualia_50": 2086, "ng:cognition:memory_51": 2087, "ng:cognition:attention_51": 2088, "ng:cognition:bias_51": 2089, "ng:cognition:chunking_51": 2090, "ng:cognition:salience_51": 2091, "ng:cognition:metacognition_51": 2092, "ng:cognition:consciousness_51": 2093, "ng:cognition:qualia_51": 2094, "ng:cognition:memory_52": 2095, "ng:cognition:attention_52": 2096, "ng:cognition:bias_52": 2097, "ng:cognition:chunking_52": 2098, "ng:cognition:salience_52": 2099, "ng:cognition:metacognition_52": 2100, "ng:cognition:consciousness_52": 2101, "ng:cognition:qualia_52": 2102, "ng:cognition:memory_53": 2103, "ng:cognition:attention_53": 2104, "ng:cognition:bias_53": 2105, "ng:cognition:chunking_53": 2106, "ng:cognition:salience_53": 2107, "ng:cognition:metacognition_53": 2108, "ng:cognition:consciousness_53": 2109, "ng:cognition:qualia_53": 2110, "ng:cognition:memory_54": 2111, "ng:cognition:attention_54": 2112, "ng:cognition:bias_54": 2113, "ng:cognition:chunking_54": 2114, "ng:cognition:salience_54": 2115, "ng:cognition:metacognition_54": 2116, "ng:cognition:consciousness_54": 2117, "ng:cognition:qualia_54": 2118, "ng:cognition:memory_55": 2119, "ng:cognition:attention_55": 2120, "ng:cognition:bias_55": 2121, "ng:cognition:chunking_55": 2122, "ng:cognition:salience_55": 2123, "ng:cognition:metacognition_55": 2124, "ng:cognition:consciousness_55": 2125, "ng:cognition:qualia_55": 2126, "ng:cognition:memory_56": 2127, "ng:cognition:attention_56": 2128, "ng:cognition:bias_56": 2129, "ng:cognition:chunking_56": 2130, "ng:cognition:salience_56": 2131, "ng:cognition:metacognition_56": 2132, "ng:cognition:consciousness_56": 2133, "ng:cognition:qualia_56": 2134, "ng:cognition:memory_57": 2135, "ng:cognition:attention_57": 2136, "ng:cognition:bias_57": 2137, "ng:cognition:chunking_57": 2138, "ng:cognition:salience_57": 2139, "ng:cognition:metacognition_57": 2140, "ng:cognition:consciousness_57": 2141, "ng:cognition:qualia_57": 2142, "ng:cognition:memory_58": 2143, "ng:cognition:attention_58": 2144, "ng:cognition:bias_58": 2145, "ng:cognition:chunking_58": 2146, "ng:cognition:salience_58": 2147, "ng:cognition:metacognition_58": 2148, "ng:cognition:consciousness_58": 2149, "ng:cognition:qualia_58": 2150, "ng:cognition:memory_59": 2151, "ng:cognition:attention_59": 2152, "ng:cognition:bias_59": 2153, "ng:cognition:chunking_59": 2154, "ng:cognition:salience_59": 2155, "ng:cognition:metacognition_59": 2156, "ng:cognition:consciousness_59": 2157, "ng:cognition:attention_60": 2158, "ng:cognition:bias_60": 2159, "ng:cognition:chunking_60": 2160, "ng:cognition:salience_60": 2161, "ng:cognition:metacognition_60": 2162, "ng:cognition:consciousness_60": 2163, "ng:cognition:qualia_60": 2164, "ng:cognition:memory_61": 2165, "ng:cognition:attention_61": 2166, "ng:cognition:bias_61": 2167, "ng:cognition:chunking_61": 2168, "ng:cognition:salience_61": 2169, "ng:cognition:metacognition_61": 2170, "ng:cognition:consciousness_61": 2171, "ng:cognition:qualia_61": 2172, "ng:cognition:memory_62": 2173, "ng:cognition:attention_62": 2174, "ng:cognition:bias_62": 2175, "ng:cognition:chunking_62": 2176, "ng:cognition:salience_62": 2177, "ng:cognition:metacognition_62": 2178, "ng:cognition:consciousness_62": 2179, "ng:cognition:qualia_62": 2180, "ng:cognition:memory_63": 2181, "ng:cognition:attention_63": 2182, "ng:cognition:bias_63": 2183, "ng:cognition:chunking_63": 2184, "ng:cognition:salience_63": 2185, "ng:cognition:metacognition_63": 2186, "ng:cognition:consciousness_63": 2187, "ng:cognition:qualia_63": 2188, "ng:cognition:memory_64": 2189, "ng:cognition:attention_64": 2190, "ng:cognition:bias_64": 2191, "ng:cognition:chunking_64": 2192, "ng:cognition:salience_64": 2193, "ng:cognition:metacognition_64": 2194, "ng:cognition:consciousness_64": 2195, "ng:cognition:qualia_64": 2196, "ng:cognition:memory_65": 2197, "ng:cognition:attention_65": 2198, "ng:cognition:bias_65": 2199, "ng:cognition:chunking_65": 2200, "ng:cognition:salience_65": 2201, "ng:cognition:metacognition_65": 2202, "ng:cognition:consciousness_65": 2203, "ng:cognition:qualia_65": 2204, "ng:cognition:memory_66": 2205, "ng:cognition:attention_66": 2206, "ng:cognition:bias_66": 2207, "ng:cognition:chunking_66": 2208, "ng:cognition:salience_66": 2209, "ng:cognition:metacognition_66": 2210, "ng:cognition:consciousness_66": 2211, "ng:cognition:qualia_66": 2212, "ng:cognition:memory_67": 2213, "ng:cognition:attention_67": 2214, "ng:cognition:bias_67": 2215, "ng:cognition:chunking_67": 2216, "ng:cognition:salience_67": 2217, "ng:cognition:metacognition_67": 2218, "ng:cognition:consciousness_67": 2219, "ng:cognition:qualia_67": 2220, "ng:cognition:memory_68": 2221, "ng:cognition:attention_68": 2222, "ng:cognition:bias_68": 2223, "ng:cognition:chunking_68": 2224, "ng:cognition:salience_68": 2225, "ng:cognition:metacognition_68": 2226, "ng:cognition:consciousness_68": 2227, "ng:cognition:qualia_68": 2228, "ng:cognition:memory_69": 2229, "ng:cognition:attention_69": 2230, "ng:cognition:bias_69": 2231, "ng:cognition:chunking_69": 2232, "ng:cognition:salience_69": 2233, "ng:cognition:metacognition_69": 2234, "ng:cognition:consciousness_69": 2235, "ng:cognition:qualia_69": 2236, "ng:cognition:memory_70": 2237, "ng:cognition:attention_70": 2238, "ng:cognition:bias_70": 2239, "ng:cognition:chunking_70": 2240, "ng:cognition:salience_70": 2241, "ng:cognition:metacognition_70": 2242, "ng:cognition:consciousness_70": 2243, "ng:cognition:qualia_70": 2244, "ng:cognition:memory_71": 2245, "ng:cognition:attention_71": 2246, "ng:cognition:bias_71": 2247, "ng:cognition:chunking_71": 2248, "ng:cognition:salience_71": 2249, "ng:cognition:metacognition_71": 2250, "ng:cognition:consciousness_71": 2251, "ng:cognition:qualia_71": 2252, "ng:cognition:memory_72": 2253, "ng:cognition:attention_72": 2254, "ng:cognition:bias_72": 2255, "ng:cognition:chunking_72": 2256, "ng:cognition:salience_72": 2257, "ng:cognition:metacognition_72": 2258, "ng:cognition:consciousness_72": 2259, "ng:cognition:qualia_72": 2260, "ng:cognition:memory_73": 2261, "ng:cognition:attention_73": 2262, "ng:cognition:bias_73": 2263, "ng:cognition:chunking_73": 2264, "ng:cognition:salience_73": 2265, "ng:cognition:metacognition_73": 2266, "ng:cognition:consciousness_73": 2267, "ng:cognition:qualia_73": 2268, "ng:cognition:memory_74": 2269, "ng:cognition:attention_74": 2270, "ng:cognition:bias_74": 2271, "ng:cognition:chunking_74": 2272, "ng:cognition:salience_74": 2273, "ng:cognition:metacognition_74": 2274, "ng:cognition:consciousness_74": 2275, "ng:cognition:qualia_74": 2276, "ng:cognition:memory_75": 2277, "ng:cognition:attention_75": 2278, "ng:cognition:bias_75": 2279, "ng:cognition:chunking_75": 2280, "ng:cognition:salience_75": 2281, "ng:cognition:metacognition_75": 2282, "ng:cognition:consciousness_75": 2283, "ng:cognition:qualia_75": 2284, "ng:cognition:memory_76": 2285, "ng:cognition:attention_76": 2286, "ng:cognition:bias_76": 2287, "ng:cognition:chunking_76": 2288, "ng:cognition:salience_76": 2289, "ng:cognition:metacognition_76": 2290, "ng:cognition:consciousness_76": 2291, "ng:cognition:qualia_76": 2292, "ng:cognition:memory_77": 2293, "ng:cognition:attention_77": 2294, "ng:cognition:bias_77": 2295, "ng:cognition:chunking_77": 2296, "ng:cognition:salience_77": 2297, "ng:cognition:metacognition_77": 2298, "ng:cognition:consciousness_77": 2299, "ng:cognition:qualia_77": 2300, "ng:cognition:memory_78": 2301, "ng:cognition:attention_78": 2302, "ng:cognition:bias_78": 2303, "ng:cognition:chunking_78": 2304, "ng:cognition:salience_78": 2305, "ng:cognition:metacognition_78": 2306, "ng:cognition:consciousness_78": 2307, "ng:cognition:qualia_78": 2308, "ng:cognition:memory_79": 2309, "ng:cognition:attention_79": 2310, "ng:cognition:bias_79": 2311, "ng:cognition:chunking_79": 2312, "ng:cognition:salience_79": 2313, "ng:cognition:metacognition_79": 2314, "ng:cognition:consciousness_79": 2315, "ng:cognition:qualia_79": 2316, "ng:cognition:memory_80": 2317, "ng:cognition:attention_80": 2318, "ng:cognition:bias_80": 2319, "ng:cognition:chunking_80": 2320, "ng:cognition:salience_80": 2321, "ng:cognition:metacognition_80": 2322, "ng:cognition:consciousness_80": 2323, "ng:cognition:qualia_80": 2324, "ng:cognition:memory_81": 2325, "ng:cognition:attention_81": 2326, "ng:cognition:bias_81": 2327, "ng:cognition:chunking_81": 2328, "ng:cognition:salience_81": 2329, "ng:cognition:metacognition_81": 2330, "ng:cognition:consciousness_81": 2331, "ng:cognition:qualia_81": 2332, "ng:cognition:memory_82": 2333, "ng:cognition:attention_82": 2334, "ng:cognition:bias_82": 2335, "ng:cognition:chunking_82": 2336, "ng:cognition:salience_82": 2337, "ng:cognition:metacognition_82": 2338, "ng:cognition:consciousness_82": 2339, "ng:cognition:qualia_82": 2340, "ng:cognition:memory_83": 2341, "ng:cognition:attention_83": 2342, "ng:cognition:bias_83": 2343, "ng:cognition:chunking_83": 2344, "ng:cognition:salience_83": 2345, "ng:cognition:metacognition_83": 2346, "ng:cognition:consciousness_83": 2347, "ng:cognition:qualia_83": 2348, "ng:cognition:memory_84": 2349, "ng:cognition:attention_84": 2350, "ng:cognition:bias_84": 2351, "ng:cognition:chunking_84": 2352, "ng:cognition:salience_84": 2353, "ng:cognition:metacognition_84": 2354, "ng:cognition:consciousness_84": 2355, "ng:cognition:qualia_84": 2356, "ng:cognition:memory_85": 2357, "ng:cognition:attention_85": 2358, "ng:cognition:bias_85": 2359, "ng:cognition:chunking_85": 2360, "ng:cognition:salience_85": 2361, "ng:cognition:metacognition_85": 2362, "ng:cognition:consciousness_85": 2363, "ng:cognition:qualia_85": 2364, "ng:cognition:memory_86": 2365, "ng:cognition:attention_86": 2366, "ng:cognition:bias_86": 2367, "ng:cognition:chunking_86": 2368, "ng:cognition:salience_86": 2369, "ng:cognition:metacognition_86": 2370, "ng:cognition:consciousness_86": 2371, "ng:cognition:qualia_86": 2372, "ng:cognition:memory_87": 2373, "ng:cognition:attention_87": 2374, "ng:cognition:bias_87": 2375, "ng:cognition:chunking_87": 2376, "ng:cognition:salience_87": 2377, "ng:cognition:metacognition_87": 2378, "ng:cognition:consciousness_87": 2379, "ng:cognition:qualia_87": 2380, "𝑶": 2381, "𝟜": 2382, "╜": 2383, "⮽": 2384, "⯁": 2385, "⯀": 2386, "⮿": 2387, "⮾": 2388, "⪼": 2389, "➕": 2390, "⎼": 2391, "⩧": 2392, "⮲": 2393, "⪽": 2394, "⪿": 2395, "⫀": 2396, "➼": 2397, "⎿": 2398, "⏀": 2399, "⏁": 2400, "⫁": 2401, "⧨": 2402, "➜": 2403, "⎾": 2404, "➫": 2405, "➾": 2406, "⏂": 2407, "⏄": 2408, "⫂": 2409, "⫃": 2410, "ℿ": 2411, "↞": 2412, "ℛ": 2413, "⇂": 2414, "⇸": 2415, "⯥": 2416, "⎊": 2417, "⎄": 2418, "⎃": 2419, "⪆": 2420, "⎈": 2421, "⭾": 2422, "⪉": 2423, "⪅": 2424, "⫝": 2425, "⊄": 2426, "⫨": 2427, "⊂": 2428, "⧾": 2429, "⯺": 2430, "⥾": 2431, "⍿": 2432, "⯎": 2433, "❾": 2434, "➃": 2435, "⪃": 2436, "⎅": 2437, "➄": 2438, "⩨": 2439, "⮄": 2440, "❿": 2441, "⩾": 2442, "⎉": 2443, "➅": 2444, "➆": 2445, "➇": 2446, "➈": 2447, "➉": 2448, "➊": 2449, "⪇": 2450, "⪈": 2451, "⪊": 2452, "⫯": 2453, "⭷": 2454, "⮅": 2455, "⮆": 2456, "⮇": 2457, "⮡": 2458, "➀": 2459, "⎂": 2460, "➂": 2461, "⮁": 2462, "⮂": 2463, "➁": 2464, "✈": 2465, "⨿": 2466, "⌶": 2467, "◄": 2468, "◀": 2469, "⬥": 2470, "∙": 2471, "⦿": 2472, "◌": 2473, "⌄": 2474, "≙": 2475, "⌂": 2476, "⨛": 2477, "◘": 2478, "⦅": 2479, "⬅": 2480, "⩒": 2481, "◡": 2482, "✎": 2483, "⨁": 2484, "⌆": 2485, "⨧": 2486, "⦰": 2487, "⌐": 2488, "⌉": 2489, "❍": 2490, "✰": 2491, "∍": 2492, "⬋": 2493, "✨": 2494, "∢": 2495, "⊃": 2496, "⌎": 2497, "⨇": 2498, "⦽": 2499, "⌃": 2500, "◦": 2501, "❀": 2502, "✅": 2503, "▱": 2504, "⦇": 2505, "⨡": 2506, "⤁": 2507, "⩉": 2508, "⦳": 2509, "◕": 2510, "◭": 2511, "⬎": 2512, "⤅": 2513, "◛": 2514, "◳": 2515, "⥖": 2516, "⦕": 2517, "⥈": 2518, "◔": 2519, "⤼": 2520, "◪": 2521, "◃": 2522, "⤮": 2523, "⨱": 2524, "⥃": 2525, "⬼": 2526, "⋳": 2527, "⊉": 2528, "🝛": 2529, "🞷": 2530, "🞆": 2531, "🞉": 2532, "🟊": 2533, "🟉": 2534, "🞯": 2535, "🞵": 2536, "🟋": 2537, "🞶": 2538, "🟍": 2539, "🟂": 2540, "🞈": 2541, "🝚": 2542, "🝜": 2543, "🝝": 2544, "🞊": 2545, "🟀": 2546, "🟁": 2547, "🟇": 2548, "🟏": 2549, "🟐": 2550, "🟑": 2551, "🟓": 2552, "₡": 2553, "℃": 2554, "‗": 2555, "⏸": 2556, "⋞": 2557, "↚": 2559, "₤": 2560, "⁎": 2561, "⋯": 2562, "≪": 2563, "⊎": 2564, "⋃": 2565, "≱": 2566, "⊅": 2567, "≯": 2568, "⋢": 2569, "⑵": 2570, "⒃": 2571, "⅌": 2572, "⊰": 2573, "⁏": 2574, "∽": 2575, "Ⅻ": 2576, "⊒": 2577, "⋥": 2578, "⋆": 2579, "≿": 2580, "∃": 2581, "ⅎ": 2582, "℣": 2583, "ng:distributed:replication": 2584, "ng:distributed:consistency": 2585, "ng:distributed:availability": 2586, "ng:distributed:replication_1": 2587, "ng:distributed:gossip_1": 2588, "ng:distributed:consensus_2": 2589, "ng:distributed:replication_2": 2590, "ng:distributed:consistency_2": 2591, "ng:distributed:partition_3": 2592, "ng:distributed:coordination_3": 2593, "ng:distributed:consensus_4": 2594, "ng:distributed:replication_4": 2595, "ng:distributed:consistency_4": 2596, "ng:distributed:availability_4": 2597, "ng:distributed:gossip_4": 2598, "ng:distributed:raft_4": 2599, "ng:distributed:consensus_5": 2600, "ng:distributed:replication_5": 2601, "ng:distributed:consistency_5": 2602, "ng:distributed:availability_5": 2603, "ng:distributed:gossip_5": 2604, "ng:distributed:consensus_6": 2605, "ng:distributed:gossip_6": 2606, "ng:distributed:partition_7": 2607, "ng:distributed:partition_8": 2608, "ng:distributed:consistency_8": 2609, "ng:distributed:raft_8": 2610, "ng:distributed:consensus_9": 2611, "ng:distributed:replication_9": 2612, "ng:distributed:consistency_9": 2613, "ng:distributed:raft_9": 2614, "ng:distributed:partition_10": 2615, "ng:distributed:replication_10": 2616, "ng:distributed:consensus_11": 2617, "ng:distributed:replication_11": 2618, "ng:distributed:consistency_11": 2619, "ng:distributed:availability_11": 2620, "ng:distributed:coordination_11": 2621, "ng:distributed:partition_12": 2622, "ng:distributed:consistency_12": 2623, "ng:distributed:coordination_12": 2624, "ng:distributed:raft_12": 2625, "ng:distributed:partition_13": 2626, "ng:distributed:replication_13": 2627, "ng:distributed:consistency_13": 2628, "ng:distributed:availability_13": 2629, "ng:distributed:coordination_13": 2630, "ng:distributed:gossip_13": 2631, "ng:distributed:partition_14": 2632, "ng:distributed:replication_14": 2633, "ng:distributed:consistency_14": 2634, "ng:distributed:availability_14": 2635, "ng:distributed:coordination_14": 2636, "ng:distributed:gossip_14": 2637, "ng:distributed:consensus_15": 2638, "ng:distributed:partition_15": 2639, "ng:distributed:replication_15": 2640, "ng:distributed:consistency_15": 2641, "ng:distributed:availability_15": 2642, "ng:distributed:raft_15": 2643, "ng:distributed:replication_16": 2644, "ng:distributed:consistency_16": 2645, "ng:distributed:coordination_16": 2646, "ng:distributed:raft_16": 2647, "ng:distributed:consensus_17": 2648, "ng:distributed:replication_17": 2649, "ng:distributed:consistency_17": 2650, "ng:distributed:availability_17": 2651, "ng:distributed:coordination_17": 2652, "ng:distributed:gossip_17": 2653, "ng:distributed:raft_17": 2654, "ng:distributed:replication_18": 2655, "ng:distributed:gossip_18": 2656, "ng:distributed:raft_18": 2657, "ng:distributed:replication_19": 2658, "ng:distributed:availability_19": 2659, "ng:distributed:coordination_19": 2660, "ng:distributed:gossip_19": 2661, "ng:distributed:consensus_20": 2662, "ng:distributed:partition_20": 2663, "ng:distributed:coordination_20": 2664, "ng:distributed:raft_20": 2665, "ng:distributed:consensus_21": 2666, "ng:distributed:replication_21": 2667, "ng:distributed:consistency_21": 2668, "ng:distributed:availability_21": 2669, "ng:distributed:gossip_21": 2670, "ng:distributed:consensus_24": 2671, "ng:distributed:partition_24": 2672, "ng:distributed:replication_24": 2673, "ng:distributed:consistency_24": 2674, "ng:distributed:availability_24": 2675, "ng:distributed:coordination_24": 2676, "ng:distributed:gossip_24": 2677, "ng:distributed:raft_24": 2678, "ng:distributed:consensus_25": 2679, "ng:distributed:partition_25": 2680, "ng:distributed:replication_25": 2681, "ng:distributed:consistency_25": 2682, "ng:distributed:availability_25": 2683, "ng:distributed:coordination_25": 2684, "ng:distributed:gossip_25": 2685, "ng:distributed:raft_25": 2686, "ng:distributed:consensus_26": 2687, "ng:distributed:partition_26": 2688, "ng:distributed:replication_26": 2689, "ng:distributed:consistency_26": 2690, "ng:distributed:availability_26": 2691, "ng:distributed:coordination_26": 2692, "ng:distributed:gossip_26": 2693, "ng:distributed:raft_26": 2694, "ng:distributed:consensus_27": 2695, "ng:distributed:partition_27": 2696, "ng:distributed:replication_27": 2697, "ng:distributed:consistency_27": 2698, "ng:distributed:availability_27": 2699, "ng:distributed:coordination_27": 2700, "ng:distributed:gossip_27": 2701, "ng:distributed:raft_27": 2702, "ng:distributed:consensus_28": 2703, "ng:distributed:partition_28": 2704, "ng:distributed:replication_28": 2705, "ng:distributed:consistency_28": 2706, "ng:distributed:availability_28": 2707, "ng:distributed:coordination_28": 2708, "ng:distributed:gossip_28": 2709, "ng:distributed:raft_28": 2710, "ng:distributed:consensus_29": 2711, "ng:distributed:partition_29": 2712, "ng:distributed:replication_29": 2713, "ng:distributed:consistency_29": 2714, "ng:distributed:availability_29": 2715, "ng:distributed:coordination_29": 2716, "ng:distributed:gossip_29": 2717, "ng:distributed:raft_29": 2718, "ng:distributed:consensus_30": 2719, "ng:distributed:partition_30": 2720, "ng:distributed:replication_30": 2721, "ng:distributed:consistency_30": 2722, "ng:distributed:availability_30": 2723, "ng:distributed:coordination_30": 2724, "ng:distributed:gossip_30": 2725, "ng:distributed:raft_30": 2726, "ng:distributed:consensus_31": 2727, "ng:distributed:partition_31": 2728, "ng:distributed:replication_31": 2729, "ng:distributed:availability_31": 2730, "ng:distributed:coordination_31": 2731, "ng:distributed:gossip_31": 2732, "ng:distributed:raft_31": 2733, "ng:distributed:consensus_32": 2734, "ng:distributed:partition_32": 2735, "ng:distributed:replication_32": 2736, "ng:distributed:consistency_32": 2737, "ng:distributed:availability_32": 2738, "ng:distributed:coordination_32": 2739, "ng:distributed:gossip_32": 2740, "ng:distributed:raft_32": 2741, "ng:distributed:consensus_33": 2742, "ng:distributed:partition_33": 2743, "ng:distributed:replication_33": 2744, "ng:distributed:consistency_33": 2745, "ng:distributed:availability_33": 2746, "ng:distributed:coordination_33": 2747, "ng:distributed:gossip_33": 2748, "ng:distributed:raft_33": 2749, "ng:distributed:consensus_34": 2750, "ng:distributed:partition_34": 2751, "ng:distributed:replication_34": 2752, "ng:distributed:consistency_34": 2753, "ng:distributed:availability_34": 2754, "ng:distributed:coordination_34": 2755, "ng:distributed:gossip_34": 2756, "ng:distributed:raft_34": 2757, "ng:distributed:consensus_35": 2758, "ng:distributed:partition_35": 2759, "ng:distributed:replication_35": 2760, "ng:distributed:consistency_35": 2761, "ng:distributed:availability_35": 2762, "ng:distributed:coordination_35": 2763, "ng:distributed:gossip_35": 2764, "ng:distributed:raft_35": 2765, "ng:distributed:consensus_36": 2766, "ng:distributed:partition_36": 2767, "ng:distributed:replication_36": 2768, "ng:distributed:consistency_36": 2769, "ng:distributed:availability_36": 2770, "ng:distributed:coordination_36": 2771, "ng:distributed:gossip_36": 2772, "ng:distributed:raft_36": 2773, "ng:distributed:consensus_37": 2774, "ng:distributed:partition_37": 2775, "ng:distributed:replication_37": 2776, "ng:distributed:availability_37": 2777, "ng:distributed:coordination_37": 2778, "ng:distributed:gossip_37": 2779, "ng:distributed:raft_37": 2780, "ng:distributed:consensus_38": 2781, "ng:distributed:partition_38": 2782, "ng:distributed:replication_38": 2783, "ng:distributed:consistency_38": 2784, "ng:distributed:availability_38": 2785, "ng:distributed:coordination_38": 2786, "ng:distributed:gossip_38": 2787, "ng:distributed:raft_38": 2788, "ng:distributed:consensus_39": 2789, "ng:distributed:partition_39": 2790, "ng:distributed:replication_39": 2791, "ng:distributed:consistency_39": 2792, "ng:distributed:availability_39": 2793, "ng:distributed:coordination_39": 2794, "ng:distributed:gossip_39": 2795, "ng:distributed:raft_39": 2796, "ng:distributed:consensus_40": 2797, "ng:distributed:partition_40": 2798, "ng:distributed:replication_40": 2799, "ng:distributed:consistency_40": 2800, "ng:distributed:availability_40": 2801, "ng:distributed:coordination_40": 2802, "ng:distributed:gossip_40": 2803, "ng:distributed:raft_40": 2804, "ng:distributed:consensus_41": 2805, "ng:distributed:partition_41": 2806, "ng:distributed:replication_41": 2807, "ng:distributed:consistency_41": 2808, "ng:distributed:availability_41": 2809, "ng:distributed:coordination_41": 2810, "ng:distributed:gossip_41": 2811, "ng:distributed:consensus_42": 2812, "ng:distributed:partition_42": 2813, "ng:distributed:replication_42": 2814, "ng:distributed:consistency_42": 2815, "ng:distributed:coordination_42": 2816, "ng:distributed:gossip_42": 2817, "ng:distributed:raft_42": 2818, "ng:distributed:consensus_43": 2819, "ng:distributed:partition_43": 2820, "ng:distributed:replication_43": 2821, "ng:distributed:consistency_43": 2822, "ng:distributed:availability_43": 2823, "ng:distributed:coordination_43": 2824, "ng:distributed:gossip_43": 2825, "ng:distributed:raft_43": 2826, "ng:distributed:consensus_44": 2827, "ng:distributed:partition_44": 2828, "ng:distributed:replication_44": 2829, "ng:distributed:consistency_44": 2830, "ng:distributed:availability_44": 2831, "ng:distributed:coordination_44": 2832, "ng:distributed:gossip_44": 2833, "ng:distributed:raft_44": 2834, "ng:distributed:consensus_45": 2835, "ng:distributed:partition_45": 2836, "ng:distributed:replication_45": 2837, "ng:distributed:consistency_45": 2838, "ng:distributed:availability_45": 2839, "ng:distributed:coordination_45": 2840, "ng:distributed:raft_45": 2841, "ng:distributed:consensus_46": 2842, "ng:distributed:partition_46": 2843, "ng:distributed:replication_46": 2844, "ng:distributed:availability_46": 2845, "ng:distributed:coordination_46": 2846, "ng:distributed:gossip_46": 2847, "ng:distributed:raft_46": 2848, "ng:distributed:consensus_47": 2849, "ng:distributed:partition_47": 2850, "ng:distributed:replication_47": 2851, "ng:distributed:consistency_47": 2852, "ng:distributed:availability_47": 2853, "ng:distributed:coordination_47": 2854, "ng:distributed:gossip_47": 2855, "ng:distributed:raft_47": 2856, "ng:distributed:consensus_48": 2857, "ng:distributed:partition_48": 2858, "ng:distributed:replication_48": 2859, "ng:distributed:consistency_48": 2860, "ng:distributed:availability_48": 2861, "ng:distributed:coordination_48": 2862, "ng:distributed:gossip_48": 2863, "ng:distributed:raft_48": 2864, "ng:distributed:consensus_49": 2865, "ng:distributed:partition_49": 2866, "ng:distributed:replication_49": 2867, "ng:distributed:consistency_49": 2868, "ng:distributed:availability_49": 2869, "ng:distributed:coordination_49": 2870, "ng:distributed:gossip_49": 2871, "ng:distributed:raft_49": 2872, "ng:distributed:consensus_50": 2873, "ng:distributed:partition_50": 2874, "ng:distributed:replication_50": 2875, "ng:distributed:consistency_50": 2876, "ng:distributed:availability_50": 2877, "ng:distributed:coordination_50": 2878, "ng:distributed:gossip_50": 2879, "ng:distributed:raft_50": 2880, "ng:distributed:consensus_51": 2881, "ng:distributed:partition_51": 2882, "ng:distributed:replication_51": 2883, "ng:distributed:consistency_51": 2884, "ng:distributed:availability_51": 2885, "ng:distributed:coordination_51": 2886, "ng:distributed:gossip_51": 2887, "ng:distributed:raft_51": 2888, "ng:distributed:consensus_52": 2889, "ng:distributed:partition_52": 2890, "ng:distributed:replication_52": 2891, "ng:distributed:consistency_52": 2892, "ng:distributed:availability_52": 2893, "ng:distributed:coordination_52": 2894, "ng:distributed:gossip_52": 2895, "ng:distributed:raft_52": 2896, "ng:distributed:consensus_53": 2897, "ng:distributed:partition_53": 2898, "ng:distributed:replication_53": 2899, "ng:distributed:consistency_53": 2900, "ng:distributed:availability_53": 2901, "ng:distributed:coordination_53": 2902, "ng:distributed:gossip_53": 2903, "ng:distributed:raft_53": 2904, "ng:distributed:consensus_54": 2905, "ng:distributed:partition_54": 2906, "ng:distributed:replication_54": 2907, "ng:distributed:consistency_54": 2908, "ng:distributed:availability_54": 2909, "ng:distributed:coordination_54": 2910, "ng:distributed:gossip_54": 2911, "ng:distributed:raft_54": 2912, "ng:distributed:consensus_55": 2913, "ng:distributed:partition_55": 2914, "ng:distributed:replication_55": 2915, "ng:distributed:consistency_55": 2916, "ng:distributed:availability_55": 2917, "ng:distributed:coordination_55": 2918, "ng:distributed:gossip_55": 2919, "ng:distributed:raft_55": 2920, "ng:distributed:consensus_56": 2921, "ng:distributed:partition_56": 2922, "ng:distributed:replication_56": 2923, "ng:distributed:consistency_56": 2924, "ng:distributed:availability_56": 2925, "ng:distributed:coordination_56": 2926, "ng:distributed:gossip_56": 2927, "ng:distributed:raft_56": 2928, "ng:distributed:consensus_57": 2929, "ng:distributed:partition_57": 2930, "ng:distributed:replication_57": 2931, "ng:distributed:consistency_57": 2932, "ng:distributed:availability_57": 2933, "ng:distributed:coordination_57": 2934, "ng:distributed:gossip_57": 2935, "ng:distributed:raft_57": 2936, "ng:distributed:consensus_58": 2937, "ng:distributed:partition_58": 2938, "ng:distributed:replication_58": 2939, "ng:distributed:consistency_58": 2940, "ng:distributed:availability_58": 2941, "ng:distributed:coordination_58": 2942, "ng:distributed:gossip_58": 2943, "ng:distributed:raft_58": 2944, "ng:distributed:consensus_59": 2945, "ng:distributed:partition_59": 2946, "ng:distributed:replication_59": 2947, "ng:distributed:consistency_59": 2948, "ng:distributed:availability_59": 2949, "ng:distributed:coordination_59": 2950, "ng:distributed:gossip_59": 2951, "ng:distributed:raft_59": 2952, "ng:distributed:consensus_60": 2953, "ng:distributed:partition_60": 2954, "ng:distributed:replication_60": 2955, "ng:distributed:consistency_60": 2956, "ng:distributed:availability_60": 2957, "ng:distributed:coordination_60": 2958, "ng:distributed:gossip_60": 2959, "ng:distributed:raft_60": 2960, "ng:distributed:consensus_61": 2961, "ng:distributed:partition_61": 2962, "ng:distributed:replication_61": 2963, "ng:distributed:consistency_61": 2964, "ng:distributed:availability_61": 2965, "ng:distributed:coordination_61": 2966, "ng:distributed:gossip_61": 2967, "ng:distributed:raft_61": 2968, "ng:distributed:consensus_62": 2969, "ng:distributed:partition_62": 2970, "ng:distributed:replication_62": 2971, "ng:distributed:consistency_62": 2972, "ng:distributed:availability_62": 2973, "ng:distributed:coordination_62": 2974, "ng:distributed:gossip_62": 2975, "ng:distributed:raft_62": 2976, "ng:distributed:consensus_63": 2977, "ng:distributed:partition_63": 2978, "ng:distributed:replication_63": 2979, "ng:distributed:consistency_63": 2980, "ng:distributed:availability_63": 2981, "ng:distributed:coordination_63": 2982, "ng:distributed:gossip_63": 2983, "ng:distributed:raft_63": 2984, "ng:distributed:consensus_64": 2985, "ng:distributed:replication_64": 2986, "ng:distributed:consistency_64": 2987, "ng:distributed:availability_64": 2988, "ng:distributed:coordination_64": 2989, "ng:distributed:gossip_64": 2990, "ng:distributed:raft_64": 2991, "ng:distributed:consensus_65": 2992, "ng:distributed:partition_65": 2993, "ng:distributed:replication_65": 2994, "ng:distributed:consistency_65": 2995, "ng:distributed:availability_65": 2996, "ng:distributed:gossip_65": 2997, "ng:distributed:raft_65": 2998, "ng:distributed:consensus_66": 2999, "ng:distributed:partition_66": 3000, "ng:distributed:replication_66": 3001, "ng:distributed:consistency_66": 3002, "ng:distributed:availability_66": 3003, "ng:distributed:coordination_66": 3004, "ng:distributed:gossip_66": 3005, "ng:distributed:raft_66": 3006, "ng:distributed:consensus_67": 3007, "ng:distributed:partition_67": 3008, "ng:distributed:replication_67": 3009, "ng:distributed:consistency_67": 3010, "ng:distributed:availability_67": 3011, "ng:distributed:coordination_67": 3012, "ng:distributed:gossip_67": 3013, "ng:distributed:raft_67": 3014, "ng:distributed:consensus_68": 3015, "ng:distributed:partition_68": 3016, "ng:distributed:replication_68": 3017, "ng:distributed:consistency_68": 3018, "ng:distributed:availability_68": 3019, "ng:distributed:coordination_68": 3020, "ng:distributed:gossip_68": 3021, "ng:distributed:raft_68": 3022, "ng:distributed:consensus_69": 3023, "ng:distributed:partition_69": 3024, "ng:distributed:replication_69": 3025, "ng:distributed:consistency_69": 3026, "ng:distributed:availability_69": 3027, "ng:distributed:coordination_69": 3028, "ng:distributed:gossip_69": 3029, "ng:distributed:raft_69": 3030, "ng:distributed:consensus_70": 3031, "ng:distributed:partition_70": 3032, "ng:distributed:replication_70": 3033, "ng:distributed:consistency_70": 3034, "ng:distributed:availability_70": 3035, "ng:distributed:coordination_70": 3036, "ng:distributed:gossip_70": 3037, "ng:distributed:raft_70": 3038, "ng:distributed:consensus_71": 3039, "ng:distributed:partition_71": 3040, "ng:distributed:replication_71": 3041, "ng:distributed:consistency_71": 3042, "ng:distributed:availability_71": 3043, "ng:distributed:coordination_71": 3044, "ng:distributed:gossip_71": 3045, "ng:distributed:raft_71": 3046, "ng:distributed:consensus_72": 3047, "ng:distributed:partition_72": 3048, "ng:distributed:replication_72": 3049, "ng:distributed:consistency_72": 3050, "ng:distributed:availability_72": 3051, "ng:distributed:coordination_72": 3052, "ng:distributed:gossip_72": 3053, "ng:distributed:raft_72": 3054, "ng:distributed:consensus_73": 3055, "ng:distributed:partition_73": 3056, "ng:distributed:replication_73": 3057, "ng:distributed:consistency_73": 3058, "ng:distributed:availability_73": 3059, "ng:distributed:coordination_73": 3060, "ng:distributed:gossip_73": 3061, "ng:distributed:raft_73": 3062, "ng:distributed:consensus_74": 3063, "ng:distributed:partition_74": 3064, "ng:distributed:replication_74": 3065, "ng:distributed:consistency_74": 3066, "ng:distributed:availability_74": 3067, "ng:distributed:coordination_74": 3068, "ng:distributed:gossip_74": 3069, "ng:distributed:raft_74": 3070, "ng:distributed:consensus_75": 3071, "ng:distributed:partition_75": 3072, "ng:distributed:replication_75": 3073, "ng:distributed:consistency_75": 3074, "ng:distributed:availability_75": 3075, "ng:distributed:coordination_75": 3076, "ng:distributed:gossip_75": 3077, "ng:distributed:raft_75": 3078, "ng:distributed:consensus_76": 3079, "ng:distributed:partition_76": 3080, "ng:distributed:replication_76": 3081, "ng:distributed:consistency_76": 3082, "ng:distributed:availability_76": 3083, "ng:distributed:coordination_76": 3084, "ng:distributed:gossip_76": 3085, "ng:distributed:raft_76": 3086, "ng:distributed:consensus_77": 3087, "ng:distributed:partition_77": 3088, "ng:distributed:replication_77": 3089, "ng:distributed:consistency_77": 3090, "ng:distributed:availability_77": 3091, "ng:distributed:coordination_77": 3092, "ng:distributed:gossip_77": 3093, "ng:distributed:raft_77": 3094, "ng:distributed:consensus_78": 3095, "ng:distributed:partition_78": 3096, "ng:distributed:replication_78": 3097, "ng:distributed:availability_78": 3098, "ng:distributed:coordination_78": 3099, "ng:distributed:gossip_78": 3100, "ng:distributed:raft_78": 3101, "ng:distributed:consensus_79": 3102, "ng:distributed:partition_79": 3103, "ng:distributed:replication_79": 3104, "ng:distributed:consistency_79": 3105, "ng:distributed:availability_79": 3106, "ng:distributed:coordination_79": 3107, "ng:distributed:gossip_79": 3108, "ng:distributed:raft_79": 3109, "ng:distributed:consensus_80": 3110, "ng:distributed:partition_80": 3111, "ng:distributed:replication_80": 3112, "ng:distributed:consistency_80": 3113, "ng:distributed:availability_80": 3114, "ng:distributed:coordination_80": 3115, "ng:distributed:gossip_80": 3116, "ng:distributed:raft_80": 3117, "ng:distributed:consensus_81": 3118, "ng:distributed:partition_81": 3119, "ng:distributed:replication_81": 3120, "ng:distributed:consistency_81": 3121, "ng:distributed:availability_81": 3122, "ng:distributed:coordination_81": 3123, "ng:distributed:gossip_81": 3124, "ng:distributed:raft_81": 3125, "ng:distributed:consensus_82": 3126, "ng:distributed:partition_82": 3127, "ng:distributed:replication_82": 3128, "ng:distributed:consistency_82": 3129, "ng:distributed:availability_82": 3130, "ng:distributed:coordination_82": 3131, "ng:distributed:gossip_82": 3132, "ng:distributed:raft_82": 3133, "ng:distributed:consensus_83": 3134, "ng:distributed:partition_83": 3135, "ng:distributed:replication_83": 3136, "ng:distributed:consistency_83": 3137, "ng:distributed:availability_83": 3138, "ng:distributed:coordination_83": 3139, "ng:distributed:gossip_83": 3140, "ng:distributed:raft_83": 3141, "ng:distributed:consensus_84": 3142, "ng:distributed:partition_84": 3143, "ng:distributed:replication_84": 3144, "ng:distributed:consistency_84": 3145, "ng:distributed:availability_84": 3146, "ng:distributed:coordination_84": 3147, "ng:distributed:gossip_84": 3148, "ng:distributed:raft_84": 3149, "ng:distributed:consensus_85": 3150, "ng:distributed:partition_85": 3151, "ng:distributed:replication_85": 3152, "ng:distributed:consistency_85": 3153, "ng:distributed:availability_85": 3154, "ng:distributed:coordination_85": 3155, "ng:distributed:gossip_85": 3156, "ng:distributed:raft_85": 3157, "ng:distributed:consensus_86": 3158, "ng:distributed:partition_86": 3159, "ng:distributed:replication_86": 3160, "ng:distributed:consistency_86": 3161, "ng:distributed:availability_86": 3162, "ng:distributed:coordination_86": 3163, "ng:distributed:gossip_86": 3164, "ng:distributed:raft_86": 3165, "❉": 3166, "◾": 3167, "⭑": 3168, "⧒": 3169, "⦷": 3170, "⦾": 3171, "✣": 3172, "⪧": 3173, "⭘": 3174, "⭕": 3175, "✮": 3176, "⭖": 3177, "⨏": 3178, "⦑": 3179, "⬄": 3180, "⤎": 3181, "∧": 3182, "∨": 3183, "✃": 3184, "❙": 3185, "⬁": 3186, "⦧": 3187, "⫚": 3188, "⨤": 3189, "⦥": 3190, "⤚": 3191, "⮕": 3192, "⤜": 3193, "⧴": 3194, "⩗": 3195, "⪾": 3196, "✻": 3197, "⯊": 3198, "⏜": 3199, "✹": 3200, "⩖": 3201, "⩂": 3202, "✁": 3203, "⧖": 3204, "⥎": 3205, "⥬": 3206, "◈": 3207, "❂": 3208, "⤬": 3209, "⦨": 3210, "⩈": 3211, "⩞": 3212, "⫵": 3213, "⬕": 3214, "⬺": 3215, "⭚": 3216, "⮢": 3217, "⍓": 3218, "⧎": 3219, "⤇": 3220, "⩜": 3221, "⭣": 3222, "⤆": 3223, "⭢": 3224, "⦏": 3225, "⤫": 3226, "⍗": 3227, "⤠": 3228, "⤯": 3229, "⭛": 3230, "⍔": 3231, "❕": 3232, "⥓": 3233, "⮊": 3234, "🜁": 3235, "🝪": 3236, "🝅": 3237, "🝗": 3238, "🝂": 3239, "🝃": 3240, "🝙": 3241, "🝠": 3242, "🜃": 3243, "🜂": 3244, "🝉": 3245, "🝋": 3246, "🜲": 3247, "🝭": 3248, "🜔": 3249, "🜛": 3250, "🝔": 3251, "🝇": 3252, "🜍": 3253, "🜿": 3254, "🝕": 3255, "🜊": 3256, "🜄": 3257, "🝊": 3258, "🞟": 3259, "🞗": 3260, "🞌": 3261, "🞘": 3262, "🞞": 3263, "🞱": 3264, "🞤": 3265, "🞫": 3266, "⊘": 3267, "⊖": 3268, "⊗": 3269, "⫽": 3270, "🠿": 3271, "🠷": 3272, "🡇": 3273, "🢓": 3274, "🞧": 3275, "🞮": 3276, "🟆": 3277, "🞾": 3278, "🞲": 3279, "🞬": 3280, "🟌": 3281, "🞇": 3282, "🟫": 3283, "🟢": 3284, "🡘": 3285, "🠴": 3286, "🟄": 3287, "🞎": 3288, "🞼": 3289, "🞰": 3290, "🞏": 3291, "🟖": 3292, "🡔": 3293, "⯽": 3294, "⏾": 3295, "🠾": 3296, "⯻": 3297, "🞡": 3298, "🞨": 3299, "⫸": 3300, "⊨": 3301, "🠽": 3302, "🡅": 3303, "🞥": 3304, "🞒": 3305, "🢣": 3306, "🝐": 3307, "🟻": 3308, "⯶": 3309, "🞛": 3310, "🡨": 3311, "🜵": 3312, "🢀": 3313, "🟵": 3314, "🜷": 3315, "🝸": 3316, "🢯": 3317, "🟈": 3318, "⯴": 3319, "🟳": 3320, "⯸": 3321, "🠚": 3322, "🢁": 3323, "🡛": 3324, "🜳": 3325, "🞕": 3326, "🞠": 3327, "🢚": 3328, "🜝": 3329, "🠦": 3330, "🢘": 3331, "🢥": 3332, "🟚": 3333, "🟎": 3334, "🠃": 3335, "🢙": 3336, "🜼": 3337, "🝘": 3338, "🡴": 3339, "🝓": 3340, "🢔": 3341, "⫻": 3342, "🢳": 3343, "🟯": 3344, "🣃": 3345, "🢛": 3346, "🜱": 3347, "🢱": 3348, "🞴": 3349, "🠝": 3350, "⯷": 3351, "🣆": 3352, "🜘": 3353, "🟱": 3354, "🠉": 3355, "🟔": 3356, "🝎": 3357, "🠢": 3358, "🝩": 3359, "🠪": 3360, "🟃": 3361, "🟝": 3362, "🡈": 3363, "⯵": 3364, "🞂": 3365, "🟮": 3366, "🡞": 3367, "🡌": 3368, "🜉": 3369, "⋻": 3370, "🠘": 3371, "🟰": 3372, "🜢": 3373, "🠇": 3374, "🝏": 3375, "🢍": 3376, "🟒": 3377, "🠛": 3378, "🡼": 3379, "🡎": 3380, "🣂": 3381, "🝵": 3382, "🢕": 3383, "🟹": 3384, "🠫": 3385, "🠳": 3386, "🞳": 3387, "🟷": 3388, "🢎": 3389, "🝢": 3390, "🢧": 3391, "🠭": 3392, "🜭": 3393, "🞹": 3394, "🜎": 3395, "🢂": 3396, "⯳": 3397, "🜈": 3398, "🜗": 3399, "🠆": 3400, "🡩": 3401, "🡠": 3402, "🞚": 3403, "🜏": 3404, "🠲": 3405, "🜾": 3406, "🝁": 3407, "🞀": 3408, "🢃": 3409, "🢋": 3410, "🠂": 3411, "🠈": 3412, "🝲": 3413, "🢺": 3414, "🟶": 3415, "🡵": 3416, "🞿": 3417, "🟅": 3418, "🠣": 3419, "🝼": 3420, "⍳": 3421, "⍵": 3422, "⍴": 3423, "⩴": 3424, "⩱": 3425, "⩮": 3426, "≮": 3427, "⩲": 3428, "⧵": 3429, "⩵": 3430, "⍲": 3431, "❲": 3432, "≴": 3433, "❳": 3434, "❵": 3435, "⥪": 3436, "⩳": 3437, "⭳": 3438, "⭴": 3439, "⍯": 3440, "❯": 3441, "⩯": 3442, "≰": 3443, "⍰": 3444, "⪁": 3445, "⥱": 3446, "⍮": 3447, "⭮": 3448, "⇘": 3449, "⇪": 3450, "↴": 3451, "⇓": 3452, "⇥": 3453, "↝": 3454, "ℬ": 3455, "℈": 3456, "⇷": 3457, "⇁": 3458, "ⅇ": 3459, "⇉": 3460, "ℴ": 3461, "↷": 3462, "⇭": 3463, "⦞": 3464, "≃": 3465, "∵": 3466, "⍾": 3467, "✬": 3468, "▰": 3469, "⌍": 3470, "⌟": 3471, "⌌": 3472, "⏡": 3473, "◎": 3474, "◐": 3475, "◓": 3476, "⊳": 3477, "≘": 3478, "⌀": 3479, "⬖": 3480, "⬗": 3481, "∣": 3482, "⋇": 3483, "⋅": 3484, "⩔": 3485, "„": 3486, "ℂ": 3487, "⋱": 3488, "⤋": 3489, "↡": 3490, "⌁": 3491, "⋝": 3492, "≍": 3493, "⨍": 3494, "⊩": 3495, "≳": 3497, "✚": 3499, "⧜": 3500, "⨎": 3501, "⌨": 3502, "⬲": 3503, "⌊": 3504, "〈": 3505, "←": 3506, "≲": 3507, "ₒ": 3508, "↹": 3509, "⇍": 3510, "⇎": 3511, "⇞": 3512, "≒": 3513, "⊯": 3514, "⋭": 3515, "⌾": 3516, "⍈": 3517, "⍊": 3518, "⍐": 3519, "⍖": 3520, "⍛": 3521, "⍫": 3522, "⎋": 3523, "⎓": 3524, "⏆": 3525, "⩛": 3526, "◊": 3527, "≫": 3528, "₦": 3529, "⊼": 3530, "⎘": 3531, "‑": 3532, "≄": 3533, "≭": 3534, "⋪": 3535, "℥": 3536, "✙": 3537, "⑽": 3538, "⋔": 3539, "⌘": 3540, "℞": 3541, "⌋": 3542, "◗": 3543, "⊢": 3544, "⦄": 3545, "≗": 3546, "ℐ": 3547, "⌓": 3548, "∿": 3549, "⋾": 3551, "∊": 3552, "⌑": 3553, "⁺": 3554, "∯": 3555, "⦀": 3556, "⩕": 3557, "✐": 3558, "◹": 3559, "⤒": 3560, "⤊": 3561, "⌚": 3562, "⌇": 3563, "◇": 3564, "◅": 3565, "◽": 3566, "◻": 3567, "▦": 3568, "⋉": 3569, "⬽": 3570, "❏": 3571, "⦈": 3572, "⨒": 3573, "⋨": 3574, "◰": 3575, "⤧": 3576, "⬑": 3577, "ng:logic:modal": 3578, "ng:logic:fuzzy": 3579, "ng:logic:quantum": 3580, "ng:logic:negation_1": 3581, "ng:logic:conjunction_2": 3582, "ng:logic:negation_2": 3583, "ng:logic:temporal_2": 3584, "ng:logic:conjunction_3": 3585, "ng:logic:temporal_3": 3586, "ng:logic:negation_4": 3587, "ng:logic:modal_5": 3588, "ng:logic:temporal_5": 3589, "ng:logic:implication_6": 3590, "ng:logic:modal_6": 3591, "ng:logic:fuzzy_6": 3592, "ng:logic:conjunction_7": 3593, "ng:logic:negation_7": 3594, "ng:logic:temporal_7": 3595, "ng:logic:fuzzy_7": 3596, "ng:logic:quantum_7": 3597, "ng:logic:negation_8": 3598, "ng:logic:biconditional_8": 3599, "ng:logic:quantum_8": 3600, "ng:logic:quantum_9": 3601, "ng:logic:negation_10": 3602, "ng:logic:modal_10": 3603, "ng:logic:temporal_10": 3604, "ng:logic:fuzzy_10": 3605, "ng:logic:quantum_10": 3606, "ng:logic:biconditional_11": 3607, "ng:logic:modal_11": 3608, "ng:logic:fuzzy_11": 3609, "ng:logic:quantum_11": 3610, "ng:logic:implication_12": 3611, "ng:logic:conjunction_12": 3612, "ng:logic:negation_12": 3613, "ng:logic:biconditional_12": 3614, "ng:logic:modal_12": 3615, "ng:logic:fuzzy_12": 3616, "ng:logic:implication_13": 3617, "ng:logic:negation_13": 3618, "ng:logic:biconditional_13": 3619, "ng:logic:modal_13": 3620, "ng:logic:temporal_13": 3621, "ng:logic:quantum_13": 3622, "ng:logic:temporal_14": 3623, "ng:logic:fuzzy_14": 3624, "ng:logic:implication_15": 3625, "ng:logic:negation_15": 3626, "ng:logic:biconditional_15": 3627, "ng:logic:modal_15": 3628, "ng:logic:temporal_15": 3629, "ng:logic:fuzzy_15": 3630, "ng:logic:implication_16": 3631, "ng:logic:biconditional_16": 3632, "ng:logic:modal_16": 3633, "ng:logic:temporal_16": 3634, "ng:logic:fuzzy_16": 3635, "ng:logic:quantum_16": 3636, "ng:logic:implication_17": 3637, "ng:logic:modal_17": 3638, "ng:logic:temporal_17": 3639, "ng:logic:fuzzy_17": 3640, "ng:logic:negation_18": 3641, "ng:logic:biconditional_18": 3642, "ng:logic:fuzzy_18": 3643, "ng:logic:implication_19": 3644, "ng:logic:negation_19": 3645, "ng:logic:biconditional_19": 3646, "ng:logic:fuzzy_19": 3647, "ng:logic:implication_20": 3648, "ng:logic:negation_20": 3649, "ng:logic:biconditional_20": 3650, "ng:logic:modal_20": 3651, "ng:logic:temporal_20": 3652, "ng:logic:conjunction_21": 3653, "ng:logic:biconditional_21": 3654, "ng:logic:modal_21": 3655, "ng:logic:fuzzy_21": 3656, "ng:logic:implication_22": 3657, "ng:logic:negation_22": 3658, "ng:logic:biconditional_22": 3659, "ng:logic:modal_22": 3660, "ng:logic:fuzzy_22": 3661, "ng:logic:quantum_22": 3662, "ng:logic:implication_25": 3663, "ng:logic:conjunction_25": 3664, "ng:logic:negation_25": 3665, "ng:logic:biconditional_25": 3666, "ng:logic:modal_25": 3667, "ng:logic:temporal_25": 3668, "ng:logic:fuzzy_25": 3669, "ng:logic:quantum_25": 3670, "ng:logic:implication_26": 3671, "ng:logic:conjunction_26": 3672, "ng:logic:negation_26": 3673, "ng:logic:biconditional_26": 3674, "ng:logic:modal_26": 3675, "ng:logic:temporal_26": 3676, "ng:logic:fuzzy_26": 3677, "ng:logic:quantum_26": 3678, "ng:logic:implication_27": 3679, "ng:logic:conjunction_27": 3680, "ng:logic:negation_27": 3681, "ng:logic:biconditional_27": 3682, "ng:logic:modal_27": 3683, "ng:logic:temporal_27": 3684, "ng:logic:fuzzy_27": 3685, "ng:logic:quantum_27": 3686, "ng:logic:implication_28": 3687, "ng:logic:conjunction_28": 3688, "ng:logic:negation_28": 3689, "ng:logic:biconditional_28": 3690, "ng:logic:modal_28": 3691, "ng:logic:temporal_28": 3692, "ng:logic:fuzzy_28": 3693, "ng:logic:quantum_28": 3694, "ng:logic:implication_29": 3695, "ng:logic:conjunction_29": 3696, "ng:logic:negation_29": 3697, "ng:logic:biconditional_29": 3698, "ng:logic:modal_29": 3699, "ng:logic:temporal_29": 3700, "ng:logic:fuzzy_29": 3701, "ng:logic:quantum_29": 3702, "ng:logic:implication_30": 3703, "ng:logic:conjunction_30": 3704, "ng:logic:negation_30": 3705, "ng:logic:biconditional_30": 3706, "ng:logic:modal_30": 3707, "ng:logic:temporal_30": 3708, "ng:logic:fuzzy_30": 3709, "ng:logic:quantum_30": 3710, "ng:logic:implication_31": 3711, "ng:logic:conjunction_31": 3712, "ng:logic:negation_31": 3713, "ng:logic:biconditional_31": 3714, "ng:logic:modal_31": 3715, "ng:logic:temporal_31": 3716, "ng:logic:fuzzy_31": 3717, "ng:logic:quantum_31": 3718, "ng:logic:implication_32": 3719, "ng:logic:conjunction_32": 3720, "ng:logic:negation_32": 3721, "ng:logic:biconditional_32": 3722, "ng:logic:modal_32": 3723, "ng:logic:temporal_32": 3724, "ng:logic:fuzzy_32": 3725, "ng:logic:quantum_32": 3726, "ng:logic:implication_33": 3727, "ng:logic:conjunction_33": 3728, "ng:logic:negation_33": 3729, "ng:logic:biconditional_33": 3730, "ng:logic:modal_33": 3731, "ng:logic:temporal_33": 3732, "ng:logic:fuzzy_33": 3733, "ng:logic:quantum_33": 3734, "ng:logic:implication_34": 3735, "ng:logic:conjunction_34": 3736, "ng:logic:negation_34": 3737, "ng:logic:biconditional_34": 3738, "ng:logic:modal_34": 3739, "ng:logic:temporal_34": 3740, "ng:logic:fuzzy_34": 3741, "ng:logic:quantum_34": 3742, "ng:logic:implication_35": 3743, "ng:logic:conjunction_35": 3744, "ng:logic:negation_35": 3745, "ng:logic:biconditional_35": 3746, "ng:logic:modal_35": 3747, "ng:logic:temporal_35": 3748, "ng:logic:fuzzy_35": 3749, "ng:logic:quantum_35": 3750, "ng:logic:implication_36": 3751, "ng:logic:conjunction_36": 3752, "ng:logic:negation_36": 3753, "ng:logic:biconditional_36": 3754, "ng:logic:modal_36": 3755, "ng:logic:temporal_36": 3756, "ng:logic:fuzzy_36": 3757, "ng:logic:quantum_36": 3758, "ng:logic:implication_37": 3759, "ng:logic:conjunction_37": 3760, "ng:logic:negation_37": 3761, "ng:logic:biconditional_37": 3762, "ng:logic:modal_37": 3763, "ng:logic:temporal_37": 3764, "ng:logic:fuzzy_37": 3765, "ng:logic:quantum_37": 3766, "ng:logic:implication_38": 3767, "ng:logic:conjunction_38": 3768, "ng:logic:negation_38": 3769, "ng:logic:modal_38": 3770, "ng:logic:temporal_38": 3771, "ng:logic:fuzzy_38": 3772, "ng:logic:quantum_38": 3773, "ng:logic:implication_39": 3774, "ng:logic:conjunction_39": 3775, "ng:logic:negation_39": 3776, "ng:logic:biconditional_39": 3777, "ng:logic:modal_39": 3778, "ng:logic:temporal_39": 3779, "ng:logic:fuzzy_39": 3780, "ng:logic:quantum_39": 3781, "ng:logic:implication_40": 3782, "ng:logic:conjunction_40": 3783, "ng:logic:negation_40": 3784, "ng:logic:biconditional_40": 3785, "ng:logic:modal_40": 3786, "ng:logic:temporal_40": 3787, "ng:logic:fuzzy_40": 3788, "ng:logic:quantum_40": 3789, "ng:logic:implication_41": 3790, "ng:logic:conjunction_41": 3791, "ng:logic:negation_41": 3792, "ng:logic:biconditional_41": 3793, "ng:logic:modal_41": 3794, "ng:logic:temporal_41": 3795, "ng:logic:fuzzy_41": 3796, "ng:logic:quantum_41": 3797, "ng:logic:implication_42": 3798, "ng:logic:conjunction_42": 3799, "ng:logic:negation_42": 3800, "ng:logic:biconditional_42": 3801, "ng:logic:modal_42": 3802, "ng:logic:temporal_42": 3803, "ng:logic:fuzzy_42": 3804, "ng:logic:quantum_42": 3805, "ng:logic:implication_43": 3806, "ng:logic:conjunction_43": 3807, "ng:logic:negation_43": 3808, "ng:logic:biconditional_43": 3809, "ng:logic:modal_43": 3810, "ng:logic:temporal_43": 3811, "ng:logic:fuzzy_43": 3812, "ng:logic:quantum_43": 3813, "ng:logic:implication_44": 3814, "ng:logic:conjunction_44": 3815, "ng:logic:negation_44": 3816, "ng:logic:biconditional_44": 3817, "ng:logic:modal_44": 3818, "ng:logic:temporal_44": 3819, "ng:logic:fuzzy_44": 3820, "ng:logic:quantum_44": 3821, "ng:logic:conjunction_45": 3822, "ng:logic:negation_45": 3823, "ng:logic:biconditional_45": 3824, "ng:logic:modal_45": 3825, "ng:logic:temporal_45": 3826, "ng:logic:fuzzy_45": 3827, "ng:logic:quantum_45": 3828, "ng:logic:implication_46": 3829, "ng:logic:conjunction_46": 3830, "ng:logic:negation_46": 3831, "ng:logic:biconditional_46": 3832, "ng:logic:modal_46": 3833, "ng:logic:temporal_46": 3834, "ng:logic:fuzzy_46": 3835, "ng:logic:quantum_46": 3836, "ng:logic:implication_47": 3837, "ng:logic:conjunction_47": 3838, "ng:logic:negation_47": 3839, "ng:logic:biconditional_47": 3840, "ng:logic:modal_47": 3841, "ng:logic:temporal_47": 3842, "ng:logic:fuzzy_47": 3843, "ng:logic:quantum_47": 3844, "ng:logic:implication_48": 3845, "ng:logic:conjunction_48": 3846, "ng:logic:negation_48": 3847, "ng:logic:biconditional_48": 3848, "ng:logic:modal_48": 3849, "ng:logic:temporal_48": 3850, "ng:logic:fuzzy_48": 3851, "ng:logic:quantum_48": 3852, "ng:logic:implication_49": 3853, "ng:logic:conjunction_49": 3854, "ng:logic:biconditional_49": 3855, "ng:logic:modal_49": 3856, "ng:logic:temporal_49": 3857, "ng:logic:fuzzy_49": 3858, "ng:logic:quantum_49": 3859, "ng:logic:implication_50": 3860, "ng:logic:conjunction_50": 3861, "ng:logic:negation_50": 3862, "ng:logic:biconditional_50": 3863, "ng:logic:modal_50": 3864, "ng:logic:temporal_50": 3865, "ng:logic:fuzzy_50": 3866, "ng:logic:quantum_50": 3867, "ng:logic:implication_51": 3868, "ng:logic:conjunction_51": 3869, "ng:logic:negation_51": 3870, "ng:logic:biconditional_51": 3871, "ng:logic:modal_51": 3872, "ng:logic:temporal_51": 3873, "ng:logic:fuzzy_51": 3874, "ng:logic:quantum_51": 3875, "ng:logic:implication_52": 3876, "ng:logic:conjunction_52": 3877, "ng:logic:negation_52": 3878, "ng:logic:biconditional_52": 3879, "ng:logic:modal_52": 3880, "ng:logic:temporal_52": 3881, "ng:logic:fuzzy_52": 3882, "ng:logic:quantum_52": 3883, "ng:logic:implication_53": 3884, "ng:logic:conjunction_53": 3885, "ng:logic:negation_53": 3886, "ng:logic:biconditional_53": 3887, "ng:logic:modal_53": 3888, "ng:logic:temporal_53": 3889, "ng:logic:fuzzy_53": 3890, "ng:logic:quantum_53": 3891, "ng:logic:implication_54": 3892, "ng:logic:conjunction_54": 3893, "ng:logic:negation_54": 3894, "ng:logic:biconditional_54": 3895, "ng:logic:modal_54": 3896, "ng:logic:temporal_54": 3897, "ng:logic:fuzzy_54": 3898, "ng:logic:quantum_54": 3899, "ng:logic:implication_55": 3900, "ng:logic:conjunction_55": 3901, "ng:logic:negation_55": 3902, "ng:logic:biconditional_55": 3903, "ng:logic:modal_55": 3904, "ng:logic:temporal_55": 3905, "ng:logic:fuzzy_55": 3906, "ng:logic:quantum_55": 3907, "ng:logic:implication_56": 3908, "ng:logic:conjunction_56": 3909, "ng:logic:negation_56": 3910, "ng:logic:biconditional_56": 3911, "ng:logic:modal_56": 3912, "ng:logic:temporal_56": 3913, "ng:logic:fuzzy_56": 3914, "ng:logic:quantum_56": 3915, "ng:logic:implication_57": 3916, "ng:logic:negation_57": 3917, "ng:logic:biconditional_57": 3918, "ng:logic:modal_57": 3919, "ng:logic:temporal_57": 3920, "ng:logic:fuzzy_57": 3921, "ng:logic:quantum_57": 3922, "ng:logic:implication_58": 3923, "ng:logic:conjunction_58": 3924, "ng:logic:negation_58": 3925, "ng:logic:biconditional_58": 3926, "ng:logic:modal_58": 3927, "ng:logic:temporal_58": 3928, "ng:logic:fuzzy_58": 3929, "ng:logic:quantum_58": 3930, "ng:logic:implication_59": 3931, "ng:logic:conjunction_59": 3932, "ng:logic:negation_59": 3933, "ng:logic:biconditional_59": 3934, "ng:logic:modal_59": 3935, "ng:logic:temporal_59": 3936, "ng:logic:fuzzy_59": 3937, "ng:logic:quantum_59": 3938, "ng:logic:implication_60": 3939, "ng:logic:conjunction_60": 3940, "ng:logic:negation_60": 3941, "ng:logic:biconditional_60": 3942, "ng:logic:modal_60": 3943, "ng:logic:temporal_60": 3944, "ng:logic:fuzzy_60": 3945, "ng:logic:quantum_60": 3946, "ng:logic:implication_61": 3947, "ng:logic:conjunction_61": 3948, "ng:logic:negation_61": 3949, "ng:logic:biconditional_61": 3950, "ng:logic:modal_61": 3951, "ng:logic:temporal_61": 3952, "ng:logic:fuzzy_61": 3953, "ng:logic:quantum_61": 3954, "ng:logic:implication_62": 3955, "ng:logic:conjunction_62": 3956, "ng:logic:negation_62": 3957, "ng:logic:biconditional_62": 3958, "ng:logic:modal_62": 3959, "ng:logic:temporal_62": 3960, "ng:logic:fuzzy_62": 3961, "ng:logic:quantum_62": 3962, "ng:logic:implication_63": 3963, "ng:logic:conjunction_63": 3964, "ng:logic:negation_63": 3965, "ng:logic:biconditional_63": 3966, "ng:logic:modal_63": 3967, "ng:logic:temporal_63": 3968, "ng:logic:fuzzy_63": 3969, "ng:logic:quantum_63": 3970, "ng:logic:implication_64": 3971, "ng:logic:conjunction_64": 3972, "ng:logic:negation_64": 3973, "ng:logic:biconditional_64": 3974, "ng:logic:modal_64": 3975, "ng:logic:temporal_64": 3976, "ng:logic:fuzzy_64": 3977, "ng:logic:quantum_64": 3978, "ng:logic:implication_65": 3979, "ng:logic:conjunction_65": 3980, "ng:logic:negation_65": 3981, "ng:logic:biconditional_65": 3982, "ng:logic:modal_65": 3983, "ng:logic:temporal_65": 3984, "ng:logic:fuzzy_65": 3985, "ng:logic:quantum_65": 3986, "ng:logic:implication_66": 3987, "ng:logic:conjunction_66": 3988, "ng:logic:negation_66": 3989, "ng:logic:biconditional_66": 3990, "ng:logic:modal_66": 3991, "ng:logic:temporal_66": 3992, "ng:logic:fuzzy_66": 3993, "ng:logic:quantum_66": 3994, "ng:logic:implication_67": 3995, "ng:logic:conjunction_67": 3996, "ng:logic:negation_67": 3997, "ng:logic:biconditional_67": 3998, "ng:logic:modal_67": 3999, "ng:logic:temporal_67": 4000, "ng:logic:fuzzy_67": 4001, "ng:logic:quantum_67": 4002, "ng:logic:implication_68": 4003, "ng:logic:conjunction_68": 4004, "ng:logic:negation_68": 4005, "ng:logic:biconditional_68": 4006, "ng:logic:modal_68": 4007, "ng:logic:temporal_68": 4008, "ng:logic:fuzzy_68": 4009, "ng:logic:quantum_68": 4010, "ng:logic:implication_69": 4011, "ng:logic:conjunction_69": 4012, "ng:logic:negation_69": 4013, "ng:logic:biconditional_69": 4014, "ng:logic:modal_69": 4015, "ng:logic:temporal_69": 4016, "ng:logic:fuzzy_69": 4017, "ng:logic:quantum_69": 4018, "ng:logic:implication_70": 4019, "ng:logic:conjunction_70": 4020, "ng:logic:negation_70": 4021, "ng:logic:biconditional_70": 4022, "ng:logic:modal_70": 4023, "ng:logic:temporal_70": 4024, "ng:logic:fuzzy_70": 4025, "ng:logic:quantum_70": 4026, "ng:logic:implication_71": 4027, "ng:logic:conjunction_71": 4028, "ng:logic:negation_71": 4029, "ng:logic:biconditional_71": 4030, "ng:logic:modal_71": 4031, "ng:logic:temporal_71": 4032, "ng:logic:fuzzy_71": 4033, "ng:logic:quantum_71": 4034, "ng:logic:implication_72": 4035, "ng:logic:conjunction_72": 4036, "ng:logic:negation_72": 4037, "ng:logic:biconditional_72": 4038, "ng:logic:modal_72": 4039, "ng:logic:temporal_72": 4040, "ng:logic:fuzzy_72": 4041, "ng:logic:quantum_72": 4042, "ng:logic:implication_73": 4043, "ng:logic:conjunction_73": 4044, "ng:logic:negation_73": 4045, "ng:logic:biconditional_73": 4046, "ng:logic:modal_73": 4047, "ng:logic:temporal_73": 4048, "ng:logic:fuzzy_73": 4049, "ng:logic:quantum_73": 4050, "ng:logic:implication_74": 4051, "ng:logic:conjunction_74": 4052, "ng:logic:negation_74": 4053, "ng:logic:modal_74": 4054, "ng:logic:temporal_74": 4055, "ng:logic:fuzzy_74": 4056, "ng:logic:quantum_74": 4057, "ng:logic:implication_75": 4058, "ng:logic:conjunction_75": 4059, "ng:logic:negation_75": 4060, "ng:logic:biconditional_75": 4061, "ng:logic:modal_75": 4062, "ng:logic:temporal_75": 4063, "ng:logic:fuzzy_75": 4064, "ng:logic:quantum_75": 4065, "ng:logic:implication_76": 4066, "ng:logic:conjunction_76": 4067, "ng:logic:negation_76": 4068, "ng:logic:biconditional_76": 4069, "ng:logic:modal_76": 4070, "ng:logic:temporal_76": 4071, "ng:logic:fuzzy_76": 4072, "ng:logic:quantum_76": 4073, "ng:logic:implication_77": 4074, "ng:logic:conjunction_77": 4075, "ng:logic:negation_77": 4076, "ng:logic:biconditional_77": 4077, "ng:logic:modal_77": 4078, "ng:logic:temporal_77": 4079, "ng:logic:fuzzy_77": 4080, "ng:logic:quantum_77": 4081, "ng:logic:implication_78": 4082, "ng:logic:conjunction_78": 4083, "ng:logic:negation_78": 4084, "ng:logic:biconditional_78": 4085, "ng:logic:modal_78": 4086, "ng:logic:temporal_78": 4087, "ng:logic:fuzzy_78": 4088, "ng:logic:quantum_78": 4089, "ng:logic:implication_79": 4090, "ng:logic:conjunction_79": 4091, "ng:logic:negation_79": 4092, "ng:logic:modal_79": 4093, "ng:logic:temporal_79": 4094, "ng:logic:fuzzy_79": 4095, "ng:logic:quantum_79": 4096, "ng:logic:implication_80": 4097, "ng:logic:conjunction_80": 4098, "ng:logic:negation_80": 4099, "ng:logic:biconditional_80": 4100, "ng:logic:modal_80": 4101, "ng:logic:temporal_80": 4102, "ng:logic:fuzzy_80": 4103, "ng:logic:quantum_80": 4104, "ng:logic:implication_81": 4105, "ng:logic:conjunction_81": 4106, "ng:logic:negation_81": 4107, "ng:logic:biconditional_81": 4108, "ng:logic:modal_81": 4109, "ng:logic:temporal_81": 4110, "ng:logic:fuzzy_81": 4111, "ng:logic:quantum_81": 4112, "ng:logic:implication_82": 4113, "ng:logic:conjunction_82": 4114, "ng:logic:negation_82": 4115, "ng:logic:biconditional_82": 4116, "ng:logic:modal_82": 4117, "ng:logic:temporal_82": 4118, "ng:logic:fuzzy_82": 4119, "ng:logic:quantum_82": 4120, "ng:logic:implication_83": 4121, "ng:logic:conjunction_83": 4122, "ng:logic:negation_83": 4123, "ng:logic:biconditional_83": 4124, "ng:logic:modal_83": 4125, "ng:logic:temporal_83": 4126, "ng:logic:fuzzy_83": 4127, "ng:logic:quantum_83": 4128, "ng:logic:implication_84": 4129, "ng:logic:conjunction_84": 4130, "ng:logic:negation_84": 4131, "ng:logic:biconditional_84": 4132, "ng:logic:modal_84": 4133, "ng:logic:temporal_84": 4134, "ng:logic:fuzzy_84": 4135, "ng:logic:quantum_84": 4136, "ng:logic:implication_85": 4137, "ng:logic:conjunction_85": 4138, "ng:logic:negation_85": 4139, "ng:logic:biconditional_85": 4140, "ng:logic:modal_85": 4141, "ng:logic:temporal_85": 4142, "ng:logic:fuzzy_85": 4143, "ng:logic:quantum_85": 4144, "ng:logic:implication_86": 4145, "ng:logic:conjunction_86": 4146, "ng:logic:negation_86": 4147, "ng:logic:biconditional_86": 4148, "ng:logic:modal_86": 4149, "ng:logic:temporal_86": 4150, "ng:logic:fuzzy_86": 4151, "ng:logic:quantum_86": 4152, "ng:logic:implication_87": 4153, "ng:logic:conjunction_87": 4154, "ng:logic:negation_87": 4155, "ng:logic:biconditional_87": 4156, "ng:logic:modal_87": 4157, "ng:logic:temporal_87": 4158, "ng:logic:fuzzy_87": 4159, "ng:logic:quantum_87": 4160, "⤹": 4161, "◂": 4162, "⭃": 4163, "⨔": 4164, "◲": 4165, "⤈": 4166, "⦋": 4167, "⬏": 4168, "⭞": 4169, "▩": 4170, "↻": 4171, "ℸ": 4172, "℔": 4173, "↩": 4174, "↗": 4175, "⇱": 4176, "Ω": 4177, "⅋": 4178, "⇟": 4179, "⎕": 4180, "⊜": 4181, "⩭": 4182, "⎖": 4183, "⪢": 4184, "⪡": 4185, "➗": 4186, "➖": 4187, "➙": 4188, "➘": 4189, "⎎": 4190, "⎜": 4191, "⎝": 4192, "⎛": 4193, "⎍": 4194, "⊌": 4195, "⮒": 4196, "⮓": 4197, "⎒": 4198, "⯕": 4199, "⎗": 4200, "⎙": 4201, "⮑": 4202, "⮳": 4203, "⥽": 4204, "⎟": 4205, "⎠": 4206, "⪞": 4207, "⪝": 4208, "⪕": 4209, "⎔": 4210, "⊔": 4211, "⊑": 4212, "⊡": 4213, "⊟": 4214, "⮗": 4215, "➍": 4216, "➎": 4217, "➏": 4218, "⪌": 4219, "⪍": 4220, "⪎": 4221, "⮍": 4222, "➒": 4223, "➓": 4224, "➔": 4225, "➝": 4226, "➟": 4227, "➢": 4228, "➽": 4229, "⥵": 4230, "⪄": 4231, "⪋": 4232, "⪓": 4233, "⪔": 4234, "⪗": 4235, "⪛": 4236, "⫮": 4237, "⭰": 4238, "⭲": 4239, "⮖": 4240, "⮚": 4241, "⮛": 4242, "⮝": 4243, "⮠": 4244, "⮪": 4245, "⎐": 4246, "➐": 4247, "➑": 4248, "⩰": 4249, "⪏": 4250, "⪐": 4251, "⪑": 4252, "➌": 4253, "⮋": 4254, "➋": 4255, "⎇": 4256, "⊦": 4257, "ℭ": 4258, "⌡": 4259, "•": 4260, "⑭": 4261, "≔": 4262, "⌴": 4263, "≜": 4264, "⊀": 4265, "⊁": 4266, "⋸": 4267, "∅": 4268, "℮": 4269, "ℇ": 4270, "∹": 4271, "‒": 4272, "⊹": 4273, "⎻": 4274, "‐": 4275, "∾": 4276, "⇿": 4277, "ₐ": 4278, "ₓ": 4279, "⇀": 4280, "⌻": 4281, "⍉": 4282, "⎏": 4283, "⏅": 4284, "⊧": 4285, "⇗": 4287, "⒘": 4288, "⏻": 4289, "⎞": 4290, "Ⅶ": 4291, "Ⅹ": 4292, "⍽": 4293, "ⅰ": 4294, "↘": 4295, "↙": 4296, "⊠": 4297, "₀": 4298, "⊱": 4299, "∼": 4300, "⌠": 4301, "‴": 4302, "↥": 4303, "⇈": 4304, "∰": 4305, "ng:math:sum": 4306, "ng:math:category": 4307, "ng:math:sum_1": 4308, "ng:math:integral_3": 4309, "ng:math:tensor_3": 4310, "ng:math:topology_3": 4311, "ng:math:lambda_3": 4312, "ng:math:topology_5": 4313, "ng:math:lambda_5": 4314, "ng:math:tensor_6": 4315, "ng:math:matrix_6": 4316, "ng:math:sum_7": 4317, "ng:math:integral_7": 4318, "ng:math:matrix_7": 4319, "ng:math:function_7": 4320, "ng:math:topology_7": 4321, "ng:math:category_7": 4322, "ng:math:matrix_8": 4323, "ng:math:topology_8": 4324, "ng:math:category_8": 4325, "ng:math:tensor_9": 4326, "ng:math:tensor_10": 4327, "ng:math:function_10": 4328, "ng:math:category_10": 4329, "ng:math:lambda_10": 4330, "ng:math:sum_11": 4331, "ng:math:integral_11": 4332, "ng:math:tensor_11": 4333, "ng:math:topology_11": 4334, "ng:math:category_11": 4335, "ng:math:lambda_11": 4336, "ng:math:sum_12": 4337, "ng:math:integral_12": 4338, "ng:math:tensor_12": 4339, "ng:math:function_12": 4340, "ng:math:topology_12": 4341, "ng:math:category_12": 4342, "ng:math:function_13": 4343, "ng:math:topology_13": 4344, "ng:math:lambda_13": 4345, "ng:math:integral_14": 4346, "ng:math:tensor_14": 4347, "ng:math:matrix_14": 4348, "ng:math:function_14": 4349, "ng:math:category_14": 4350, "ng:math:lambda_14": 4351, "ng:math:sum_15": 4352, "ng:math:integral_15": 4353, "ng:math:tensor_15": 4354, "ng:math:function_15": 4355, "ng:math:topology_15": 4356, "ng:math:lambda_15": 4357, "ng:math:sum_16": 4358, "ng:math:tensor_16": 4359, "ng:math:matrix_16": 4360, "ng:math:function_16": 4361, "ng:math:topology_16": 4362, "ng:math:category_16": 4363, "ng:math:sum_17": 4364, "ng:math:integral_17": 4365, "ng:math:matrix_17": 4366, "ng:math:function_17": 4367, "ng:math:category_17": 4368, "ng:math:sum_18": 4369, "ng:math:integral_18": 4370, "ng:math:tensor_18": 4371, "ng:math:function_18": 4372, "ng:math:topology_18": 4373, "ng:math:category_18": 4374, "ng:math:sum_19": 4375, "ng:math:tensor_19": 4376, "ng:math:matrix_19": 4377, "ng:math:function_19": 4378, "ng:math:topology_19": 4379, "ng:math:lambda_19": 4380, "ng:math:sum_20": 4381, "ng:math:tensor_20": 4382, "ng:math:matrix_20": 4383, "ng:math:function_20": 4384, "ng:math:integral_21": 4385, "ng:math:tensor_21": 4386, "ng:math:category_21": 4387, "ng:math:integral_22": 4388, "ng:math:tensor_22": 4389, "ng:math:matrix_22": 4390, "ng:math:sum_24": 4391, "ng:math:integral_24": 4392, "ng:math:tensor_24": 4393, "ng:math:matrix_24": 4394, "ng:math:function_24": 4395, "ng:math:topology_24": 4396, "ng:math:category_24": 4397, "ng:math:lambda_24": 4398, "ng:math:sum_25": 4399, "ng:math:integral_25": 4400, "ng:math:tensor_25": 4401, "ng:math:matrix_25": 4402, "ng:math:function_25": 4403, "ng:math:topology_25": 4404, "ng:math:category_25": 4405, "ng:math:lambda_25": 4406, "ng:math:sum_26": 4407, "ng:math:integral_26": 4408, "ng:math:tensor_26": 4409, "ng:math:matrix_26": 4410, "ng:math:function_26": 4411, "ng:math:topology_26": 4412, "ng:math:category_26": 4413, "ng:math:lambda_26": 4414, "ng:math:sum_27": 4415, "ng:math:integral_27": 4416, "ng:math:tensor_27": 4417, "ng:math:matrix_27": 4418, "ng:math:function_27": 4419, "ng:math:topology_27": 4420, "ng:math:category_27": 4421, "ng:math:lambda_27": 4422, "ng:math:sum_28": 4423, "ng:math:integral_28": 4424, "ng:math:tensor_28": 4425, "ng:math:matrix_28": 4426, "ng:math:function_28": 4427, "ng:math:topology_28": 4428, "ng:math:category_28": 4429, "ng:math:lambda_28": 4430, "ng:math:sum_29": 4431, "ng:math:integral_29": 4432, "ng:math:tensor_29": 4433, "ng:math:matrix_29": 4434, "ng:math:function_29": 4435, "ng:math:topology_29": 4436, "ng:math:category_29": 4437, "ng:math:lambda_29": 4438, "ng:math:sum_30": 4439, "ng:math:integral_30": 4440, "ng:math:tensor_30": 4441, "ng:math:matrix_30": 4442, "ng:math:function_30": 4443, "ng:math:topology_30": 4444, "ng:math:category_30": 4445, "ng:math:lambda_30": 4446, "ng:math:sum_31": 4447, "ng:math:integral_31": 4448, "ng:math:tensor_31": 4449, "ng:math:matrix_31": 4450, "ng:math:function_31": 4451, "ng:math:topology_31": 4452, "ng:math:category_31": 4453, "ng:math:lambda_31": 4454, "ng:math:sum_32": 4455, "ng:math:integral_32": 4456, "ng:math:tensor_32": 4457, "ng:math:matrix_32": 4458, "ng:math:function_32": 4459, "ng:math:topology_32": 4460, "ng:math:category_32": 4461, "ng:math:lambda_32": 4462, "ng:math:sum_33": 4463, "ng:math:integral_33": 4464, "ng:math:tensor_33": 4465, "ng:math:matrix_33": 4466, "ng:math:function_33": 4467, "ng:math:topology_33": 4468, "ng:math:category_33": 4469, "ng:math:lambda_33": 4470, "ng:math:sum_34": 4471, "ng:math:integral_34": 4472, "ng:math:tensor_34": 4473, "ng:math:matrix_34": 4474, "ng:math:function_34": 4475, "ng:math:topology_34": 4476, "ng:math:category_34": 4477, "ng:math:lambda_34": 4478, "ng:math:sum_35": 4479, "ng:math:integral_35": 4480, "ng:math:tensor_35": 4481, "ng:math:matrix_35": 4482, "ng:math:function_35": 4483, "ng:math:topology_35": 4484, "ng:math:category_35": 4485, "ng:math:lambda_35": 4486, "ng:math:sum_36": 4487, "ng:math:integral_36": 4488, "ng:math:tensor_36": 4489, "ng:math:matrix_36": 4490, "ng:math:function_36": 4491, "ng:math:topology_36": 4492, "ng:math:category_36": 4493, "ng:math:lambda_36": 4494, "ng:math:sum_37": 4495, "ng:math:integral_37": 4496, "ng:math:tensor_37": 4497, "ng:math:matrix_37": 4498, "ng:math:function_37": 4499, "ng:math:topology_37": 4500, "ng:math:category_37": 4501, "ng:math:lambda_37": 4502, "ng:math:sum_38": 4503, "ng:math:integral_38": 4504, "ng:math:tensor_38": 4505, "ng:math:matrix_38": 4506, "ng:math:function_38": 4507, "ng:math:topology_38": 4508, "ng:math:category_38": 4509, "ng:math:lambda_38": 4510, "ng:math:sum_39": 4511, "ng:math:integral_39": 4512, "ng:math:tensor_39": 4513, "ng:math:matrix_39": 4514, "ng:math:function_39": 4515, "ng:math:topology_39": 4516, "ng:math:category_39": 4517, "ng:math:lambda_39": 4518, "ng:math:sum_40": 4519, "ng:math:integral_40": 4520, "ng:math:tensor_40": 4521, "ng:math:matrix_40": 4522, "ng:math:function_40": 4523, "ng:math:topology_40": 4524, "ng:math:category_40": 4525, "ng:math:lambda_40": 4526, "ng:math:sum_41": 4527, "ng:math:integral_41": 4528, "ng:math:tensor_41": 4529, "ng:math:matrix_41": 4530, "ng:math:function_41": 4531, "ng:math:topology_41": 4532, "ng:math:category_41": 4533, "ng:math:lambda_41": 4534, "ng:math:sum_42": 4535, "ng:math:integral_42": 4536, "ng:math:tensor_42": 4537, "ng:math:matrix_42": 4538, "ng:math:function_42": 4539, "ng:math:topology_42": 4540, "ng:math:category_42": 4541, "ng:math:lambda_42": 4542, "ng:math:sum_43": 4543, "ng:math:integral_43": 4544, "ng:math:tensor_43": 4545, "ng:math:matrix_43": 4546, "ng:math:function_43": 4547, "ng:math:topology_43": 4548, "ng:math:category_43": 4549, "ng:math:lambda_43": 4550, "ng:math:sum_44": 4551, "ng:math:integral_44": 4552, "ng:math:tensor_44": 4553, "ng:math:matrix_44": 4554, "ng:math:function_44": 4555, "ng:math:topology_44": 4556, "ng:math:category_44": 4557, "ng:math:lambda_44": 4558, "ng:math:sum_45": 4559, "ng:math:integral_45": 4560, "ng:math:tensor_45": 4561, "ng:math:matrix_45": 4562, "ng:math:function_45": 4563, "ng:math:topology_45": 4564, "ng:math:category_45": 4565, "ng:math:lambda_45": 4566, "ng:math:sum_46": 4567, "ng:math:integral_46": 4568, "ng:math:tensor_46": 4569, "ng:math:matrix_46": 4570, "ng:math:function_46": 4571, "ng:math:topology_46": 4572, "ng:math:category_46": 4573, "ng:math:lambda_46": 4574, "ng:math:sum_47": 4575, "ng:math:integral_47": 4576, "ng:math:tensor_47": 4577, "ng:math:matrix_47": 4578, "ng:math:function_47": 4579, "ng:math:topology_47": 4580, "ng:math:category_47": 4581, "ng:math:lambda_47": 4582, "ng:math:sum_48": 4583, "ng:math:integral_48": 4584, "ng:math:tensor_48": 4585, "ng:math:matrix_48": 4586, "ng:math:function_48": 4587, "ng:math:topology_48": 4588, "ng:math:category_48": 4589, "ng:math:lambda_48": 4590, "ng:math:sum_49": 4591, "ng:math:integral_49": 4592, "ng:math:tensor_49": 4593, "ng:math:matrix_49": 4594, "ng:math:function_49": 4595, "ng:math:topology_49": 4596, "ng:math:category_49": 4597, "ng:math:lambda_49": 4598, "ng:math:sum_50": 4599, "ng:math:integral_50": 4600, "ng:math:tensor_50": 4601, "ng:math:matrix_50": 4602, "ng:math:function_50": 4603, "ng:math:topology_50": 4604, "ng:math:category_50": 4605, "ng:math:lambda_50": 4606, "ng:math:sum_51": 4607, "ng:math:integral_51": 4608, "ng:math:tensor_51": 4609, "ng:math:matrix_51": 4610, "ng:math:function_51": 4611, "ng:math:topology_51": 4612, "ng:math:category_51": 4613, "ng:math:lambda_51": 4614, "ng:math:sum_52": 4615, "ng:math:integral_52": 4616, "ng:math:tensor_52": 4617, "ng:math:matrix_52": 4618, "ng:math:function_52": 4619, "ng:math:topology_52": 4620, "ng:math:category_52": 4621, "ng:math:lambda_52": 4622, "ng:math:sum_53": 4623, "ng:math:integral_53": 4624, "ng:math:tensor_53": 4625, "ng:math:matrix_53": 4626, "ng:math:function_53": 4627, "ng:math:topology_53": 4628, "ng:math:category_53": 4629, "ng:math:lambda_53": 4630, "ng:math:sum_54": 4631, "ng:math:integral_54": 4632, "ng:math:tensor_54": 4633, "ng:math:matrix_54": 4634, "ng:math:function_54": 4635, "ng:math:topology_54": 4636, "ng:math:category_54": 4637, "ng:math:lambda_54": 4638, "ng:math:sum_55": 4639, "ng:math:integral_55": 4640, "ng:math:tensor_55": 4641, "ng:math:matrix_55": 4642, "ng:math:function_55": 4643, "ng:math:topology_55": 4644, "ng:math:category_55": 4645, "ng:math:lambda_55": 4646, "ng:math:sum_56": 4647, "ng:math:integral_56": 4648, "ng:math:tensor_56": 4649, "ng:math:matrix_56": 4650, "ng:math:function_56": 4651, "ng:math:topology_56": 4652, "ng:math:category_56": 4653, "ng:math:lambda_56": 4654, "ng:math:sum_57": 4655, "ng:math:integral_57": 4656, "ng:math:tensor_57": 4657, "ng:math:matrix_57": 4658, "ng:math:function_57": 4659, "ng:math:topology_57": 4660, "ng:math:category_57": 4661, "ng:math:lambda_57": 4662, "ng:math:sum_58": 4663, "ng:math:integral_58": 4664, "ng:math:tensor_58": 4665, "ng:math:matrix_58": 4666, "ng:math:function_58": 4667, "ng:math:topology_58": 4668, "ng:math:category_58": 4669, "ng:math:lambda_58": 4670, "ng:math:sum_59": 4671, "ng:math:integral_59": 4672, "ng:math:tensor_59": 4673, "ng:math:matrix_59": 4674, "ng:math:function_59": 4675, "ng:math:topology_59": 4676, "ng:math:category_59": 4677, "ng:math:lambda_59": 4678, "ng:math:sum_60": 4679, "ng:math:integral_60": 4680, "ng:math:tensor_60": 4681, "ng:math:matrix_60": 4682, "ng:math:function_60": 4683, "ng:math:topology_60": 4684, "ng:math:category_60": 4685, "ng:math:lambda_60": 4686, "ng:math:sum_61": 4687, "ng:math:integral_61": 4688, "ng:math:tensor_61": 4689, "ng:math:matrix_61": 4690, "ng:math:function_61": 4691, "ng:math:topology_61": 4692, "ng:math:category_61": 4693, "ng:math:lambda_61": 4694, "ng:math:sum_62": 4695, "ng:math:integral_62": 4696, "ng:math:tensor_62": 4697, "ng:math:matrix_62": 4698, "ng:math:function_62": 4699, "ng:math:topology_62": 4700, "ng:math:category_62": 4701, "ng:math:lambda_62": 4702, "ng:math:sum_63": 4703, "ng:math:integral_63": 4704, "ng:math:tensor_63": 4705, "ng:math:function_63": 4706, "ng:math:topology_63": 4707, "ng:math:category_63": 4708, "ng:math:lambda_63": 4709, "ng:math:sum_64": 4710, "ng:math:integral_64": 4711, "ng:math:tensor_64": 4712, "ng:math:matrix_64": 4713, "ng:math:function_64": 4714, "ng:math:topology_64": 4715, "ng:math:category_64": 4716, "ng:math:lambda_64": 4717, "ng:math:sum_65": 4718, "ng:math:integral_65": 4719, "ng:math:tensor_65": 4720, "ng:math:matrix_65": 4721, "ng:math:function_65": 4722, "ng:math:topology_65": 4723, "ng:math:category_65": 4724, "ng:math:lambda_65": 4725, "ng:math:sum_66": 4726, "ng:math:integral_66": 4727, "ng:math:tensor_66": 4728, "ng:math:matrix_66": 4729, "ng:math:function_66": 4730, "ng:math:topology_66": 4731, "ng:math:category_66": 4732, "ng:math:lambda_66": 4733, "ng:math:sum_67": 4734, "ng:math:integral_67": 4735, "ng:math:tensor_67": 4736, "ng:math:matrix_67": 4737, "ng:math:function_67": 4738, "ng:math:topology_67": 4739, "ng:math:category_67": 4740, "ng:math:lambda_67": 4741, "ng:math:sum_68": 4742, "ng:math:integral_68": 4743, "ng:math:tensor_68": 4744, "ng:math:matrix_68": 4745, "ng:math:topology_68": 4746, "ng:math:category_68": 4747, "ng:math:lambda_68": 4748, "ng:math:sum_69": 4749, "ng:math:integral_69": 4750, "ng:math:tensor_69": 4751, "ng:math:matrix_69": 4752, "ng:math:function_69": 4753, "ng:math:topology_69": 4754, "ng:math:category_69": 4755, "ng:math:lambda_69": 4756, "ng:math:sum_70": 4757, "ng:math:integral_70": 4758, "ng:math:tensor_70": 4759, "ng:math:matrix_70": 4760, "ng:math:function_70": 4761, "ng:math:topology_70": 4762, "ng:math:category_70": 4763, "ng:math:lambda_70": 4764, "ng:math:sum_71": 4765, "ng:math:integral_71": 4766, "ng:math:tensor_71": 4767, "ng:math:matrix_71": 4768, "ng:math:function_71": 4769, "ng:math:topology_71": 4770, "ng:math:category_71": 4771, "ng:math:lambda_71": 4772, "ng:math:sum_72": 4773, "ng:math:integral_72": 4774, "ng:math:tensor_72": 4775, "ng:math:matrix_72": 4776, "ng:math:function_72": 4777, "ng:math:topology_72": 4778, "ng:math:category_72": 4779, "ng:math:lambda_72": 4780, "ng:math:sum_73": 4781, "ng:math:integral_73": 4782, "ng:math:tensor_73": 4783, "ng:math:matrix_73": 4784, "ng:math:function_73": 4785, "ng:math:topology_73": 4786, "ng:math:category_73": 4787, "ng:math:lambda_73": 4788, "ng:math:sum_74": 4789, "ng:math:integral_74": 4790, "ng:math:tensor_74": 4791, "ng:math:matrix_74": 4792, "ng:math:function_74": 4793, "ng:math:topology_74": 4794, "ng:math:category_74": 4795, "ng:math:lambda_74": 4796, "ng:math:sum_75": 4797, "ng:math:integral_75": 4798, "ng:math:tensor_75": 4799, "ng:math:matrix_75": 4800, "ng:math:function_75": 4801, "ng:math:topology_75": 4802, "ng:math:category_75": 4803, "ng:math:lambda_75": 4804, "ng:math:sum_76": 4805, "ng:math:integral_76": 4806, "ng:math:tensor_76": 4807, "ng:math:matrix_76": 4808, "ng:math:function_76": 4809, "ng:math:topology_76": 4810, "ng:math:category_76": 4811, "ng:math:lambda_76": 4812, "ng:math:sum_77": 4813, "ng:math:integral_77": 4814, "ng:math:tensor_77": 4815, "ng:math:matrix_77": 4816, "ng:math:function_77": 4817, "ng:math:topology_77": 4818, "ng:math:category_77": 4819, "ng:math:lambda_77": 4820, "ng:math:sum_78": 4821, "ng:math:integral_78": 4822, "ng:math:tensor_78": 4823, "ng:math:matrix_78": 4824, "ng:math:function_78": 4825, "ng:math:topology_78": 4826, "ng:math:category_78": 4827, "ng:math:lambda_78": 4828, "ng:math:sum_79": 4829, "ng:math:integral_79": 4830, "ng:math:tensor_79": 4831, "ng:math:matrix_79": 4832, "ng:math:function_79": 4833, "ng:math:topology_79": 4834, "ng:math:category_79": 4835, "ng:math:lambda_79": 4836, "ng:math:sum_80": 4837, "ng:math:integral_80": 4838, "ng:math:tensor_80": 4839, "ng:math:matrix_80": 4840, "ng:math:function_80": 4841, "ng:math:topology_80": 4842, "ng:math:category_80": 4843, "ng:math:lambda_80": 4844, "ng:math:sum_81": 4845, "ng:math:integral_81": 4846, "ng:math:tensor_81": 4847, "ng:math:matrix_81": 4848, "ng:math:function_81": 4849, "ng:math:topology_81": 4850, "ng:math:category_81": 4851, "ng:math:lambda_81": 4852, "ng:math:sum_82": 4853, "ng:math:integral_82": 4854, "ng:math:tensor_82": 4855, "ng:math:matrix_82": 4856, "ng:math:function_82": 4857, "ng:math:topology_82": 4858, "ng:math:lambda_82": 4859, "ng:math:sum_83": 4860, "ng:math:integral_83": 4861, "ng:math:tensor_83": 4862, "ng:math:matrix_83": 4863, "ng:math:function_83": 4864, "ng:math:topology_83": 4865, "ng:math:category_83": 4866, "ng:math:lambda_83": 4867, "ng:math:sum_84": 4868, "ng:math:integral_84": 4869, "ng:math:tensor_84": 4870, "ng:math:matrix_84": 4871, "ng:math:function_84": 4872, "ng:math:topology_84": 4873, "ng:math:category_84": 4874, "ng:math:lambda_84": 4875, "ng:math:sum_85": 4876, "ng:math:integral_85": 4877, "ng:math:tensor_85": 4878, "ng:math:matrix_85": 4879, "ng:math:function_85": 4880, "ng:math:topology_85": 4881, "ng:math:category_85": 4882, "ng:math:lambda_85": 4883, "ng:math:sum_86": 4884, "ng:math:integral_86": 4885, "ng:math:tensor_86": 4886, "ng:math:matrix_86": 4887, "ng:math:function_86": 4888, "ng:math:topology_86": 4889, "ng:math:category_86": 4890, "ng:math:lambda_86": 4891, "ng:math:sum_87": 4892, "ng:math:integral_87": 4893, "ng:math:tensor_87": 4894, "ng:math:matrix_87": 4895, "ng:math:function_87": 4896, "ng:math:topology_87": 4897, "ng:math:category_87": 4898, "ng:math:lambda_87": 4899, "ℌ": 4900, "⏿": 4901, "↳": 4902, "⇅": 4903, "⎪": 4904, "⪮": 4905, "⪭": 4906, "⎩": 4907, "⊣": 4908, "⮰": 4909, "⪬": 4910, "➧": 4911, "⫶": 4912, "⏢": 4913, "⎣": 4914, "⊫": 4915, "⎫": 4916, "➩": 4917, "➯": 4918, "⥲": 4919, "⮌": 4920, "⮩": 4921, "⮫": 4922, "⮮": 4923, "⎤": 4924, "➣": 4925, "⎦": 4926, "❮": 4927, "➥": 4928, "➦": 4929, "⮥": 4930, "⮦": 4931, "⎨": 4932, "❰": 4933, "➨": 4934, "⮧": 4935, "⭽": 4936, "⧼": 4937, "⩩": 4938, "⮤": 4939, "◢": 4940, "◼": 4941, "▶": 4942, "■": 4943, "◥": 4944, "◒": 4945, "⦻": 4946, "⨐": 4947, "∱": 4948, "❌": 4949, "⋓": 4950, "⬇": 4951, "✉": 4952, "⨙": 4953, "◙": 4954, "⩚": 4955, "⨀": 4956, "✟": 4957, "⦆": 4958, "⨽": 4959, "⬃": 4960, "◧": 4961, "▥": 4962, "⫗": 4963, "⩅": 4964, "◸": 4965, "✑": 4966, "❔": 4967, "□": 4968, "✍": 4969, "⨟": 4970, "⦁": 4971, "⤀": 4972, "⭝": 4973, "⨮": 4974, "≹": 4975, "▨": 4976, "⭊": 4977, "▴": 4978, "⭂": 4979, "⤄": 4980, "◟": 4981, "◝": 4982, "◞": 4983, "◮": 4984, "▵": 4985, "⍍": 4986, "⍟": 4987, "⋈": 4988, "⠳": 4989, "⢅": 4990, "⡩": 4991, "⢉": 4992, "⣀": 4993, "∋": 4994, "⒐": 4995, "—": 4996, "⋟": 4998, "⋧": 4999, "∞": 5000, "Ꜳ": 5001, "ₔ": 5002, "𝛂": 5003, "𝐺": 5004, "𝑎": 5005, "⒙": 5006, "⒦": 5007, "⸩": 5008, "⋊": 5009, "⊿": 5010, "ↇ": 5011, "Ⅴ": 5012, "Ⅾ": 5013, "Ⅰ": 5014, "℠": 5015, "ⅼ": 5016, "⋤": 5017, "⊞": 5018, "≛": 5019, "⌯": 5020, "⅝": 5022, "⋿": 5023, "𝝘": 5024, "ꭖ": 5025, "ⅽ": 5026, "𝕒": 5027, "𝜁": 5028, "𝞽": 5029, "ꬾ": 5030, "𝛺": 5031, "ꭞ": 5032, "𝗗": 5033, "𝗰": 5034, "𝚎": 5035, "ꟳ": 5036, "𝞤": 5037, "ng:meta:reflection": 5038, "ng:meta:metaprogramming": 5039, "ng:meta:composition": 5040, "ng:meta:reflection_1": 5041, "ng:meta:composition_1": 5042, "ng:meta:introspection_2": 5043, "ng:meta:abstraction_2": 5044, "ng:meta:composition_2": 5045, "ng:meta:inheritance_2": 5046, "ng:meta:metaprogramming_3": 5047, "ng:meta:abstraction_3": 5048, "ng:meta:inheritance_3": 5049, "ng:meta:introspection_4": 5050, "ng:meta:metaprogramming_4": 5051, "ng:meta:composition_4": 5052, "ng:meta:inheritance_4": 5053, "ng:meta:polymorphism_4": 5054, "ng:meta:reflection_5": 5055, "ng:meta:metaprogramming_5": 5056, "ng:meta:inheritance_5": 5057, "ng:meta:reflection_6": 5058, "ng:meta:abstraction_6": 5059, "ng:meta:reflection_7": 5060, "ng:meta:introspection_7": 5061, "ng:meta:metaprogramming_7": 5062, "ng:meta:reflection_8": 5063, "ng:meta:composition_8": 5064, "ng:meta:inheritance_8": 5065, "ng:meta:reflection_9": 5066, "ng:meta:introspection_9": 5067, "ng:meta:metaprogramming_9": 5068, "ng:meta:reflection_10": 5069, "ng:meta:introspection_10": 5070, "ng:meta:inheritance_10": 5071, "ng:meta:encapsulation_10": 5072, "ng:meta:metaprogramming_11": 5073, "ng:meta:abstraction_11": 5074, "ng:meta:composition_11": 5075, "ng:meta:inheritance_11": 5076, "ng:meta:polymorphism_11": 5077, "ng:meta:encapsulation_11": 5078, "ng:meta:reflection_12": 5079, "ng:meta:composition_12": 5080, "ng:meta:reflection_13": 5081, "ng:meta:introspection_13": 5082, "ng:meta:metaprogramming_13": 5083, "ng:meta:abstraction_13": 5084, "ng:meta:composition_13": 5085, "ng:meta:reflection_14": 5086, "ng:meta:introspection_14": 5087, "ng:meta:abstraction_14": 5088, "ng:meta:composition_14": 5089, "ng:meta:inheritance_14": 5090, "ng:meta:polymorphism_14": 5091, "ng:meta:encapsulation_14": 5092, "ng:meta:reflection_15": 5093, "ng:meta:metaprogramming_15": 5094, "ng:meta:abstraction_15": 5095, "ng:meta:composition_15": 5096, "ng:meta:inheritance_15": 5097, "ng:meta:polymorphism_15": 5098, "ng:meta:reflection_16": 5099, "ng:meta:abstraction_16": 5100, "ng:meta:inheritance_16": 5101, "ng:meta:polymorphism_16": 5102, "ng:meta:encapsulation_16": 5103, "ng:meta:reflection_17": 5104, "ng:meta:metaprogramming_17": 5105, "ng:meta:abstraction_17": 5106, "ng:meta:inheritance_17": 5107, "ng:meta:reflection_18": 5108, "ng:meta:metaprogramming_18": 5109, "ng:meta:abstraction_18": 5110, "ng:meta:inheritance_18": 5111, "ng:meta:polymorphism_18": 5112, "ng:meta:encapsulation_18": 5113, "ng:meta:introspection_19": 5114, "ng:meta:metaprogramming_19": 5115, "ng:meta:abstraction_19": 5116, "ng:meta:inheritance_19": 5117, "ng:meta:encapsulation_19": 5118, "ng:meta:metaprogramming_20": 5119, "ng:meta:composition_20": 5120, "ng:meta:encapsulation_20": 5121, "ng:meta:reflection_21": 5122, "ng:meta:introspection_21": 5123, "ng:meta:abstraction_21": 5124, "ng:meta:composition_21": 5125, "ng:meta:polymorphism_21": 5126, "ng:meta:reflection_24": 5127, "ng:meta:introspection_24": 5128, "ng:meta:metaprogramming_24": 5129, "ng:meta:abstraction_24": 5130, "ng:meta:composition_24": 5131, "ng:meta:inheritance_24": 5132, "ng:meta:polymorphism_24": 5133, "ng:meta:encapsulation_24": 5134, "ng:meta:reflection_25": 5135, "ng:meta:introspection_25": 5136, "ng:meta:metaprogramming_25": 5137, "ng:meta:abstraction_25": 5138, "ng:meta:composition_25": 5139, "ng:meta:inheritance_25": 5140, "ng:meta:polymorphism_25": 5141, "ng:meta:encapsulation_25": 5142, "ng:meta:reflection_26": 5143, "ng:meta:introspection_26": 5144, "ng:meta:metaprogramming_26": 5145, "ng:meta:abstraction_26": 5146, "ng:meta:composition_26": 5147, "ng:meta:inheritance_26": 5148, "ng:meta:polymorphism_26": 5149, "ng:meta:encapsulation_26": 5150, "ng:meta:reflection_27": 5151, "ng:meta:introspection_27": 5152, "ng:meta:metaprogramming_27": 5153, "ng:meta:abstraction_27": 5154, "ng:meta:composition_27": 5155, "ng:meta:inheritance_27": 5156, "ng:meta:polymorphism_27": 5157, "ng:meta:encapsulation_27": 5158, "ng:meta:reflection_28": 5159, "ng:meta:introspection_28": 5160, "ng:meta:metaprogramming_28": 5161, "ng:meta:abstraction_28": 5162, "ng:meta:composition_28": 5163, "ng:meta:inheritance_28": 5164, "ng:meta:polymorphism_28": 5165, "ng:meta:encapsulation_28": 5166, "ng:meta:reflection_29": 5167, "ng:meta:introspection_29": 5168, "ng:meta:metaprogramming_29": 5169, "ng:meta:abstraction_29": 5170, "ng:meta:composition_29": 5171, "ng:meta:inheritance_29": 5172, "ng:meta:polymorphism_29": 5173, "ng:meta:encapsulation_29": 5174, "ng:meta:reflection_30": 5175, "ng:meta:introspection_30": 5176, "ng:meta:metaprogramming_30": 5177, "ng:meta:abstraction_30": 5178, "ng:meta:composition_30": 5179, "ng:meta:inheritance_30": 5180, "ng:meta:polymorphism_30": 5181, "ng:meta:encapsulation_30": 5182, "ng:meta:reflection_31": 5183, "ng:meta:introspection_31": 5184, "ng:meta:metaprogramming_31": 5185, "ng:meta:abstraction_31": 5186, "ng:meta:composition_31": 5187, "ng:meta:inheritance_31": 5188, "ng:meta:polymorphism_31": 5189, "ng:meta:encapsulation_31": 5190, "ng:meta:reflection_32": 5191, "ng:meta:introspection_32": 5192, "ng:meta:metaprogramming_32": 5193, "ng:meta:abstraction_32": 5194, "ng:meta:composition_32": 5195, "ng:meta:inheritance_32": 5196, "ng:meta:polymorphism_32": 5197, "ng:meta:encapsulation_32": 5198, "ng:meta:reflection_33": 5199, "ng:meta:introspection_33": 5200, "ng:meta:metaprogramming_33": 5201, "ng:meta:abstraction_33": 5202, "ng:meta:composition_33": 5203, "ng:meta:inheritance_33": 5204, "ng:meta:polymorphism_33": 5205, "ng:meta:encapsulation_33": 5206, "ng:meta:reflection_34": 5207, "ng:meta:introspection_34": 5208, "ng:meta:metaprogramming_34": 5209, "ng:meta:abstraction_34": 5210, "ng:meta:composition_34": 5211, "ng:meta:inheritance_34": 5212, "ng:meta:polymorphism_34": 5213, "ng:meta:encapsulation_34": 5214, "ng:meta:reflection_35": 5215, "ng:meta:introspection_35": 5216, "ng:meta:metaprogramming_35": 5217, "ng:meta:abstraction_35": 5218, "ng:meta:composition_35": 5219, "ng:meta:inheritance_35": 5220, "ng:meta:polymorphism_35": 5221, "ng:meta:encapsulation_35": 5222, "ng:meta:reflection_36": 5223, "ng:meta:introspection_36": 5224, "ng:meta:metaprogramming_36": 5225, "ng:meta:abstraction_36": 5226, "ng:meta:composition_36": 5227, "ng:meta:inheritance_36": 5228, "ng:meta:polymorphism_36": 5229, "ng:meta:encapsulation_36": 5230, "ng:meta:reflection_37": 5231, "ng:meta:introspection_37": 5232, "ng:meta:metaprogramming_37": 5233, "ng:meta:abstraction_37": 5234, "ng:meta:composition_37": 5235, "ng:meta:inheritance_37": 5236, "ng:meta:polymorphism_37": 5237, "ng:meta:encapsulation_37": 5238, "ng:meta:reflection_38": 5239, "ng:meta:introspection_38": 5240, "ng:meta:metaprogramming_38": 5241, "ng:meta:abstraction_38": 5242, "ng:meta:composition_38": 5243, "ng:meta:inheritance_38": 5244, "ng:meta:polymorphism_38": 5245, "ng:meta:encapsulation_38": 5246, "ng:meta:reflection_39": 5247, "ng:meta:introspection_39": 5248, "ng:meta:metaprogramming_39": 5249, "ng:meta:abstraction_39": 5250, "ng:meta:composition_39": 5251, "ng:meta:inheritance_39": 5252, "ng:meta:polymorphism_39": 5253, "ng:meta:encapsulation_39": 5254, "ng:meta:reflection_40": 5255, "ng:meta:introspection_40": 5256, "ng:meta:metaprogramming_40": 5257, "ng:meta:abstraction_40": 5258, "ng:meta:composition_40": 5259, "ng:meta:inheritance_40": 5260, "ng:meta:polymorphism_40": 5261, "ng:meta:encapsulation_40": 5262, "ng:meta:reflection_41": 5263, "ng:meta:introspection_41": 5264, "ng:meta:metaprogramming_41": 5265, "ng:meta:composition_41": 5266, "ng:meta:inheritance_41": 5267, "ng:meta:polymorphism_41": 5268, "ng:meta:encapsulation_41": 5269, "ng:meta:reflection_42": 5270, "ng:meta:introspection_42": 5271, "ng:meta:metaprogramming_42": 5272, "ng:meta:abstraction_42": 5273, "ng:meta:composition_42": 5274, "ng:meta:inheritance_42": 5275, "ng:meta:polymorphism_42": 5276, "ng:meta:encapsulation_42": 5277, "ng:meta:reflection_43": 5278, "ng:meta:introspection_43": 5279, "ng:meta:metaprogramming_43": 5280, "ng:meta:abstraction_43": 5281, "ng:meta:composition_43": 5282, "ng:meta:inheritance_43": 5283, "ng:meta:polymorphism_43": 5284, "ng:meta:encapsulation_43": 5285, "ng:meta:reflection_44": 5286, "ng:meta:introspection_44": 5287, "ng:meta:metaprogramming_44": 5288, "ng:meta:abstraction_44": 5289, "ng:meta:composition_44": 5290, "ng:meta:inheritance_44": 5291, "ng:meta:polymorphism_44": 5292, "ng:meta:encapsulation_44": 5293, "ng:meta:reflection_45": 5294, "ng:meta:introspection_45": 5295, "ng:meta:metaprogramming_45": 5296, "ng:meta:abstraction_45": 5297, "ng:meta:composition_45": 5298, "ng:meta:inheritance_45": 5299, "ng:meta:polymorphism_45": 5300, "ng:meta:encapsulation_45": 5301, "ng:meta:reflection_46": 5302, "ng:meta:introspection_46": 5303, "ng:meta:metaprogramming_46": 5304, "ng:meta:abstraction_46": 5305, "ng:meta:composition_46": 5306, "ng:meta:inheritance_46": 5307, "ng:meta:polymorphism_46": 5308, "ng:meta:encapsulation_46": 5309, "ng:meta:reflection_47": 5310, "ng:meta:introspection_47": 5311, "ng:meta:metaprogramming_47": 5312, "ng:meta:abstraction_47": 5313, "ng:meta:composition_47": 5314, "ng:meta:inheritance_47": 5315, "ng:meta:polymorphism_47": 5316, "ng:meta:encapsulation_47": 5317, "ng:meta:reflection_48": 5318, "ng:meta:introspection_48": 5319, "ng:meta:metaprogramming_48": 5320, "ng:meta:abstraction_48": 5321, "ng:meta:composition_48": 5322, "ng:meta:inheritance_48": 5323, "ng:meta:polymorphism_48": 5324, "ng:meta:encapsulation_48": 5325, "ng:meta:reflection_49": 5326, "ng:meta:introspection_49": 5327, "ng:meta:metaprogramming_49": 5328, "ng:meta:abstraction_49": 5329, "ng:meta:composition_49": 5330, "ng:meta:inheritance_49": 5331, "ng:meta:polymorphism_49": 5332, "ng:meta:encapsulation_49": 5333, "ng:meta:reflection_50": 5334, "ng:meta:introspection_50": 5335, "ng:meta:metaprogramming_50": 5336, "ng:meta:abstraction_50": 5337, "ng:meta:composition_50": 5338, "ng:meta:inheritance_50": 5339, "ng:meta:polymorphism_50": 5340, "ng:meta:encapsulation_50": 5341, "ng:meta:reflection_51": 5342, "ng:meta:introspection_51": 5343, "ng:meta:metaprogramming_51": 5344, "ng:meta:abstraction_51": 5345, "ng:meta:composition_51": 5346, "ng:meta:inheritance_51": 5347, "ng:meta:polymorphism_51": 5348, "ng:meta:encapsulation_51": 5349, "ng:meta:reflection_52": 5350, "ng:meta:introspection_52": 5351, "ng:meta:metaprogramming_52": 5352, "ng:meta:abstraction_52": 5353, "ng:meta:composition_52": 5354, "ng:meta:inheritance_52": 5355, "ng:meta:polymorphism_52": 5356, "ng:meta:encapsulation_52": 5357, "ng:meta:reflection_53": 5358, "ng:meta:introspection_53": 5359, "ng:meta:metaprogramming_53": 5360, "ng:meta:abstraction_53": 5361, "ng:meta:composition_53": 5362, "ng:meta:inheritance_53": 5363, "ng:meta:polymorphism_53": 5364, "ng:meta:encapsulation_53": 5365, "ng:meta:reflection_54": 5366, "ng:meta:introspection_54": 5367, "ng:meta:metaprogramming_54": 5368, "ng:meta:abstraction_54": 5369, "ng:meta:composition_54": 5370, "ng:meta:inheritance_54": 5371, "ng:meta:polymorphism_54": 5372, "ng:meta:encapsulation_54": 5373, "ng:meta:reflection_55": 5374, "ng:meta:introspection_55": 5375, "ng:meta:metaprogramming_55": 5376, "ng:meta:abstraction_55": 5377, "ng:meta:composition_55": 5378, "ng:meta:inheritance_55": 5379, "ng:meta:polymorphism_55": 5380, "ng:meta:encapsulation_55": 5381, "ng:meta:reflection_56": 5382, "ng:meta:introspection_56": 5383, "ng:meta:metaprogramming_56": 5384, "ng:meta:abstraction_56": 5385, "ng:meta:composition_56": 5386, "ng:meta:inheritance_56": 5387, "ng:meta:polymorphism_56": 5388, "ng:meta:encapsulation_56": 5389, "ng:meta:reflection_57": 5390, "ng:meta:introspection_57": 5391, "ng:meta:metaprogramming_57": 5392, "ng:meta:abstraction_57": 5393, "ng:meta:composition_57": 5394, "ng:meta:inheritance_57": 5395, "ng:meta:polymorphism_57": 5396, "ng:meta:encapsulation_57": 5397, "ng:meta:reflection_58": 5398, "ng:meta:introspection_58": 5399, "ng:meta:metaprogramming_58": 5400, "ng:meta:abstraction_58": 5401, "ng:meta:composition_58": 5402, "ng:meta:inheritance_58": 5403, "ng:meta:polymorphism_58": 5404, "ng:meta:encapsulation_58": 5405, "ng:meta:reflection_59": 5406, "ng:meta:introspection_59": 5407, "ng:meta:metaprogramming_59": 5408, "ng:meta:abstraction_59": 5409, "ng:meta:composition_59": 5410, "ng:meta:inheritance_59": 5411, "ng:meta:polymorphism_59": 5412, "ng:meta:encapsulation_59": 5413, "ng:meta:reflection_60": 5414, "ng:meta:introspection_60": 5415, "ng:meta:metaprogramming_60": 5416, "ng:meta:abstraction_60": 5417, "ng:meta:composition_60": 5418, "ng:meta:inheritance_60": 5419, "ng:meta:polymorphism_60": 5420, "ng:meta:encapsulation_60": 5421, "ng:meta:reflection_61": 5422, "ng:meta:introspection_61": 5423, "ng:meta:metaprogramming_61": 5424, "ng:meta:abstraction_61": 5425, "ng:meta:composition_61": 5426, "ng:meta:inheritance_61": 5427, "ng:meta:polymorphism_61": 5428, "ng:meta:encapsulation_61": 5429, "ng:meta:reflection_62": 5430, "ng:meta:introspection_62": 5431, "ng:meta:metaprogramming_62": 5432, "ng:meta:abstraction_62": 5433, "ng:meta:composition_62": 5434, "ng:meta:inheritance_62": 5435, "ng:meta:polymorphism_62": 5436, "ng:meta:encapsulation_62": 5437, "ng:meta:reflection_63": 5438, "ng:meta:introspection_63": 5439, "ng:meta:metaprogramming_63": 5440, "ng:meta:abstraction_63": 5441, "ng:meta:composition_63": 5442, "ng:meta:inheritance_63": 5443, "ng:meta:polymorphism_63": 5444, "ng:meta:encapsulation_63": 5445, "ng:meta:reflection_64": 5446, "ng:meta:introspection_64": 5447, "ng:meta:metaprogramming_64": 5448, "ng:meta:abstraction_64": 5449, "ng:meta:composition_64": 5450, "ng:meta:inheritance_64": 5451, "ng:meta:polymorphism_64": 5452, "ng:meta:encapsulation_64": 5453, "ng:meta:reflection_65": 5454, "ng:meta:introspection_65": 5455, "ng:meta:metaprogramming_65": 5456, "ng:meta:abstraction_65": 5457, "ng:meta:composition_65": 5458, "ng:meta:inheritance_65": 5459, "ng:meta:polymorphism_65": 5460, "ng:meta:encapsulation_65": 5461, "ng:meta:reflection_66": 5462, "ng:meta:introspection_66": 5463, "ng:meta:metaprogramming_66": 5464, "ng:meta:abstraction_66": 5465, "ng:meta:composition_66": 5466, "ng:meta:inheritance_66": 5467, "ng:meta:polymorphism_66": 5468, "ng:meta:encapsulation_66": 5469, "ng:meta:reflection_67": 5470, "ng:meta:introspection_67": 5471, "ng:meta:metaprogramming_67": 5472, "ng:meta:abstraction_67": 5473, "ng:meta:composition_67": 5474, "ng:meta:inheritance_67": 5475, "ng:meta:polymorphism_67": 5476, "ng:meta:encapsulation_67": 5477, "ng:meta:reflection_68": 5478, "ng:meta:introspection_68": 5479, "ng:meta:metaprogramming_68": 5480, "ng:meta:abstraction_68": 5481, "ng:meta:composition_68": 5482, "ng:meta:inheritance_68": 5483, "ng:meta:polymorphism_68": 5484, "ng:meta:encapsulation_68": 5485, "ng:meta:reflection_69": 5486, "ng:meta:introspection_69": 5487, "ng:meta:metaprogramming_69": 5488, "ng:meta:abstraction_69": 5489, "ng:meta:composition_69": 5490, "ng:meta:inheritance_69": 5491, "ng:meta:polymorphism_69": 5492, "ng:meta:encapsulation_69": 5493, "ng:meta:reflection_70": 5494, "ng:meta:introspection_70": 5495, "ng:meta:metaprogramming_70": 5496, "ng:meta:abstraction_70": 5497, "ng:meta:composition_70": 5498, "ng:meta:inheritance_70": 5499, "ng:meta:polymorphism_70": 5500, "ng:meta:encapsulation_70": 5501, "ng:meta:reflection_71": 5502, "ng:meta:introspection_71": 5503, "ng:meta:metaprogramming_71": 5504, "ng:meta:abstraction_71": 5505, "ng:meta:composition_71": 5506, "ng:meta:inheritance_71": 5507, "ng:meta:polymorphism_71": 5508, "ng:meta:encapsulation_71": 5509, "ng:meta:reflection_72": 5510, "ng:meta:introspection_72": 5511, "ng:meta:metaprogramming_72": 5512, "ng:meta:abstraction_72": 5513, "ng:meta:composition_72": 5514, "ng:meta:inheritance_72": 5515, "ng:meta:polymorphism_72": 5516, "ng:meta:encapsulation_72": 5517, "ng:meta:reflection_73": 5518, "ng:meta:introspection_73": 5519, "ng:meta:metaprogramming_73": 5520, "ng:meta:abstraction_73": 5521, "ng:meta:composition_73": 5522, "ng:meta:inheritance_73": 5523, "ng:meta:polymorphism_73": 5524, "ng:meta:encapsulation_73": 5525, "ng:meta:reflection_74": 5526, "ng:meta:introspection_74": 5527, "ng:meta:metaprogramming_74": 5528, "ng:meta:abstraction_74": 5529, "ng:meta:composition_74": 5530, "ng:meta:inheritance_74": 5531, "ng:meta:polymorphism_74": 5532, "ng:meta:encapsulation_74": 5533, "ng:meta:reflection_75": 5534, "ng:meta:introspection_75": 5535, "ng:meta:metaprogramming_75": 5536, "ng:meta:abstraction_75": 5537, "ng:meta:composition_75": 5538, "ng:meta:inheritance_75": 5539, "ng:meta:polymorphism_75": 5540, "ng:meta:encapsulation_75": 5541, "ng:meta:reflection_76": 5542, "ng:meta:introspection_76": 5543, "ng:meta:metaprogramming_76": 5544, "ng:meta:abstraction_76": 5545, "ng:meta:composition_76": 5546, "ng:meta:inheritance_76": 5547, "ng:meta:polymorphism_76": 5548, "ng:meta:encapsulation_76": 5549, "ng:meta:reflection_77": 5550, "ng:meta:introspection_77": 5551, "ng:meta:metaprogramming_77": 5552, "ng:meta:abstraction_77": 5553, "ng:meta:composition_77": 5554, "ng:meta:inheritance_77": 5555, "ng:meta:polymorphism_77": 5556, "ng:meta:encapsulation_77": 5557, "ng:meta:reflection_78": 5558, "ng:meta:introspection_78": 5559, "ng:meta:metaprogramming_78": 5560, "ng:meta:abstraction_78": 5561, "ng:meta:composition_78": 5562, "ng:meta:inheritance_78": 5563, "ng:meta:polymorphism_78": 5564, "ng:meta:encapsulation_78": 5565, "ng:meta:reflection_79": 5566, "ng:meta:introspection_79": 5567, "ng:meta:metaprogramming_79": 5568, "ng:meta:abstraction_79": 5569, "ng:meta:composition_79": 5570, "ng:meta:inheritance_79": 5571, "ng:meta:polymorphism_79": 5572, "ng:meta:encapsulation_79": 5573, "ng:meta:reflection_80": 5574, "ng:meta:introspection_80": 5575, "ng:meta:metaprogramming_80": 5576, "ng:meta:abstraction_80": 5577, "ng:meta:composition_80": 5578, "ng:meta:inheritance_80": 5579, "ng:meta:polymorphism_80": 5580, "ng:meta:encapsulation_80": 5581, "ng:meta:reflection_81": 5582, "ng:meta:introspection_81": 5583, "ng:meta:metaprogramming_81": 5584, "ng:meta:abstraction_81": 5585, "ng:meta:composition_81": 5586, "ng:meta:inheritance_81": 5587, "ng:meta:polymorphism_81": 5588, "ng:meta:encapsulation_81": 5589, "ng:meta:reflection_82": 5590, "ng:meta:introspection_82": 5591, "ng:meta:metaprogramming_82": 5592, "ng:meta:abstraction_82": 5593, "ng:meta:composition_82": 5594, "ng:meta:inheritance_82": 5595, "ng:meta:polymorphism_82": 5596, "ng:meta:encapsulation_82": 5597, "ng:meta:reflection_83": 5598, "ng:meta:introspection_83": 5599, "ng:meta:metaprogramming_83": 5600, "ng:meta:abstraction_83": 5601, "ng:meta:composition_83": 5602, "ng:meta:inheritance_83": 5603, "ng:meta:polymorphism_83": 5604, "ng:meta:encapsulation_83": 5605, "ng:meta:reflection_84": 5606, "ng:meta:introspection_84": 5607, "ng:meta:metaprogramming_84": 5608, "ng:meta:abstraction_84": 5609, "ng:meta:composition_84": 5610, "ng:meta:inheritance_84": 5611, "ng:meta:polymorphism_84": 5612, "ng:meta:encapsulation_84": 5613, "ng:meta:reflection_85": 5614, "ng:meta:introspection_85": 5615, "ng:meta:metaprogramming_85": 5616, "ng:meta:abstraction_85": 5617, "ng:meta:composition_85": 5618, "ng:meta:inheritance_85": 5619, "ng:meta:polymorphism_85": 5620, "ng:meta:encapsulation_85": 5621, "ng:meta:reflection_86": 5622, "ng:meta:introspection_86": 5623, "ng:meta:metaprogramming_86": 5624, "ng:meta:abstraction_86": 5625, "ng:meta:composition_86": 5626, "ng:meta:inheritance_86": 5627, "ng:meta:polymorphism_86": 5628, "ng:meta:encapsulation_86": 5629, "𝟈": 5630, "𝜬": 5631, "𝘆": 5632, "𝒒": 5633, "𝙺": 5634, "𝟆": 5635, "≌": 5636, "❖": 5637, "⬬": 5638, "⧑": 5639, "⩌": 5640, "⬘": 5641, "⬚": 5642, "⩏": 5643, "❈": 5644, "⯚": 5645, "⩟": 5646, "❎": 5647, "⤣": 5648, "⧛": 5649, "⭏": 5650, "⭎": 5651, "⧈": 5652, "⧋": 5653, "✌": 5654, "⥋": 5655, "⥒": 5656, "⨷": 5657, "⩣": 5658, "⭍": 5659, "✽": 5660, "⥅": 5661, "⥌": 5662, "⦭": 5663, "⧣": 5664, "⩋": 5665, "⩙": 5666, "⍏": 5667, "⤔": 5668, "⧏": 5669, "⍢": 5670, "❒": 5671, "❢": 5672, "❥": 5673, "⤡": 5674, "⧥": 5675, "⩝": 5676, "⍎": 5677, "❁": 5678, "⮘": 5679, "⮜": 5680, "ℷ": 5681, "↖": 5682, "ℓ": 5683, "↨": 5684, "↺": 5685, "⇌": 5686, "⏦": 5687, "⍬": 5688, "⏣": 5689, "⧭": 5690, "⫪": 5691, "⫫": 5692, "⯰": 5693, "⯱": 5694, "⏥": 5695, "⏳": 5696, "⯣": 5697, "⮺": 5698, "⯧": 5699, "⯩": 5700, "⯲": 5701, "⫧": 5702, "⩬": 5703, "⯪": 5704, "⯫": 5705, "⏲": 5706, "⫷": 5707, "⫣": 5708, "⫤": 5709, "⯬": 5710, "⯭": 5711, "⯹": 5712, "⫹": 5713, "❭": 5714, "⥭": 5715, "⩫": 5716, "⭫": 5717, "⏶": 5718, "⫥": 5719, "⫭": 5720, "⏯": 5721, "⫰": 5722, "⍭": 5723, "⭪": 5724, "⭬": 5725, "⏭": 5726, "ℵ": 5727, "⅀": 5728, "↦": 5729, "⇮": 5730, "∗": 5731, "●": 5732, "✒": 5733, "✀": 5734, "▮": 5735, "∌": 5736, "≝": 5737, "∀": 5738, "✖": 5739, "⨜": 5740, "◯": 5741, "⌈": 5742, "⤛": 5743, "◺": 5744, "◿": 5745, "≞": 5746, "⦝": 5747, "⋂": 5748, "⨉": 5749, "⨃": 5750, "⬉": 5751, "⌖": 5752, "⨖": 5753, "✋": 5754, "⧙": 5755, "⤍": 5756, "⤏": 5757, "⨳": 5758, "⬓": 5759, "▤": 5760, "◨": 5761, "⧇": 5762, "⌕": 5763, "⤺": 5764, "⧊": 5765, "⬍": 5766, "◠": 5767, "▽": 5768, "▭": 5769, "▻": 5770, "△": 5771, "⦂": 5772, "⨄": 5773, "◴": 5774, "⌸": 5775, "⋲": 5776, "⨾": 5777, "⤉": 5778, "⤗": 5779, "⭇": 5780, "⦬": 5781, "◜": 5782, "✸": 5783, "▢": 5784, "◫": 5785, "◱": 5786, "⤂": 5787, "⨅": 5788, "⦊": 5789, "⤸": 5790, "▹": 5791, "◶": 5792, "⦔": 5793, "⨵": 5794, "❋": 5795, "⬔": 5796, "◬": 5797, "⥄": 5798, "℁": 5799, "⏰": 5800, "≊": 5801, "Å": 5802, "⍕": 5803, "⍡": 5804, "③": 5805, "⊬": 5806, "↲": 5807, "⇊": 5808, "₠": 5809, "⌢": 5810, "‧": 5811, "∆": 5812, "↫": 5813, "⊍": 5814, "∐": 5815, "≵": 5816, "↸": 5817, "⒤": 5818, "₱": 5819, "≺": 5820, "⁆": 5821, "→": 5822, "Ⅺ": 5823, "∖": 5824, "⋼": 5825, "⋷": 5826, "ⅿ": 5827, "⊆": 5828, "₸": 5829, "⅐": 5831, "⅜": 5832, "⊻": 5833, "ng:performance:optimization": 5834, "ng:performance:caching": 5835, "ng:performance:parallelization": 5836, "ng:performance:caching_1": 5837, "ng:performance:parallelization_2": 5838, "ng:performance:benchmarking_3": 5839, "ng:performance:scaling_3": 5840, "ng:performance:tuning_3": 5841, "ng:performance:scaling_4": 5842, "ng:performance:tuning_4": 5843, "ng:performance:optimization_5": 5844, "ng:performance:vectorization_5": 5845, "ng:performance:benchmarking_5": 5846, "ng:performance:tuning_5": 5847, "ng:performance:optimization_6": 5848, "ng:performance:caching_6": 5849, "ng:performance:parallelization_6": 5850, "ng:performance:vectorization_6": 5851, "ng:performance:profiling_6": 5852, "ng:performance:benchmarking_6": 5853, "ng:performance:scaling_6": 5854, "ng:performance:tuning_6": 5855, "ng:performance:optimization_7": 5856, "ng:performance:vectorization_7": 5857, "ng:performance:scaling_7": 5858, "ng:performance:optimization_8": 5859, "ng:performance:parallelization_8": 5860, "ng:performance:vectorization_8": 5861, "ng:performance:benchmarking_8": 5862, "ng:performance:caching_9": 5863, "ng:performance:tuning_9": 5864, "ng:performance:caching_10": 5865, "ng:performance:parallelization_10": 5866, "ng:performance:profiling_10": 5867, "ng:performance:caching_11": 5868, "ng:performance:vectorization_11": 5869, "ng:performance:scaling_11": 5870, "ng:performance:tuning_11": 5871, "ng:performance:optimization_12": 5872, "ng:performance:benchmarking_12": 5873, "ng:performance:optimization_13": 5874, "ng:performance:benchmarking_13": 5875, "ng:performance:tuning_13": 5876, "ng:performance:parallelization_14": 5877, "ng:performance:profiling_14": 5878, "ng:performance:benchmarking_14": 5879, "ng:performance:tuning_14": 5880, "ng:performance:optimization_15": 5881, "ng:performance:caching_15": 5882, "ng:performance:parallelization_15": 5883, "ng:performance:scaling_15": 5884, "ng:performance:optimization_16": 5885, "ng:performance:caching_16": 5886, "ng:performance:parallelization_16": 5887, "ng:performance:vectorization_16": 5888, "ng:performance:profiling_16": 5889, "ng:performance:scaling_16": 5890, "ng:performance:optimization_17": 5891, "ng:performance:caching_17": 5892, "ng:performance:parallelization_17": 5893, "ng:performance:vectorization_17": 5894, "ng:performance:benchmarking_17": 5895, "ng:performance:tuning_17": 5896, "ng:performance:optimization_18": 5897, "ng:performance:parallelization_18": 5898, "ng:performance:profiling_18": 5899, "ng:performance:benchmarking_18": 5900, "ng:performance:scaling_18": 5901, "ng:performance:caching_19": 5902, "ng:performance:vectorization_19": 5903, "ng:performance:benchmarking_19": 5904, "ng:performance:scaling_19": 5905, "ng:performance:optimization_20": 5906, "ng:performance:parallelization_20": 5907, "ng:performance:benchmarking_20": 5908, "ng:performance:scaling_20": 5909, "ng:performance:optimization_21": 5910, "ng:performance:caching_21": 5911, "ng:performance:vectorization_21": 5912, "ng:performance:benchmarking_21": 5913, "ng:performance:scaling_21": 5914, "ng:performance:tuning_21": 5915, "ng:performance:benchmarking_23": 5916, "ng:performance:optimization_24": 5917, "ng:performance:caching_24": 5918, "ng:performance:parallelization_24": 5919, "ng:performance:vectorization_24": 5920, "ng:performance:profiling_24": 5921, "ng:performance:benchmarking_24": 5922, "ng:performance:scaling_24": 5923, "ng:performance:tuning_24": 5924, "ng:performance:optimization_25": 5925, "ng:performance:caching_25": 5926, "ng:performance:parallelization_25": 5927, "ng:performance:vectorization_25": 5928, "ng:performance:profiling_25": 5929, "ng:performance:benchmarking_25": 5930, "ng:performance:scaling_25": 5931, "ng:performance:tuning_25": 5932, "ng:performance:optimization_26": 5933, "ng:performance:caching_26": 5934, "ng:performance:parallelization_26": 5935, "ng:performance:vectorization_26": 5936, "ng:performance:profiling_26": 5937, "ng:performance:benchmarking_26": 5938, "ng:performance:scaling_26": 5939, "ng:performance:tuning_26": 5940, "ng:performance:optimization_27": 5941, "ng:performance:caching_27": 5942, "ng:performance:parallelization_27": 5943, "ng:performance:vectorization_27": 5944, "ng:performance:benchmarking_27": 5945, "ng:performance:scaling_27": 5946, "ng:performance:tuning_27": 5947, "ng:performance:optimization_28": 5948, "ng:performance:caching_28": 5949, "ng:performance:parallelization_28": 5950, "ng:performance:vectorization_28": 5951, "ng:performance:profiling_28": 5952, "ng:performance:benchmarking_28": 5953, "ng:performance:scaling_28": 5954, "ng:performance:tuning_28": 5955, "ng:performance:optimization_29": 5956, "ng:performance:caching_29": 5957, "ng:performance:parallelization_29": 5958, "ng:performance:vectorization_29": 5959, "ng:performance:profiling_29": 5960, "ng:performance:benchmarking_29": 5961, "ng:performance:scaling_29": 5962, "ng:performance:tuning_29": 5963, "ng:performance:optimization_30": 5964, "ng:performance:caching_30": 5965, "ng:performance:parallelization_30": 5966, "ng:performance:vectorization_30": 5967, "ng:performance:profiling_30": 5968, "ng:performance:benchmarking_30": 5969, "ng:performance:scaling_30": 5970, "ng:performance:tuning_30": 5971, "ng:performance:optimization_31": 5972, "ng:performance:caching_31": 5973, "ng:performance:parallelization_31": 5974, "ng:performance:vectorization_31": 5975, "ng:performance:profiling_31": 5976, "ng:performance:benchmarking_31": 5977, "ng:performance:scaling_31": 5978, "ng:performance:tuning_31": 5979, "ng:performance:optimization_32": 5980, "ng:performance:caching_32": 5981, "ng:performance:parallelization_32": 5982, "ng:performance:vectorization_32": 5983, "ng:performance:profiling_32": 5984, "ng:performance:benchmarking_32": 5985, "ng:performance:scaling_32": 5986, "ng:performance:tuning_32": 5987, "ng:performance:optimization_33": 5988, "ng:performance:caching_33": 5989, "ng:performance:parallelization_33": 5990, "ng:performance:vectorization_33": 5991, "ng:performance:profiling_33": 5992, "ng:performance:benchmarking_33": 5993, "ng:performance:scaling_33": 5994, "ng:performance:optimization_34": 5995, "ng:performance:caching_34": 5996, "ng:performance:parallelization_34": 5997, "ng:performance:vectorization_34": 5998, "ng:performance:profiling_34": 5999, "ng:performance:benchmarking_34": 6000, "ng:performance:scaling_34": 6001, "ng:performance:tuning_34": 6002, "ng:performance:optimization_35": 6003, "ng:performance:caching_35": 6004, "ng:performance:parallelization_35": 6005, "ng:performance:vectorization_35": 6006, "ng:performance:profiling_35": 6007, "ng:performance:benchmarking_35": 6008, "ng:performance:scaling_35": 6009, "ng:performance:tuning_35": 6010, "ng:performance:optimization_36": 6011, "ng:performance:caching_36": 6012, "ng:performance:parallelization_36": 6013, "ng:performance:vectorization_36": 6014, "ng:performance:profiling_36": 6015, "ng:performance:benchmarking_36": 6016, "ng:performance:scaling_36": 6017, "ng:performance:tuning_36": 6018, "ng:performance:optimization_37": 6019, "ng:performance:caching_37": 6020, "ng:performance:parallelization_37": 6021, "ng:performance:vectorization_37": 6022, "ng:performance:profiling_37": 6023, "ng:performance:benchmarking_37": 6024, "ng:performance:scaling_37": 6025, "ng:performance:tuning_37": 6026, "ng:performance:optimization_38": 6027, "ng:performance:caching_38": 6028, "ng:performance:parallelization_38": 6029, "ng:performance:vectorization_38": 6030, "ng:performance:profiling_38": 6031, "ng:performance:benchmarking_38": 6032, "ng:performance:scaling_38": 6033, "ng:performance:tuning_38": 6034, "ng:performance:optimization_39": 6035, "ng:performance:caching_39": 6036, "ng:performance:parallelization_39": 6037, "ng:performance:vectorization_39": 6038, "ng:performance:profiling_39": 6039, "ng:performance:scaling_39": 6040, "ng:performance:tuning_39": 6041, "ng:performance:optimization_40": 6042, "ng:performance:caching_40": 6043, "ng:performance:parallelization_40": 6044, "ng:performance:vectorization_40": 6045, "ng:performance:profiling_40": 6046, "ng:performance:benchmarking_40": 6047, "ng:performance:scaling_40": 6048, "ng:performance:tuning_40": 6049, "ng:performance:optimization_41": 6050, "ng:performance:caching_41": 6051, "ng:performance:parallelization_41": 6052, "ng:performance:vectorization_41": 6053, "ng:performance:profiling_41": 6054, "ng:performance:benchmarking_41": 6055, "ng:performance:scaling_41": 6056, "ng:performance:tuning_41": 6057, "ng:performance:optimization_42": 6058, "ng:performance:caching_42": 6059, "ng:performance:parallelization_42": 6060, "ng:performance:vectorization_42": 6061, "ng:performance:profiling_42": 6062, "ng:performance:benchmarking_42": 6063, "ng:performance:scaling_42": 6064, "ng:performance:tuning_42": 6065, "ng:performance:optimization_43": 6066, "ng:performance:caching_43": 6067, "ng:performance:parallelization_43": 6068, "ng:performance:vectorization_43": 6069, "ng:performance:profiling_43": 6070, "ng:performance:benchmarking_43": 6071, "ng:performance:scaling_43": 6072, "ng:performance:tuning_43": 6073, "ng:performance:optimization_44": 6074, "ng:performance:caching_44": 6075, "ng:performance:parallelization_44": 6076, "ng:performance:vectorization_44": 6077, "ng:performance:profiling_44": 6078, "ng:performance:benchmarking_44": 6079, "ng:performance:scaling_44": 6080, "ng:performance:tuning_44": 6081, "ng:performance:optimization_45": 6082, "ng:performance:caching_45": 6083, "ng:performance:parallelization_45": 6084, "ng:performance:vectorization_45": 6085, "ng:performance:profiling_45": 6086, "ng:performance:benchmarking_45": 6087, "ng:performance:scaling_45": 6088, "ng:performance:tuning_45": 6089, "ng:performance:optimization_46": 6090, "ng:performance:caching_46": 6091, "ng:performance:vectorization_46": 6092, "ng:performance:profiling_46": 6093, "ng:performance:benchmarking_46": 6094, "ng:performance:scaling_46": 6095, "ng:performance:tuning_46": 6096, "ng:performance:optimization_47": 6097, "ng:performance:caching_47": 6098, "ng:performance:parallelization_47": 6099, "ng:performance:vectorization_47": 6100, "ng:performance:profiling_47": 6101, "ng:performance:benchmarking_47": 6102, "ng:performance:scaling_47": 6103, "ng:performance:tuning_47": 6104, "ng:performance:optimization_48": 6105, "ng:performance:caching_48": 6106, "ng:performance:parallelization_48": 6107, "ng:performance:vectorization_48": 6108, "ng:performance:profiling_48": 6109, "ng:performance:benchmarking_48": 6110, "ng:performance:scaling_48": 6111, "ng:performance:tuning_48": 6112, "ng:performance:optimization_49": 6113, "ng:performance:caching_49": 6114, "ng:performance:parallelization_49": 6115, "ng:performance:vectorization_49": 6116, "ng:performance:profiling_49": 6117, "ng:performance:benchmarking_49": 6118, "ng:performance:scaling_49": 6119, "ng:performance:tuning_49": 6120, "ng:performance:caching_50": 6121, "ng:performance:parallelization_50": 6122, "ng:performance:vectorization_50": 6123, "ng:performance:profiling_50": 6124, "ng:performance:benchmarking_50": 6125, "ng:performance:scaling_50": 6126, "ng:performance:tuning_50": 6127, "ng:performance:optimization_51": 6128, "ng:performance:caching_51": 6129, "ng:performance:parallelization_51": 6130, "ng:performance:vectorization_51": 6131, "ng:performance:profiling_51": 6132, "ng:performance:benchmarking_51": 6133, "ng:performance:scaling_51": 6134, "ng:performance:tuning_51": 6135, "ng:performance:optimization_52": 6136, "ng:performance:caching_52": 6137, "ng:performance:parallelization_52": 6138, "ng:performance:vectorization_52": 6139, "ng:performance:profiling_52": 6140, "ng:performance:benchmarking_52": 6141, "ng:performance:scaling_52": 6142, "ng:performance:tuning_52": 6143, "ng:performance:optimization_53": 6144, "ng:performance:caching_53": 6145, "ng:performance:parallelization_53": 6146, "ng:performance:vectorization_53": 6147, "ng:performance:profiling_53": 6148, "ng:performance:benchmarking_53": 6149, "ng:performance:scaling_53": 6150, "ng:performance:tuning_53": 6151, "ng:performance:optimization_54": 6152, "ng:performance:caching_54": 6153, "ng:performance:parallelization_54": 6154, "ng:performance:vectorization_54": 6155, "ng:performance:profiling_54": 6156, "ng:performance:benchmarking_54": 6157, "ng:performance:scaling_54": 6158, "ng:performance:tuning_54": 6159, "ng:performance:optimization_55": 6160, "ng:performance:caching_55": 6161, "ng:performance:parallelization_55": 6162, "ng:performance:vectorization_55": 6163, "ng:performance:profiling_55": 6164, "ng:performance:benchmarking_55": 6165, "ng:performance:scaling_55": 6166, "ng:performance:tuning_55": 6167, "ng:performance:optimization_56": 6168, "ng:performance:caching_56": 6169, "ng:performance:parallelization_56": 6170, "ng:performance:vectorization_56": 6171, "ng:performance:profiling_56": 6172, "ng:performance:benchmarking_56": 6173, "ng:performance:scaling_56": 6174, "ng:performance:tuning_56": 6175, "ng:performance:optimization_57": 6176, "ng:performance:caching_57": 6177, "ng:performance:parallelization_57": 6178, "ng:performance:vectorization_57": 6179, "ng:performance:profiling_57": 6180, "ng:performance:benchmarking_57": 6181, "ng:performance:scaling_57": 6182, "ng:performance:tuning_57": 6183, "ng:performance:optimization_58": 6184, "ng:performance:caching_58": 6185, "ng:performance:parallelization_58": 6186, "ng:performance:profiling_58": 6187, "ng:performance:benchmarking_58": 6188, "ng:performance:scaling_58": 6189, "ng:performance:tuning_58": 6190, "ng:performance:optimization_59": 6191, "ng:performance:caching_59": 6192, "ng:performance:parallelization_59": 6193, "ng:performance:vectorization_59": 6194, "ng:performance:profiling_59": 6195, "ng:performance:benchmarking_59": 6196, "ng:performance:scaling_59": 6197, "ng:performance:tuning_59": 6198, "ng:performance:optimization_60": 6199, "ng:performance:caching_60": 6200, "ng:performance:parallelization_60": 6201, "ng:performance:vectorization_60": 6202, "ng:performance:profiling_60": 6203, "ng:performance:benchmarking_60": 6204, "ng:performance:scaling_60": 6205, "ng:performance:tuning_60": 6206, "ng:performance:optimization_61": 6207, "ng:performance:caching_61": 6208, "ng:performance:parallelization_61": 6209, "ng:performance:vectorization_61": 6210, "ng:performance:profiling_61": 6211, "ng:performance:benchmarking_61": 6212, "ng:performance:scaling_61": 6213, "ng:performance:tuning_61": 6214, "ng:performance:optimization_62": 6215, "ng:performance:caching_62": 6216, "ng:performance:parallelization_62": 6217, "ng:performance:vectorization_62": 6218, "ng:performance:profiling_62": 6219, "ng:performance:benchmarking_62": 6220, "ng:performance:scaling_62": 6221, "ng:performance:tuning_62": 6222, "ng:performance:optimization_63": 6223, "ng:performance:caching_63": 6224, "ng:performance:parallelization_63": 6225, "ng:performance:vectorization_63": 6226, "ng:performance:profiling_63": 6227, "ng:performance:benchmarking_63": 6228, "ng:performance:scaling_63": 6229, "ng:performance:tuning_63": 6230, "ng:performance:optimization_64": 6231, "ng:performance:caching_64": 6232, "ng:performance:parallelization_64": 6233, "ng:performance:vectorization_64": 6234, "ng:performance:profiling_64": 6235, "ng:performance:benchmarking_64": 6236, "ng:performance:scaling_64": 6237, "ng:performance:tuning_64": 6238, "ng:performance:optimization_65": 6239, "ng:performance:caching_65": 6240, "ng:performance:parallelization_65": 6241, "ng:performance:vectorization_65": 6242, "ng:performance:profiling_65": 6243, "ng:performance:benchmarking_65": 6244, "ng:performance:scaling_65": 6245, "ng:performance:tuning_65": 6246, "ng:performance:optimization_66": 6247, "ng:performance:caching_66": 6248, "ng:performance:parallelization_66": 6249, "ng:performance:vectorization_66": 6250, "ng:performance:profiling_66": 6251, "ng:performance:benchmarking_66": 6252, "ng:performance:scaling_66": 6253, "ng:performance:tuning_66": 6254, "ng:performance:optimization_67": 6255, "ng:performance:caching_67": 6256, "ng:performance:parallelization_67": 6257, "ng:performance:vectorization_67": 6258, "ng:performance:profiling_67": 6259, "ng:performance:benchmarking_67": 6260, "ng:performance:scaling_67": 6261, "ng:performance:tuning_67": 6262, "ng:performance:optimization_68": 6263, "ng:performance:caching_68": 6264, "ng:performance:parallelization_68": 6265, "ng:performance:vectorization_68": 6266, "ng:performance:profiling_68": 6267, "ng:performance:benchmarking_68": 6268, "ng:performance:scaling_68": 6269, "ng:performance:tuning_68": 6270, "ng:performance:optimization_69": 6271, "ng:performance:caching_69": 6272, "ng:performance:parallelization_69": 6273, "ng:performance:vectorization_69": 6274, "ng:performance:profiling_69": 6275, "ng:performance:benchmarking_69": 6276, "ng:performance:scaling_69": 6277, "ng:performance:tuning_69": 6278, "ng:performance:optimization_70": 6279, "ng:performance:caching_70": 6280, "ng:performance:parallelization_70": 6281, "ng:performance:vectorization_70": 6282, "ng:performance:profiling_70": 6283, "ng:performance:benchmarking_70": 6284, "ng:performance:scaling_70": 6285, "ng:performance:tuning_70": 6286, "ng:performance:optimization_71": 6287, "ng:performance:caching_71": 6288, "ng:performance:parallelization_71": 6289, "ng:performance:vectorization_71": 6290, "ng:performance:profiling_71": 6291, "ng:performance:benchmarking_71": 6292, "ng:performance:scaling_71": 6293, "ng:performance:tuning_71": 6294, "ng:performance:optimization_72": 6295, "ng:performance:caching_72": 6296, "ng:performance:parallelization_72": 6297, "ng:performance:vectorization_72": 6298, "ng:performance:profiling_72": 6299, "ng:performance:benchmarking_72": 6300, "ng:performance:scaling_72": 6301, "ng:performance:tuning_72": 6302, "ng:performance:optimization_73": 6303, "ng:performance:caching_73": 6304, "ng:performance:parallelization_73": 6305, "ng:performance:vectorization_73": 6306, "ng:performance:profiling_73": 6307, "ng:performance:benchmarking_73": 6308, "ng:performance:scaling_73": 6309, "ng:performance:tuning_73": 6310, "ng:performance:optimization_74": 6311, "ng:performance:caching_74": 6312, "ng:performance:vectorization_74": 6313, "ng:performance:profiling_74": 6314, "ng:performance:benchmarking_74": 6315, "ng:performance:scaling_74": 6316, "ng:performance:tuning_74": 6317, "ng:performance:optimization_75": 6318, "ng:performance:caching_75": 6319, "ng:performance:parallelization_75": 6320, "ng:performance:vectorization_75": 6321, "ng:performance:profiling_75": 6322, "ng:performance:benchmarking_75": 6323, "ng:performance:scaling_75": 6324, "ng:performance:tuning_75": 6325, "ng:performance:optimization_76": 6326, "ng:performance:caching_76": 6327, "ng:performance:parallelization_76": 6328, "ng:performance:vectorization_76": 6329, "ng:performance:profiling_76": 6330, "ng:performance:benchmarking_76": 6331, "ng:performance:scaling_76": 6332, "ng:performance:tuning_76": 6333, "ng:performance:optimization_77": 6334, "ng:performance:caching_77": 6335, "ng:performance:parallelization_77": 6336, "ng:performance:vectorization_77": 6337, "ng:performance:profiling_77": 6338, "ng:performance:benchmarking_77": 6339, "ng:performance:scaling_77": 6340, "ng:performance:tuning_77": 6341, "ng:performance:optimization_78": 6342, "ng:performance:caching_78": 6343, "ng:performance:parallelization_78": 6344, "ng:performance:vectorization_78": 6345, "ng:performance:profiling_78": 6346, "ng:performance:benchmarking_78": 6347, "ng:performance:scaling_78": 6348, "ng:performance:tuning_78": 6349, "ng:performance:optimization_79": 6350, "ng:performance:caching_79": 6351, "ng:performance:parallelization_79": 6352, "ng:performance:vectorization_79": 6353, "ng:performance:profiling_79": 6354, "ng:performance:benchmarking_79": 6355, "ng:performance:scaling_79": 6356, "ng:performance:tuning_79": 6357, "ng:performance:optimization_80": 6358, "ng:performance:caching_80": 6359, "ng:performance:parallelization_80": 6360, "ng:performance:vectorization_80": 6361, "ng:performance:profiling_80": 6362, "ng:performance:benchmarking_80": 6363, "ng:performance:scaling_80": 6364, "ng:performance:tuning_80": 6365, "ng:performance:optimization_81": 6366, "ng:performance:caching_81": 6367, "ng:performance:parallelization_81": 6368, "ng:performance:vectorization_81": 6369, "ng:performance:profiling_81": 6370, "ng:performance:benchmarking_81": 6371, "ng:performance:scaling_81": 6372, "ng:performance:tuning_81": 6373, "ng:performance:optimization_82": 6374, "ng:performance:caching_82": 6375, "ng:performance:parallelization_82": 6376, "ng:performance:vectorization_82": 6377, "ng:performance:profiling_82": 6378, "ng:performance:benchmarking_82": 6379, "ng:performance:scaling_82": 6380, "ng:performance:tuning_82": 6381, "ng:performance:optimization_83": 6382, "ng:performance:caching_83": 6383, "ng:performance:parallelization_83": 6384, "ng:performance:vectorization_83": 6385, "ng:performance:profiling_83": 6386, "ng:performance:benchmarking_83": 6387, "ng:performance:scaling_83": 6388, "ng:performance:tuning_83": 6389, "ng:performance:optimization_84": 6390, "ng:performance:caching_84": 6391, "ng:performance:parallelization_84": 6392, "ng:performance:vectorization_84": 6393, "ng:performance:profiling_84": 6394, "ng:performance:benchmarking_84": 6395, "ng:performance:scaling_84": 6396, "ng:performance:tuning_84": 6397, "ng:performance:optimization_85": 6398, "ng:performance:caching_85": 6399, "ng:performance:parallelization_85": 6400, "ng:performance:vectorization_85": 6401, "ng:performance:profiling_85": 6402, "ng:performance:benchmarking_85": 6403, "ng:performance:scaling_85": 6404, "ng:performance:tuning_85": 6405, "ng:performance:optimization_86": 6406, "ng:performance:caching_86": 6407, "ng:performance:parallelization_86": 6408, "ng:performance:vectorization_86": 6409, "ng:performance:profiling_86": 6410, "ng:performance:benchmarking_86": 6411, "ng:performance:scaling_86": 6412, "ng:performance:tuning_86": 6413, "⎵": 6414, "➰": 6415, "⪻": 6416, "⎺": 6417, "⊸": 6418, "⪳": 6419, "⎷": 6420, "⮱": 6421, "⮴": 6422, "⮵": 6423, "⎹": 6424, "⪶": 6425, "⎳": 6426, "⎲": 6427, "⎴": 6428, "⪰": 6429, "➸": 6430, "⪷": 6431, "⎰": 6432, "⪯": 6433, "⊴": 6434, "➱": 6435, "⪙": 6436, "➹": 6437, "➺": 6438, "➻": 6439, "⪹": 6440, "⪺": 6441, "⮻": 6442, "⎱": 6443, "➲": 6444, "➳": 6445, "⪱": 6446, "⎶": 6447, "➶": 6448, "⮉": 6449, "⇖": 6450, "⇨": 6451, "ℯ": 6452, "⇺": 6453, "🟗": 6454, "🟕": 6455, "🟠": 6456, "🟣": 6457, "🟡": 6458, "🟘": 6459, "🟙": 6460, "🠀": 6461, "🠁": 6462, "🠄": 6463, "🠠": 6464, "🠡": 6465, "🠤": 6466, "🠥": 6467, "🠧": 6468, "⌺": 6469, "⍅": 6470, "⍌": 6471, "⍪": 6472, "⊛": 6473, "⊝": 6474, "⋏": 6475, "≏": 6476, "⌱": 6477, "⋫": 6478, "∔": 6479, "⋶": 6480, "⎆": 6481, "∺": 6482, "≡": 6483, "⊷": 6484, "⊺": 6485, "‘": 6486, "⇦": 6487, "₼": 6488, "∇": 6489, "⊽": 6490, "⍻": 6491, "⑶": 6492, "℟": 6493, "‷": 6494, "’": 6495, "ℱ": 6496, "⋴": 6497, "ⅷ": 6498, "ⅻ": 6499, "⌰": 6500, "™": 6501, "⊪": 6502, "∪": 6503, "⋙": 6504, "⅙": 6505, "ng:quantum:entanglement": 6506, "ng:quantum:correction_1": 6507, "ng:quantum:algorithm_2": 6508, "ng:quantum:superposition_3": 6509, "ng:quantum:decoherence_3": 6510, "ng:quantum:algorithm_3": 6511, "ng:quantum:superposition_4": 6512, "ng:quantum:entanglement_4": 6513, "ng:quantum:decoherence_4": 6514, "ng:quantum:measurement_4": 6515, "ng:quantum:gate_4": 6516, "ng:quantum:algorithm_4": 6517, "ng:quantum:error_4": 6518, "ng:quantum:superposition_5": 6519, "ng:quantum:measurement_5": 6520, "ng:quantum:correction_5": 6521, "ng:quantum:algorithm_6": 6522, "ng:quantum:error_6": 6523, "ng:quantum:decoherence_7": 6524, "ng:quantum:measurement_7": 6525, "ng:quantum:error_7": 6526, "ng:quantum:entanglement_8": 6527, "ng:quantum:decoherence_8": 6528, "ng:quantum:measurement_8": 6529, "ng:quantum:error_8": 6530, "ng:quantum:correction_8": 6531, "ng:quantum:decoherence_9": 6532, "ng:quantum:algorithm_9": 6533, "ng:quantum:decoherence_10": 6534, "ng:quantum:measurement_10": 6535, "ng:quantum:gate_10": 6536, "ng:quantum:error_10": 6537, "ng:quantum:correction_10": 6538, "ng:quantum:measurement_11": 6539, "ng:quantum:error_11": 6540, "ng:quantum:measurement_12": 6541, "ng:quantum:gate_12": 6542, "ng:quantum:error_12": 6543, "ng:quantum:correction_12": 6544, "ng:quantum:measurement_13": 6545, "ng:quantum:superposition_14": 6546, "ng:quantum:measurement_14": 6547, "ng:quantum:gate_14": 6548, "ng:quantum:error_14": 6549, "ng:quantum:correction_14": 6550, "ng:quantum:superposition_15": 6551, "ng:quantum:entanglement_15": 6552, "ng:quantum:decoherence_15": 6553, "ng:quantum:measurement_15": 6554, "ng:quantum:gate_15": 6555, "ng:quantum:algorithm_15": 6556, "ng:quantum:error_15": 6557, "ng:quantum:entanglement_16": 6558, "ng:quantum:measurement_16": 6559, "ng:quantum:gate_16": 6560, "ng:quantum:algorithm_16": 6561, "ng:quantum:superposition_17": 6562, "ng:quantum:decoherence_17": 6563, "ng:quantum:measurement_17": 6564, "ng:quantum:error_17": 6565, "ng:quantum:correction_17": 6566, "ng:quantum:superposition_18": 6567, "ng:quantum:measurement_18": 6568, "ng:quantum:algorithm_18": 6569, "ng:quantum:correction_18": 6570, "ng:quantum:decoherence_19": 6571, "ng:quantum:measurement_19": 6572, "ng:quantum:gate_19": 6573, "ng:quantum:algorithm_19": 6574, "ng:quantum:error_19": 6575, "ng:quantum:superposition_20": 6576, "ng:quantum:decoherence_20": 6577, "ng:quantum:algorithm_20": 6578, "ng:quantum:decoherence_21": 6579, "ng:quantum:measurement_21": 6580, "ng:quantum:gate_21": 6581, "ng:quantum:algorithm_21": 6582, "ng:quantum:error_21": 6583, "ng:quantum:superposition_24": 6584, "ng:quantum:entanglement_24": 6585, "ng:quantum:decoherence_24": 6586, "ng:quantum:measurement_24": 6587, "ng:quantum:gate_24": 6588, "ng:quantum:algorithm_24": 6589, "ng:quantum:error_24": 6590, "ng:quantum:correction_24": 6591, "ng:quantum:superposition_25": 6592, "ng:quantum:entanglement_25": 6593, "ng:quantum:decoherence_25": 6594, "ng:quantum:measurement_25": 6595, "ng:quantum:gate_25": 6596, "ng:quantum:algorithm_25": 6597, "ng:quantum:error_25": 6598, "ng:quantum:correction_25": 6599, "ng:quantum:superposition_26": 6600, "ng:quantum:entanglement_26": 6601, "ng:quantum:decoherence_26": 6602, "ng:quantum:measurement_26": 6603, "ng:quantum:gate_26": 6604, "ng:quantum:algorithm_26": 6605, "ng:quantum:error_26": 6606, "ng:quantum:correction_26": 6607, "ng:quantum:superposition_27": 6608, "ng:quantum:entanglement_27": 6609, "ng:quantum:decoherence_27": 6610, "ng:quantum:measurement_27": 6611, "ng:quantum:gate_27": 6612, "ng:quantum:algorithm_27": 6613, "ng:quantum:error_27": 6614, "ng:quantum:correction_27": 6615, "ng:quantum:superposition_28": 6616, "ng:quantum:entanglement_28": 6617, "ng:quantum:decoherence_28": 6618, "ng:quantum:measurement_28": 6619, "ng:quantum:gate_28": 6620, "ng:quantum:algorithm_28": 6621, "ng:quantum:error_28": 6622, "ng:quantum:correction_28": 6623, "ng:quantum:superposition_29": 6624, "ng:quantum:entanglement_29": 6625, "ng:quantum:decoherence_29": 6626, "ng:quantum:measurement_29": 6627, "ng:quantum:gate_29": 6628, "ng:quantum:algorithm_29": 6629, "ng:quantum:error_29": 6630, "ng:quantum:correction_29": 6631, "ng:quantum:superposition_30": 6632, "ng:quantum:entanglement_30": 6633, "ng:quantum:decoherence_30": 6634, "ng:quantum:measurement_30": 6635, "ng:quantum:gate_30": 6636, "ng:quantum:algorithm_30": 6637, "ng:quantum:error_30": 6638, "ng:quantum:correction_30": 6639, "ng:quantum:superposition_31": 6640, "ng:quantum:entanglement_31": 6641, "ng:quantum:decoherence_31": 6642, "ng:quantum:measurement_31": 6643, "ng:quantum:gate_31": 6644, "ng:quantum:algorithm_31": 6645, "ng:quantum:error_31": 6646, "ng:quantum:correction_31": 6647, "ng:quantum:superposition_32": 6648, "ng:quantum:entanglement_32": 6649, "ng:quantum:decoherence_32": 6650, "ng:quantum:measurement_32": 6651, "ng:quantum:gate_32": 6652, "ng:quantum:algorithm_32": 6653, "ng:quantum:error_32": 6654, "ng:quantum:correction_32": 6655, "ng:quantum:superposition_33": 6656, "ng:quantum:entanglement_33": 6657, "ng:quantum:decoherence_33": 6658, "ng:quantum:measurement_33": 6659, "ng:quantum:gate_33": 6660, "ng:quantum:algorithm_33": 6661, "ng:quantum:error_33": 6662, "ng:quantum:correction_33": 6663, "ng:quantum:superposition_34": 6664, "ng:quantum:entanglement_34": 6665, "ng:quantum:decoherence_34": 6666, "ng:quantum:measurement_34": 6667, "ng:quantum:gate_34": 6668, "ng:quantum:algorithm_34": 6669, "ng:quantum:error_34": 6670, "ng:quantum:correction_34": 6671, "ng:quantum:superposition_35": 6672, "ng:quantum:entanglement_35": 6673, "ng:quantum:decoherence_35": 6674, "ng:quantum:measurement_35": 6675, "ng:quantum:gate_35": 6676, "ng:quantum:error_35": 6677, "ng:quantum:correction_35": 6678, "ng:quantum:superposition_36": 6679, "ng:quantum:entanglement_36": 6680, "ng:quantum:decoherence_36": 6681, "ng:quantum:measurement_36": 6682, "ng:quantum:gate_36": 6683, "ng:quantum:algorithm_36": 6684, "ng:quantum:error_36": 6685, "ng:quantum:correction_36": 6686, "ng:quantum:superposition_37": 6687, "ng:quantum:decoherence_37": 6688, "ng:quantum:measurement_37": 6689, "ng:quantum:gate_37": 6690, "ng:quantum:algorithm_37": 6691, "ng:quantum:error_37": 6692, "ng:quantum:correction_37": 6693, "ng:quantum:superposition_38": 6694, "ng:quantum:entanglement_38": 6695, "ng:quantum:decoherence_38": 6696, "ng:quantum:measurement_38": 6697, "ng:quantum:gate_38": 6698, "ng:quantum:algorithm_38": 6699, "ng:quantum:error_38": 6700, "ng:quantum:correction_38": 6701, "ng:quantum:superposition_39": 6702, "ng:quantum:entanglement_39": 6703, "ng:quantum:decoherence_39": 6704, "ng:quantum:measurement_39": 6705, "ng:quantum:gate_39": 6706, "ng:quantum:algorithm_39": 6707, "ng:quantum:error_39": 6708, "ng:quantum:correction_39": 6709, "ng:quantum:superposition_40": 6710, "ng:quantum:entanglement_40": 6711, "ng:quantum:decoherence_40": 6712, "ng:quantum:measurement_40": 6713, "ng:quantum:gate_40": 6714, "ng:quantum:algorithm_40": 6715, "ng:quantum:error_40": 6716, "ng:quantum:correction_40": 6717, "ng:quantum:superposition_41": 6718, "ng:quantum:entanglement_41": 6719, "ng:quantum:decoherence_41": 6720, "ng:quantum:measurement_41": 6721, "ng:quantum:gate_41": 6722, "ng:quantum:algorithm_41": 6723, "ng:quantum:error_41": 6724, "ng:quantum:correction_41": 6725, "ng:quantum:superposition_42": 6726, "ng:quantum:entanglement_42": 6727, "ng:quantum:decoherence_42": 6728, "ng:quantum:measurement_42": 6729, "ng:quantum:gate_42": 6730, "ng:quantum:algorithm_42": 6731, "ng:quantum:error_42": 6732, "ng:quantum:correction_42": 6733, "ng:quantum:superposition_43": 6734, "ng:quantum:entanglement_43": 6735, "ng:quantum:decoherence_43": 6736, "ng:quantum:measurement_43": 6737, "ng:quantum:gate_43": 6738, "ng:quantum:algorithm_43": 6739, "ng:quantum:error_43": 6740, "ng:quantum:correction_43": 6741, "ng:quantum:superposition_44": 6742, "ng:quantum:entanglement_44": 6743, "ng:quantum:decoherence_44": 6744, "ng:quantum:measurement_44": 6745, "ng:quantum:gate_44": 6746, "ng:quantum:algorithm_44": 6747, "ng:quantum:error_44": 6748, "ng:quantum:correction_44": 6749, "ng:quantum:superposition_45": 6750, "ng:quantum:entanglement_45": 6751, "ng:quantum:decoherence_45": 6752, "ng:quantum:measurement_45": 6753, "ng:quantum:gate_45": 6754, "ng:quantum:algorithm_45": 6755, "ng:quantum:error_45": 6756, "ng:quantum:correction_45": 6757, "ng:quantum:superposition_46": 6758, "ng:quantum:entanglement_46": 6759, "ng:quantum:decoherence_46": 6760, "ng:quantum:measurement_46": 6761, "ng:quantum:gate_46": 6762, "ng:quantum:algorithm_46": 6763, "ng:quantum:error_46": 6764, "ng:quantum:correction_46": 6765, "ng:quantum:superposition_47": 6766, "ng:quantum:entanglement_47": 6767, "ng:quantum:decoherence_47": 6768, "ng:quantum:measurement_47": 6769, "ng:quantum:gate_47": 6770, "ng:quantum:algorithm_47": 6771, "ng:quantum:error_47": 6772, "ng:quantum:correction_47": 6773, "ng:quantum:superposition_48": 6774, "ng:quantum:entanglement_48": 6775, "ng:quantum:decoherence_48": 6776, "ng:quantum:measurement_48": 6777, "ng:quantum:gate_48": 6778, "ng:quantum:algorithm_48": 6779, "ng:quantum:error_48": 6780, "ng:quantum:correction_48": 6781, "ng:quantum:superposition_49": 6782, "ng:quantum:entanglement_49": 6783, "ng:quantum:decoherence_49": 6784, "ng:quantum:measurement_49": 6785, "ng:quantum:gate_49": 6786, "ng:quantum:algorithm_49": 6787, "ng:quantum:error_49": 6788, "ng:quantum:correction_49": 6789, "ng:quantum:superposition_50": 6790, "ng:quantum:entanglement_50": 6791, "ng:quantum:decoherence_50": 6792, "ng:quantum:measurement_50": 6793, "ng:quantum:gate_50": 6794, "ng:quantum:algorithm_50": 6795, "ng:quantum:error_50": 6796, "ng:quantum:correction_50": 6797, "ng:quantum:superposition_51": 6798, "ng:quantum:entanglement_51": 6799, "ng:quantum:decoherence_51": 6800, "ng:quantum:measurement_51": 6801, "ng:quantum:gate_51": 6802, "ng:quantum:algorithm_51": 6803, "ng:quantum:error_51": 6804, "ng:quantum:correction_51": 6805, "ng:quantum:superposition_52": 6806, "ng:quantum:entanglement_52": 6807, "ng:quantum:decoherence_52": 6808, "ng:quantum:measurement_52": 6809, "ng:quantum:gate_52": 6810, "ng:quantum:algorithm_52": 6811, "ng:quantum:error_52": 6812, "ng:quantum:correction_52": 6813, "ng:quantum:superposition_53": 6814, "ng:quantum:entanglement_53": 6815, "ng:quantum:decoherence_53": 6816, "ng:quantum:measurement_53": 6817, "ng:quantum:gate_53": 6818, "ng:quantum:algorithm_53": 6819, "ng:quantum:error_53": 6820, "ng:quantum:correction_53": 6821, "ng:quantum:superposition_54": 6822, "ng:quantum:entanglement_54": 6823, "ng:quantum:decoherence_54": 6824, "ng:quantum:measurement_54": 6825, "ng:quantum:gate_54": 6826, "ng:quantum:algorithm_54": 6827, "ng:quantum:error_54": 6828, "ng:quantum:correction_54": 6829, "ng:quantum:superposition_55": 6830, "ng:quantum:entanglement_55": 6831, "ng:quantum:decoherence_55": 6832, "ng:quantum:measurement_55": 6833, "ng:quantum:gate_55": 6834, "ng:quantum:algorithm_55": 6835, "ng:quantum:error_55": 6836, "ng:quantum:correction_55": 6837, "ng:quantum:superposition_56": 6838, "ng:quantum:entanglement_56": 6839, "ng:quantum:decoherence_56": 6840, "ng:quantum:measurement_56": 6841, "ng:quantum:gate_56": 6842, "ng:quantum:algorithm_56": 6843, "ng:quantum:error_56": 6844, "ng:quantum:correction_56": 6845, "ng:quantum:superposition_57": 6846, "ng:quantum:entanglement_57": 6847, "ng:quantum:decoherence_57": 6848, "ng:quantum:measurement_57": 6849, "ng:quantum:gate_57": 6850, "ng:quantum:algorithm_57": 6851, "ng:quantum:error_57": 6852, "ng:quantum:correction_57": 6853, "ng:quantum:superposition_58": 6854, "ng:quantum:entanglement_58": 6855, "ng:quantum:decoherence_58": 6856, "ng:quantum:measurement_58": 6857, "ng:quantum:gate_58": 6858, "ng:quantum:algorithm_58": 6859, "ng:quantum:error_58": 6860, "ng:quantum:correction_58": 6861, "ng:quantum:superposition_59": 6862, "ng:quantum:entanglement_59": 6863, "ng:quantum:decoherence_59": 6864, "ng:quantum:measurement_59": 6865, "ng:quantum:gate_59": 6866, "ng:quantum:algorithm_59": 6867, "ng:quantum:error_59": 6868, "ng:quantum:correction_59": 6869, "ng:quantum:superposition_60": 6870, "ng:quantum:entanglement_60": 6871, "ng:quantum:decoherence_60": 6872, "ng:quantum:measurement_60": 6873, "ng:quantum:gate_60": 6874, "ng:quantum:algorithm_60": 6875, "ng:quantum:error_60": 6876, "ng:quantum:correction_60": 6877, "ng:quantum:superposition_61": 6878, "ng:quantum:entanglement_61": 6879, "ng:quantum:decoherence_61": 6880, "ng:quantum:measurement_61": 6881, "ng:quantum:gate_61": 6882, "ng:quantum:algorithm_61": 6883, "ng:quantum:error_61": 6884, "ng:quantum:correction_61": 6885, "ng:quantum:superposition_62": 6886, "ng:quantum:entanglement_62": 6887, "ng:quantum:decoherence_62": 6888, "ng:quantum:measurement_62": 6889, "ng:quantum:gate_62": 6890, "ng:quantum:algorithm_62": 6891, "ng:quantum:error_62": 6892, "ng:quantum:correction_62": 6893, "ng:quantum:superposition_63": 6894, "ng:quantum:entanglement_63": 6895, "ng:quantum:decoherence_63": 6896, "ng:quantum:measurement_63": 6897, "ng:quantum:gate_63": 6898, "ng:quantum:algorithm_63": 6899, "ng:quantum:error_63": 6900, "ng:quantum:correction_63": 6901, "ng:quantum:superposition_64": 6902, "ng:quantum:entanglement_64": 6903, "ng:quantum:decoherence_64": 6904, "ng:quantum:measurement_64": 6905, "ng:quantum:gate_64": 6906, "ng:quantum:algorithm_64": 6907, "ng:quantum:error_64": 6908, "ng:quantum:correction_64": 6909, "ng:quantum:superposition_65": 6910, "ng:quantum:entanglement_65": 6911, "ng:quantum:decoherence_65": 6912, "ng:quantum:measurement_65": 6913, "ng:quantum:gate_65": 6914, "ng:quantum:algorithm_65": 6915, "ng:quantum:error_65": 6916, "ng:quantum:correction_65": 6917, "ng:quantum:superposition_66": 6918, "ng:quantum:entanglement_66": 6919, "ng:quantum:decoherence_66": 6920, "ng:quantum:measurement_66": 6921, "ng:quantum:gate_66": 6922, "ng:quantum:algorithm_66": 6923, "ng:quantum:error_66": 6924, "ng:quantum:correction_66": 6925, "ng:quantum:superposition_67": 6926, "ng:quantum:entanglement_67": 6927, "ng:quantum:decoherence_67": 6928, "ng:quantum:measurement_67": 6929, "ng:quantum:gate_67": 6930, "ng:quantum:algorithm_67": 6931, "ng:quantum:error_67": 6932, "ng:quantum:correction_67": 6933, "ng:quantum:superposition_68": 6934, "ng:quantum:entanglement_68": 6935, "ng:quantum:decoherence_68": 6936, "ng:quantum:measurement_68": 6937, "ng:quantum:gate_68": 6938, "ng:quantum:algorithm_68": 6939, "ng:quantum:error_68": 6940, "ng:quantum:correction_68": 6941, "ng:quantum:superposition_69": 6942, "ng:quantum:entanglement_69": 6943, "ng:quantum:decoherence_69": 6944, "ng:quantum:measurement_69": 6945, "ng:quantum:gate_69": 6946, "ng:quantum:algorithm_69": 6947, "ng:quantum:error_69": 6948, "ng:quantum:correction_69": 6949, "ng:quantum:superposition_70": 6950, "ng:quantum:entanglement_70": 6951, "ng:quantum:decoherence_70": 6952, "ng:quantum:measurement_70": 6953, "ng:quantum:gate_70": 6954, "ng:quantum:algorithm_70": 6955, "ng:quantum:error_70": 6956, "ng:quantum:correction_70": 6957, "ng:quantum:superposition_71": 6958, "ng:quantum:entanglement_71": 6959, "ng:quantum:decoherence_71": 6960, "ng:quantum:measurement_71": 6961, "ng:quantum:gate_71": 6962, "ng:quantum:algorithm_71": 6963, "ng:quantum:error_71": 6964, "ng:quantum:correction_71": 6965, "ng:quantum:superposition_72": 6966, "ng:quantum:entanglement_72": 6967, "ng:quantum:decoherence_72": 6968, "ng:quantum:measurement_72": 6969, "ng:quantum:gate_72": 6970, "ng:quantum:algorithm_72": 6971, "ng:quantum:error_72": 6972, "ng:quantum:correction_72": 6973, "ng:quantum:superposition_73": 6974, "ng:quantum:entanglement_73": 6975, "ng:quantum:decoherence_73": 6976, "ng:quantum:measurement_73": 6977, "ng:quantum:gate_73": 6978, "ng:quantum:algorithm_73": 6979, "ng:quantum:correction_73": 6980, "ng:quantum:superposition_74": 6981, "ng:quantum:entanglement_74": 6982, "ng:quantum:decoherence_74": 6983, "ng:quantum:measurement_74": 6984, "ng:quantum:gate_74": 6985, "ng:quantum:algorithm_74": 6986, "ng:quantum:error_74": 6987, "ng:quantum:correction_74": 6988, "ng:quantum:superposition_75": 6989, "ng:quantum:entanglement_75": 6990, "ng:quantum:decoherence_75": 6991, "ng:quantum:measurement_75": 6992, "ng:quantum:gate_75": 6993, "ng:quantum:algorithm_75": 6994, "ng:quantum:error_75": 6995, "ng:quantum:correction_75": 6996, "ng:quantum:superposition_76": 6997, "ng:quantum:entanglement_76": 6998, "ng:quantum:decoherence_76": 6999, "ng:quantum:measurement_76": 7000, "ng:quantum:gate_76": 7001, "ng:quantum:algorithm_76": 7002, "ng:quantum:error_76": 7003, "ng:quantum:correction_76": 7004, "ng:quantum:superposition_77": 7005, "ng:quantum:entanglement_77": 7006, "ng:quantum:decoherence_77": 7007, "ng:quantum:measurement_77": 7008, "ng:quantum:gate_77": 7009, "ng:quantum:algorithm_77": 7010, "ng:quantum:error_77": 7011, "ng:quantum:correction_77": 7012, "ng:quantum:superposition_78": 7013, "ng:quantum:entanglement_78": 7014, "ng:quantum:decoherence_78": 7015, "ng:quantum:measurement_78": 7016, "ng:quantum:gate_78": 7017, "ng:quantum:algorithm_78": 7018, "ng:quantum:error_78": 7019, "ng:quantum:correction_78": 7020, "ng:quantum:superposition_79": 7021, "ng:quantum:entanglement_79": 7022, "ng:quantum:decoherence_79": 7023, "ng:quantum:measurement_79": 7024, "ng:quantum:gate_79": 7025, "ng:quantum:algorithm_79": 7026, "ng:quantum:error_79": 7027, "ng:quantum:correction_79": 7028, "ng:quantum:superposition_80": 7029, "ng:quantum:entanglement_80": 7030, "ng:quantum:decoherence_80": 7031, "ng:quantum:measurement_80": 7032, "ng:quantum:gate_80": 7033, "ng:quantum:algorithm_80": 7034, "ng:quantum:error_80": 7035, "ng:quantum:correction_80": 7036, "ng:quantum:superposition_81": 7037, "ng:quantum:entanglement_81": 7038, "ng:quantum:decoherence_81": 7039, "ng:quantum:measurement_81": 7040, "ng:quantum:gate_81": 7041, "ng:quantum:algorithm_81": 7042, "ng:quantum:error_81": 7043, "ng:quantum:correction_81": 7044, "ng:quantum:superposition_82": 7045, "ng:quantum:entanglement_82": 7046, "ng:quantum:decoherence_82": 7047, "ng:quantum:measurement_82": 7048, "ng:quantum:gate_82": 7049, "ng:quantum:algorithm_82": 7050, "ng:quantum:error_82": 7051, "ng:quantum:correction_82": 7052, "ng:quantum:superposition_83": 7053, "ng:quantum:entanglement_83": 7054, "ng:quantum:decoherence_83": 7055, "ng:quantum:measurement_83": 7056, "ng:quantum:gate_83": 7057, "ng:quantum:algorithm_83": 7058, "ng:quantum:error_83": 7059, "ng:quantum:correction_83": 7060, "ng:quantum:superposition_84": 7061, "ng:quantum:entanglement_84": 7062, "ng:quantum:decoherence_84": 7063, "ng:quantum:measurement_84": 7064, "ng:quantum:gate_84": 7065, "ng:quantum:algorithm_84": 7066, "ng:quantum:error_84": 7067, "ng:quantum:correction_84": 7068, "ng:quantum:superposition_85": 7069, "ng:quantum:entanglement_85": 7070, "ng:quantum:decoherence_85": 7071, "ng:quantum:measurement_85": 7072, "ng:quantum:gate_85": 7073, "ng:quantum:algorithm_85": 7074, "ng:quantum:error_85": 7075, "ng:quantum:correction_85": 7076, "ng:quantum:superposition_86": 7077, "ng:quantum:entanglement_86": 7078, "ng:quantum:decoherence_86": 7079, "ng:quantum:measurement_86": 7080, "ng:quantum:gate_86": 7081, "ng:quantum:algorithm_86": 7082, "ng:quantum:error_86": 7083, "ng:quantum:correction_86": 7084, "⫱": 7085, "⫝̸": 7086, "≤": 7087, "⩤": 7088, "⍥": 7089, "⥥": 7090, "⥰": 7091, "⭤": 7092, "⭸": 7093, "⥤": 7094, "⪪": 7095, "⎑": 7096, "⯋": 7097, "⫑": 7098, "⫐": 7099, "⫒": 7100, "⋒": 7101, "⋑": 7102, "⏏": 7103, "⋗": 7104, "⯒": 7105, "⋖": 7106, "⯌": 7107, "⏑": 7108, "⏒": 7109, "⏔": 7110, "⏖": 7111, "⏕": 7112, "⯔": 7113, "⋍": 7114, "⋌": 7115, "⏍": 7116, "⫓": 7117, "⫋": 7118, "⫖": 7119, "⫆": 7120, "⯑": 7121, "⫾": 7122, "⯢": 7123, "⏊": 7124, "➮": 7125, "⫊": 7126, "➭": 7127, "⥨": 7128, "⫎": 7129, "⮏": 7130, "⯍": 7131, "⯏": 7132, "⏌": 7133, "⮣": 7134, "⯆": 7135, "⏇": 7136, "⏈": 7137, "⏉": 7138, "⫇": 7139, "⭯": 7140, "⯇": 7141, "⯈": 7142, "∠": 7143, "⍁": 7144, "≬": 7145, "⏷": 7146, "₵": 7147, "⑧": 7148, "⑲": 7149, "⊚": 7150, "⋎": 7151, "∤": 7152, "⋹": 7153, "": 7154, "−": 7155, "⋁": 7156, "≇": 7157, "⇄": 7158, "ↀ": 7159, "ⁿ": 7160, "⅘": 7161, "⅛": 7162, "⅖": 7163, "ng:security:nonrepudiation": 7164, "ng:security:authorization_1": 7165, "ng:security:integrity_1": 7166, "ng:security:authorization_3": 7167, "ng:security:nonrepudiation_3": 7168, "ng:security:audit_3": 7169, "ng:security:authentication_4": 7170, "ng:security:authorization_4": 7171, "ng:security:integrity_4": 7172, "ng:security:confidentiality_4": 7173, "ng:security:nonrepudiation_4": 7174, "ng:security:authentication_5": 7175, "ng:security:integrity_5": 7176, "ng:security:confidentiality_5": 7177, "ng:security:encryption_6": 7178, "ng:security:authentication_6": 7179, "ng:security:audit_6": 7180, "ng:security:compliance_6": 7181, "ng:security:nonrepudiation_7": 7182, "ng:security:compliance_7": 7183, "ng:security:encryption_8": 7184, "ng:security:authorization_8": 7185, "ng:security:nonrepudiation_8": 7186, "ng:security:encryption_9": 7187, "ng:security:integrity_9": 7188, "ng:security:audit_9": 7189, "ng:security:compliance_9": 7190, "ng:security:encryption_10": 7191, "ng:security:nonrepudiation_10": 7192, "ng:security:authentication_11": 7193, "ng:security:confidentiality_11": 7194, "ng:security:nonrepudiation_11": 7195, "ng:security:compliance_11": 7196, "ng:security:authentication_12": 7197, "ng:security:integrity_12": 7198, "ng:security:confidentiality_12": 7199, "ng:security:encryption_13": 7200, "ng:security:authentication_13": 7201, "ng:security:integrity_13": 7202, "ng:security:confidentiality_13": 7203, "ng:security:nonrepudiation_13": 7204, "ng:security:audit_13": 7205, "ng:security:compliance_13": 7206, "ng:security:encryption_14": 7207, "ng:security:authorization_14": 7208, "ng:security:integrity_14": 7209, "ng:security:confidentiality_14": 7210, "ng:security:nonrepudiation_14": 7211, "ng:security:encryption_15": 7212, "ng:security:authorization_15": 7213, "ng:security:confidentiality_15": 7214, "ng:security:nonrepudiation_15": 7215, "ng:security:audit_15": 7216, "ng:security:compliance_15": 7217, "ng:security:encryption_16": 7218, "ng:security:authentication_16": 7219, "ng:security:integrity_16": 7220, "ng:security:confidentiality_16": 7221, "ng:security:nonrepudiation_16": 7222, "ng:security:encryption_17": 7223, "ng:security:authentication_17": 7224, "ng:security:confidentiality_17": 7225, "ng:security:nonrepudiation_17": 7226, "ng:security:audit_17": 7227, "ng:security:compliance_17": 7228, "ng:security:integrity_18": 7229, "ng:security:audit_18": 7230, "ng:security:encryption_19": 7231, "ng:security:integrity_19": 7232, "ng:security:confidentiality_19": 7233, "ng:security:audit_19": 7234, "ng:security:compliance_19": 7235, "ng:security:encryption_20": 7236, "ng:security:integrity_20": 7237, "ng:security:confidentiality_20": 7238, "ng:security:audit_20": 7239, "ng:security:authentication_21": 7240, "ng:security:authorization_21": 7241, "ng:security:integrity_21": 7242, "ng:security:audit_21": 7243, "ng:security:encryption_24": 7244, "ng:security:authentication_24": 7245, "ng:security:authorization_24": 7246, "ng:security:integrity_24": 7247, "ng:security:confidentiality_24": 7248, "ng:security:nonrepudiation_24": 7249, "ng:security:audit_24": 7250, "ng:security:compliance_24": 7251, "ng:security:encryption_25": 7252, "ng:security:authentication_25": 7253, "ng:security:authorization_25": 7254, "ng:security:integrity_25": 7255, "ng:security:confidentiality_25": 7256, "ng:security:nonrepudiation_25": 7257, "ng:security:audit_25": 7258, "ng:security:compliance_25": 7259, "ng:security:encryption_26": 7260, "ng:security:authentication_26": 7261, "ng:security:authorization_26": 7262, "ng:security:integrity_26": 7263, "ng:security:confidentiality_26": 7264, "ng:security:nonrepudiation_26": 7265, "ng:security:audit_26": 7266, "ng:security:compliance_26": 7267, "ng:security:encryption_27": 7268, "ng:security:authentication_27": 7269, "ng:security:authorization_27": 7270, "ng:security:integrity_27": 7271, "ng:security:confidentiality_27": 7272, "ng:security:nonrepudiation_27": 7273, "ng:security:audit_27": 7274, "ng:security:compliance_27": 7275, "ng:security:encryption_28": 7276, "ng:security:authentication_28": 7277, "ng:security:authorization_28": 7278, "ng:security:integrity_28": 7279, "ng:security:confidentiality_28": 7280, "ng:security:nonrepudiation_28": 7281, "ng:security:audit_28": 7282, "ng:security:compliance_28": 7283, "ng:security:encryption_29": 7284, "ng:security:authentication_29": 7285, "ng:security:authorization_29": 7286, "ng:security:integrity_29": 7287, "ng:security:confidentiality_29": 7288, "ng:security:nonrepudiation_29": 7289, "ng:security:audit_29": 7290, "ng:security:compliance_29": 7291, "ng:security:encryption_30": 7292, "ng:security:authentication_30": 7293, "ng:security:authorization_30": 7294, "ng:security:integrity_30": 7295, "ng:security:confidentiality_30": 7296, "ng:security:nonrepudiation_30": 7297, "ng:security:audit_30": 7298, "ng:security:compliance_30": 7299, "ng:security:encryption_31": 7300, "ng:security:authentication_31": 7301, "ng:security:authorization_31": 7302, "ng:security:integrity_31": 7303, "ng:security:confidentiality_31": 7304, "ng:security:nonrepudiation_31": 7305, "ng:security:audit_31": 7306, "ng:security:compliance_31": 7307, "ng:security:encryption_32": 7308, "ng:security:authentication_32": 7309, "ng:security:authorization_32": 7310, "ng:security:integrity_32": 7311, "ng:security:confidentiality_32": 7312, "ng:security:nonrepudiation_32": 7313, "ng:security:audit_32": 7314, "ng:security:compliance_32": 7315, "ng:security:authentication_33": 7316, "ng:security:authorization_33": 7317, "ng:security:integrity_33": 7318, "ng:security:confidentiality_33": 7319, "ng:security:nonrepudiation_33": 7320, "ng:security:audit_33": 7321, "ng:security:compliance_33": 7322, "ng:security:encryption_34": 7323, "ng:security:authentication_34": 7324, "ng:security:authorization_34": 7325, "ng:security:integrity_34": 7326, "ng:security:confidentiality_34": 7327, "ng:security:nonrepudiation_34": 7328, "ng:security:audit_34": 7329, "ng:security:compliance_34": 7330, "ng:security:encryption_35": 7331, "ng:security:authentication_35": 7332, "ng:security:authorization_35": 7333, "ng:security:integrity_35": 7334, "ng:security:confidentiality_35": 7335, "ng:security:nonrepudiation_35": 7336, "ng:security:audit_35": 7337, "ng:security:compliance_35": 7338, "ng:security:encryption_36": 7339, "ng:security:authentication_36": 7340, "ng:security:authorization_36": 7341, "ng:security:integrity_36": 7342, "ng:security:confidentiality_36": 7343, "ng:security:nonrepudiation_36": 7344, "ng:security:audit_36": 7345, "ng:security:compliance_36": 7346, "ng:security:encryption_37": 7347, "ng:security:authentication_37": 7348, "ng:security:authorization_37": 7349, "ng:security:integrity_37": 7350, "ng:security:confidentiality_37": 7351, "ng:security:nonrepudiation_37": 7352, "ng:security:audit_37": 7353, "ng:security:compliance_37": 7354, "ng:security:encryption_38": 7355, "ng:security:authentication_38": 7356, "ng:security:authorization_38": 7357, "ng:security:integrity_38": 7358, "ng:security:confidentiality_38": 7359, "ng:security:nonrepudiation_38": 7360, "ng:security:audit_38": 7361, "ng:security:compliance_38": 7362, "ng:security:encryption_39": 7363, "ng:security:authentication_39": 7364, "ng:security:authorization_39": 7365, "ng:security:integrity_39": 7366, "ng:security:confidentiality_39": 7367, "ng:security:nonrepudiation_39": 7368, "ng:security:audit_39": 7369, "ng:security:compliance_39": 7370, "ng:security:encryption_40": 7371, "ng:security:authentication_40": 7372, "ng:security:authorization_40": 7373, "ng:security:integrity_40": 7374, "ng:security:confidentiality_40": 7375, "ng:security:nonrepudiation_40": 7376, "ng:security:audit_40": 7377, "ng:security:compliance_40": 7378, "ng:security:encryption_41": 7379, "ng:security:authentication_41": 7380, "ng:security:authorization_41": 7381, "ng:security:integrity_41": 7382, "ng:security:confidentiality_41": 7383, "ng:security:nonrepudiation_41": 7384, "ng:security:audit_41": 7385, "ng:security:compliance_41": 7386, "ng:security:encryption_42": 7387, "ng:security:authentication_42": 7388, "ng:security:authorization_42": 7389, "ng:security:integrity_42": 7390, "ng:security:confidentiality_42": 7391, "ng:security:nonrepudiation_42": 7392, "ng:security:audit_42": 7393, "ng:security:compliance_42": 7394, "ng:security:encryption_43": 7395, "ng:security:authentication_43": 7396, "ng:security:authorization_43": 7397, "ng:security:integrity_43": 7398, "ng:security:confidentiality_43": 7399, "ng:security:nonrepudiation_43": 7400, "ng:security:audit_43": 7401, "ng:security:compliance_43": 7402, "ng:security:encryption_44": 7403, "ng:security:authentication_44": 7404, "ng:security:authorization_44": 7405, "ng:security:integrity_44": 7406, "ng:security:confidentiality_44": 7407, "ng:security:nonrepudiation_44": 7408, "ng:security:audit_44": 7409, "ng:security:compliance_44": 7410, "ng:security:encryption_45": 7411, "ng:security:authorization_45": 7412, "ng:security:integrity_45": 7413, "ng:security:confidentiality_45": 7414, "ng:security:nonrepudiation_45": 7415, "ng:security:audit_45": 7416, "ng:security:compliance_45": 7417, "ng:security:encryption_46": 7418, "ng:security:authentication_46": 7419, "ng:security:authorization_46": 7420, "ng:security:integrity_46": 7421, "ng:security:confidentiality_46": 7422, "ng:security:nonrepudiation_46": 7423, "ng:security:audit_46": 7424, "ng:security:compliance_46": 7425, "ng:security:encryption_47": 7426, "ng:security:authentication_47": 7427, "ng:security:authorization_47": 7428, "ng:security:integrity_47": 7429, "ng:security:confidentiality_47": 7430, "ng:security:nonrepudiation_47": 7431, "ng:security:audit_47": 7432, "ng:security:compliance_47": 7433, "ng:security:encryption_48": 7434, "ng:security:authentication_48": 7435, "ng:security:authorization_48": 7436, "ng:security:integrity_48": 7437, "ng:security:confidentiality_48": 7438, "ng:security:nonrepudiation_48": 7439, "ng:security:audit_48": 7440, "ng:security:compliance_48": 7441, "ng:security:encryption_49": 7442, "ng:security:authentication_49": 7443, "ng:security:authorization_49": 7444, "ng:security:integrity_49": 7445, "ng:security:confidentiality_49": 7446, "ng:security:nonrepudiation_49": 7447, "ng:security:audit_49": 7448, "ng:security:compliance_49": 7449, "ng:security:encryption_50": 7450, "ng:security:authentication_50": 7451, "ng:security:authorization_50": 7452, "ng:security:integrity_50": 7453, "ng:security:confidentiality_50": 7454, "ng:security:nonrepudiation_50": 7455, "ng:security:audit_50": 7456, "ng:security:compliance_50": 7457, "ng:security:encryption_51": 7458, "ng:security:authentication_51": 7459, "ng:security:authorization_51": 7460, "ng:security:integrity_51": 7461, "ng:security:confidentiality_51": 7462, "ng:security:nonrepudiation_51": 7463, "ng:security:audit_51": 7464, "ng:security:compliance_51": 7465, "ng:security:encryption_52": 7466, "ng:security:authentication_52": 7467, "ng:security:authorization_52": 7468, "ng:security:integrity_52": 7469, "ng:security:confidentiality_52": 7470, "ng:security:nonrepudiation_52": 7471, "ng:security:audit_52": 7472, "ng:security:compliance_52": 7473, "ng:security:encryption_53": 7474, "ng:security:authentication_53": 7475, "ng:security:authorization_53": 7476, "ng:security:integrity_53": 7477, "ng:security:confidentiality_53": 7478, "ng:security:nonrepudiation_53": 7479, "ng:security:audit_53": 7480, "ng:security:compliance_53": 7481, "ng:security:encryption_54": 7482, "ng:security:authentication_54": 7483, "ng:security:authorization_54": 7484, "ng:security:integrity_54": 7485, "ng:security:confidentiality_54": 7486, "ng:security:nonrepudiation_54": 7487, "ng:security:audit_54": 7488, "ng:security:compliance_54": 7489, "ng:security:encryption_55": 7490, "ng:security:authentication_55": 7491, "ng:security:authorization_55": 7492, "ng:security:integrity_55": 7493, "ng:security:confidentiality_55": 7494, "ng:security:nonrepudiation_55": 7495, "ng:security:audit_55": 7496, "ng:security:encryption_56": 7497, "ng:security:authentication_56": 7498, "ng:security:authorization_56": 7499, "ng:security:integrity_56": 7500, "ng:security:confidentiality_56": 7501, "ng:security:nonrepudiation_56": 7502, "ng:security:audit_56": 7503, "ng:security:compliance_56": 7504, "ng:security:encryption_57": 7505, "ng:security:authentication_57": 7506, "ng:security:authorization_57": 7507, "ng:security:integrity_57": 7508, "ng:security:confidentiality_57": 7509, "ng:security:nonrepudiation_57": 7510, "ng:security:audit_57": 7511, "ng:security:compliance_57": 7512, "ng:security:encryption_58": 7513, "ng:security:authentication_58": 7514, "ng:security:authorization_58": 7515, "ng:security:integrity_58": 7516, "ng:security:confidentiality_58": 7517, "ng:security:nonrepudiation_58": 7518, "ng:security:audit_58": 7519, "ng:security:compliance_58": 7520, "ng:security:encryption_59": 7521, "ng:security:authentication_59": 7522, "ng:security:authorization_59": 7523, "ng:security:integrity_59": 7524, "ng:security:confidentiality_59": 7525, "ng:security:nonrepudiation_59": 7526, "ng:security:audit_59": 7527, "ng:security:compliance_59": 7528, "ng:security:encryption_60": 7529, "ng:security:authentication_60": 7530, "ng:security:authorization_60": 7531, "ng:security:integrity_60": 7532, "ng:security:confidentiality_60": 7533, "ng:security:nonrepudiation_60": 7534, "ng:security:audit_60": 7535, "ng:security:compliance_60": 7536, "ng:security:encryption_61": 7537, "ng:security:authentication_61": 7538, "ng:security:authorization_61": 7539, "ng:security:integrity_61": 7540, "ng:security:confidentiality_61": 7541, "ng:security:nonrepudiation_61": 7542, "ng:security:audit_61": 7543, "ng:security:compliance_61": 7544, "ng:security:encryption_62": 7545, "ng:security:authentication_62": 7546, "ng:security:authorization_62": 7547, "ng:security:integrity_62": 7548, "ng:security:confidentiality_62": 7549, "ng:security:nonrepudiation_62": 7550, "ng:security:audit_62": 7551, "ng:security:compliance_62": 7552, "ng:security:encryption_63": 7553, "ng:security:authentication_63": 7554, "ng:security:authorization_63": 7555, "ng:security:integrity_63": 7556, "ng:security:confidentiality_63": 7557, "ng:security:nonrepudiation_63": 7558, "ng:security:audit_63": 7559, "ng:security:compliance_63": 7560, "ng:security:encryption_64": 7561, "ng:security:authentication_64": 7562, "ng:security:authorization_64": 7563, "ng:security:integrity_64": 7564, "ng:security:confidentiality_64": 7565, "ng:security:nonrepudiation_64": 7566, "ng:security:audit_64": 7567, "ng:security:compliance_64": 7568, "ng:security:encryption_65": 7569, "ng:security:authentication_65": 7570, "ng:security:authorization_65": 7571, "ng:security:integrity_65": 7572, "ng:security:confidentiality_65": 7573, "ng:security:nonrepudiation_65": 7574, "ng:security:audit_65": 7575, "ng:security:compliance_65": 7576, "ng:security:encryption_66": 7577, "ng:security:authentication_66": 7578, "ng:security:authorization_66": 7579, "ng:security:integrity_66": 7580, "ng:security:confidentiality_66": 7581, "ng:security:nonrepudiation_66": 7582, "ng:security:audit_66": 7583, "ng:security:compliance_66": 7584, "ng:security:encryption_67": 7585, "ng:security:authentication_67": 7586, "ng:security:authorization_67": 7587, "ng:security:integrity_67": 7588, "ng:security:confidentiality_67": 7589, "ng:security:nonrepudiation_67": 7590, "ng:security:audit_67": 7591, "ng:security:compliance_67": 7592, "ng:security:encryption_68": 7593, "ng:security:authentication_68": 7594, "ng:security:authorization_68": 7595, "ng:security:integrity_68": 7596, "ng:security:confidentiality_68": 7597, "ng:security:nonrepudiation_68": 7598, "ng:security:audit_68": 7599, "ng:security:compliance_68": 7600, "ng:security:encryption_69": 7601, "ng:security:authentication_69": 7602, "ng:security:authorization_69": 7603, "ng:security:integrity_69": 7604, "ng:security:confidentiality_69": 7605, "ng:security:nonrepudiation_69": 7606, "ng:security:audit_69": 7607, "ng:security:compliance_69": 7608, "ng:security:encryption_70": 7609, "ng:security:authentication_70": 7610, "ng:security:authorization_70": 7611, "ng:security:integrity_70": 7612, "ng:security:confidentiality_70": 7613, "ng:security:nonrepudiation_70": 7614, "ng:security:audit_70": 7615, "ng:security:compliance_70": 7616, "ng:security:encryption_71": 7617, "ng:security:authentication_71": 7618, "ng:security:authorization_71": 7619, "ng:security:integrity_71": 7620, "ng:security:confidentiality_71": 7621, "ng:security:nonrepudiation_71": 7622, "ng:security:audit_71": 7623, "ng:security:compliance_71": 7624, "ng:security:encryption_72": 7625, "ng:security:authentication_72": 7626, "ng:security:authorization_72": 7627, "ng:security:integrity_72": 7628, "ng:security:confidentiality_72": 7629, "ng:security:nonrepudiation_72": 7630, "ng:security:audit_72": 7631, "ng:security:compliance_72": 7632, "ng:security:encryption_73": 7633, "ng:security:authentication_73": 7634, "ng:security:authorization_73": 7635, "ng:security:integrity_73": 7636, "ng:security:confidentiality_73": 7637, "ng:security:nonrepudiation_73": 7638, "ng:security:audit_73": 7639, "ng:security:compliance_73": 7640, "ng:security:encryption_74": 7641, "ng:security:authentication_74": 7642, "ng:security:authorization_74": 7643, "ng:security:integrity_74": 7644, "ng:security:confidentiality_74": 7645, "ng:security:nonrepudiation_74": 7646, "ng:security:audit_74": 7647, "ng:security:compliance_74": 7648, "ng:security:encryption_75": 7649, "ng:security:authentication_75": 7650, "ng:security:authorization_75": 7651, "ng:security:integrity_75": 7652, "ng:security:confidentiality_75": 7653, "ng:security:nonrepudiation_75": 7654, "ng:security:audit_75": 7655, "ng:security:compliance_75": 7656, "ng:security:encryption_76": 7657, "ng:security:authentication_76": 7658, "ng:security:authorization_76": 7659, "ng:security:integrity_76": 7660, "ng:security:confidentiality_76": 7661, "ng:security:nonrepudiation_76": 7662, "ng:security:audit_76": 7663, "ng:security:compliance_76": 7664, "ng:security:encryption_77": 7665, "ng:security:authentication_77": 7666, "ng:security:authorization_77": 7667, "ng:security:integrity_77": 7668, "ng:security:confidentiality_77": 7669, "ng:security:nonrepudiation_77": 7670, "ng:security:audit_77": 7671, "ng:security:compliance_77": 7672, "ng:security:encryption_78": 7673, "ng:security:authentication_78": 7674, "ng:security:authorization_78": 7675, "ng:security:integrity_78": 7676, "ng:security:confidentiality_78": 7677, "ng:security:nonrepudiation_78": 7678, "ng:security:audit_78": 7679, "ng:security:compliance_78": 7680, "ng:security:encryption_79": 7681, "ng:security:authentication_79": 7682, "ng:security:authorization_79": 7683, "ng:security:integrity_79": 7684, "ng:security:confidentiality_79": 7685, "ng:security:nonrepudiation_79": 7686, "ng:security:audit_79": 7687, "ng:security:compliance_79": 7688, "ng:security:encryption_80": 7689, "ng:security:authentication_80": 7690, "ng:security:authorization_80": 7691, "ng:security:integrity_80": 7692, "ng:security:confidentiality_80": 7693, "ng:security:nonrepudiation_80": 7694, "ng:security:audit_80": 7695, "ng:security:compliance_80": 7696, "ng:security:encryption_81": 7697, "ng:security:authentication_81": 7698, "ng:security:authorization_81": 7699, "ng:security:integrity_81": 7700, "ng:security:confidentiality_81": 7701, "ng:security:nonrepudiation_81": 7702, "ng:security:audit_81": 7703, "ng:security:compliance_81": 7704, "ng:security:encryption_82": 7705, "ng:security:authentication_82": 7706, "ng:security:authorization_82": 7707, "ng:security:integrity_82": 7708, "ng:security:confidentiality_82": 7709, "ng:security:nonrepudiation_82": 7710, "ng:security:audit_82": 7711, "ng:security:compliance_82": 7712, "ng:security:encryption_83": 7713, "ng:security:authentication_83": 7714, "ng:security:authorization_83": 7715, "ng:security:integrity_83": 7716, "ng:security:confidentiality_83": 7717, "ng:security:nonrepudiation_83": 7718, "ng:security:audit_83": 7719, "ng:security:compliance_83": 7720, "ng:security:encryption_84": 7721, "ng:security:authentication_84": 7722, "ng:security:authorization_84": 7723, "ng:security:integrity_84": 7724, "ng:security:confidentiality_84": 7725, "ng:security:nonrepudiation_84": 7726, "ng:security:audit_84": 7727, "ng:security:compliance_84": 7728, "ng:security:encryption_85": 7729, "ng:security:authentication_85": 7730, "ng:security:authorization_85": 7731, "ng:security:integrity_85": 7732, "ng:security:confidentiality_85": 7733, "ng:security:nonrepudiation_85": 7734, "ng:security:audit_85": 7735, "ng:security:compliance_85": 7736, "ng:security:encryption_86": 7737, "ng:security:authentication_86": 7738, "ng:security:authorization_86": 7739, "ng:security:integrity_86": 7740, "ng:security:confidentiality_86": 7741, "ng:security:nonrepudiation_86": 7742, "ng:security:audit_86": 7743, "ng:security:compliance_86": 7744, "⣔": 7745, "⡀": 7746, "𝐋": 7747, "𝒹": 7748, "𝓏": 7749, "⹁": 7750, "𝖸": 7751, "𝕱": 7752, "𝗇": 7753, "Ꝗ": 7754, "𝞗": 7755, "⟢": 7756, "𝘶": 7757, "𝒀": 7758, "𝔻": 7759, "𝟬": 7760, "𝓝": 7761, "⸪": 7762, "𝞝": 7763, "⇠": 7764, "↪": 7765, "⇲": 7766, "↼": 7767, "⇕": 7768, "↟": 7769, "⇧": 7770, "⇹": 7771, "↱": 7772, "◆": 7773, "⬢": 7774, "▲": 7775, "◑": 7776, "◍": 7777, "⬌": 7778, "⦃": 7779, "⤙": 7780, "⩑": 7781, "⨆": 7782, "⌅": 7783, "⬂": 7784, "⦡": 7785, "⬆": 7786, "▫": 7787, "⥆": 7788, "▧": 7789, "▿": 7790, "◚": 7791, "◵": 7792, "⩠": 7793, "◩": 7794, "⥗": 7795, "⦉": 7796, "⦼": 7797, "⧞": 7798, "℀": 7799, "ℶ": 7800, "ℤ": 7801, "ⅉ": 7802, "↧": 7803, "⇝": 7804, "↕": 7805, "⇋": 7806, "⇯": 7807, "⧪": 7808, "❦": 7809, "⧦": 7810, "≩": 7811, "≧": 7812, "≨": 7813, "≦": 7814, "❧": 7815, "⧧": 7816, "⩪": 7817, "⭥": 7818, "❪": 7819, "⭩": 7820, "⍧": 7821, "⥧": 7822, "⥶": 7823, "⍦": 7824, "⩥": 7825, "⍩": 7826, "➪": 7827, "⍨": 7828, "❨": 7829, "⭧": 7830, "⭨": 7831, "⥦": 7832, "⫴": 7833, "ⅆ": 7834, "↤": 7835, "⇚": 7836, "ℏ": 7837, "⇾": 7838, "ℳ": 7839, "℡": 7840, "↶": 7841, "⡃": 7842, "⡮": 7843, "⢈": 7844, "⠐": 7845, "ⷲ": 7846, "ꬵ": 7847, "𝟏": 7848, "𝞚": 7849, "𝚶": 7850, "𝝚": 7851, "𝒊": 7852, "ⱴ": 7853, "𝞂": 7854, "𝞼": 7855, "𝓹": 7856, "𝑨": 7857, "℆": 7858, "⇣": 7859, "↛": 7860, "⅏": 7861, "⇑": 7862, "⇵": 7863, "⩽": 7864, "⯓": 7865, "≼": 7866, "≻": 7867, "⩻": 7868, "⭻": 7869, "⩼": 7870, "⍼": 7871, "❼": 7872, "❻": 7873, "⥻": 7874, "⩺": 7875, "⭺": 7876, "❽": 7877, "##a": 7878, "##b": 7879, "##c": 7880, "##d": 7881, "##e": 7882, "##f": 7883, "##g": 7884, "##h": 7885, "##i": 7886, "##j": 7887, "##k": 7888, "##l": 7889, "##m": 7890, "##n": 7891, "##o": 7892, "##p": 7893, "##q": 7894, "##r": 7895, "##s": 7896, "##t": 7897, "##u": 7898, "##v": 7899, "##w": 7900, "##x": 7901, "##y": 7902, "##z": 7903, "##A": 7904, "##B": 7905, "##C": 7906, "##D": 7907, "##E": 7908, "##F": 7909, "##G": 7910, "##H": 7911, "##I": 7912, "##J": 7913, "##K": 7914, "##L": 7915, "##M": 7916, "##N": 7917, "##O": 7918, "##P": 7919, "##Q": 7920, "##R": 7921, "##S": 7922, "##T": 7923, "##U": 7924, "##V": 7925, "##W": 7926, "##X": 7927, "##Y": 7928, "##Z": 7929, "##0": 7930, "##1": 7931, "##2": 7932, "##3": 7933, "##4": 7934, "##5": 7935, "##6": 7936, "##7": 7937, "##8": 7938, "##9": 7939, "the": 7940, "an": 7941, "and": 7942, "or": 7943, "but": 7944, "in": 7945, "on": 7946, "at": 7947, "to": 7948, "for": 7949, "of": 7950, "with": 7951, "by": 7952, "from": 7953, "up": 7954, "about": 7955, "into": 7956, "through": 7957, "during": 7958, "before": 7959, "after": 7960, "above": 7961, "below": 7962, "is": 7963, "are": 7964, "was": 7965, "were": 7966, "be": 7967, "been": 7968, "being": 7969, "have": 7970, "has": 7971, "had": 7972, "do": 7973, "does": 7974, "did": 7975, "will": 7976, "would": 7977, "could": 7978, "should": 7979, "may": 7980, "might": 7981, "must": 7982, "can": 7983, "shall": 7984, "this": 7985, "that": 7986, "these": 7987, "those": 7988, "you": 7989, "he": 7990, "she": 7991, "it": 7992, "we": 7993, "they": 7994, "me": 7995, "him": 7996, "her": 7997, "us": 7998, "them": 7999, "my": 8000, "your": 8001, "his": 8002, "its": 8003, "our": 8004, "their": 8005, "what": 8006, "when": 8007, "where": 8008, "why": 8009, "how": 8010, "which": 8011, "who": 8012, "whom": 8013, "whose": 8014, "if": 8015, "then": 8016, "else": 8017, "while": 8018, "return": 8019, "function": 8020, "class": 8021, "def": 8022, "import": 8023, "as": 8024, "try": 8025, "except": 8026, "finally": 8027, "lambda": 8028, "yield": 8029, "true": 8030, "false": 8031, "null": 8032, "none": 8033, "undefined": 8034, "var": 8035, "let": 8036, "const": 8037, "async": 8038, "await": 8039}}}