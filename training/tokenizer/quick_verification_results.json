{"level_1": {"status": "PASS", "runs": [{"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105]}, {"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105]}, {"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105]}], "consistency_check": true}, "level_2": {"status": "PASS", "file_hashes": {"tokenizer_config": {"path": "training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer.json", "hash": "618aaeed477c8affef39b5ac18da98780b47aa9c4fa62c9303b791ec2a20f84b", "size_bytes": 5064727}, "verification_script": {"path": "training/tokenizer/detailed_verification_2_3.py", "hash": "d6de5d8a44e1f98d65b13791dbded49df305346cb50de3e7be97bf6dcd386480", "size_bytes": 47848}, "symbol_registry": {"path": "neuroglyph_ULTIMATE_registry.json", "hash": "4c2a5743242d9e52733ebcc752e4cb316c2bfe7e525d21d16143926a19a57fe0", "size_bytes": 4500322}, "hybrid_config": {"path": "training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer_config.json", "hash": "4789c617740332e82287ff1a4cdf3a67816c953368b322b53e09f43825734877", "size_bytes": 1429770}}, "missing_files": []}, "level_3": {"status": "PASS", "required_packages": {"transformers": {"installed": true, "version": "4.52.4", "min_required": "4.0.0"}, "tokenizers": {"installed": true, "version": "0.21.1", "min_required": "0.10.0"}}, "environment_clean": true}, "level_4": {"status": "PASS", "sample_verification": {"total_samples": 100, "sample_types_distribution": {"markup": 20, "pure_symbols": 20, "code_fragment": 17, "mixed_text": 18, "edge_case": 14, "natural_language": 11}, "first_10_samples": [{"id": 1, "type": "markup", "content": "markup_1"}, {"id": 2, "type": "pure_symbols", "content": "pure_symbols_2"}, {"id": 3, "type": "pure_symbols", "content": "pure_symbols_3"}, {"id": 4, "type": "markup", "content": "markup_4"}, {"id": 5, "type": "code_fragment", "content": "code_fragment_5"}, {"id": 6, "type": "mixed_text", "content": "mixed_text_6"}, {"id": 7, "type": "mixed_text", "content": "mixed_text_7"}, {"id": 8, "type": "mixed_text", "content": "mixed_text_8"}, {"id": 9, "type": "markup", "content": "markup_9"}, {"id": 10, "type": "pure_symbols", "content": "pure_symbols_10"}]}}, "level_5": {"status": "PASS", "total_symbols_tested": 100, "successful_roundtrips": 100, "failed_roundtrips": [], "tokenizer_loaded": true}}