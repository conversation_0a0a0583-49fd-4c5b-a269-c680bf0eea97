{"verification_timestamp": 1748996081.67269, "environment_info": {"python_version": "3.9.6 (default, Nov 11 2024, 03:15:38) \n[Clang 16.0.0 (clang-1600.0.26.6)]", "platform": "macOS-15.4.1-arm64-arm-64bit", "transformers_version": "4.52.4", "working_directory": "/Volumes/DANIELE/NEUROGLYPH", "pythonhashseed": "0"}, "level_1_reproducibility": {"status": "PASS", "runs": [{"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105], "random_floats": [0.6766994874229113, 0.8921795677048454, 0.08693883262941615, 0.4219218196852704, 0.029797219438070344], "sum_integers": 3321, "avg_floats": 0.42150738537610277}, {"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105], "random_floats": [0.6766994874229113, 0.8921795677048454, 0.08693883262941615, 0.4219218196852704, 0.029797219438070344], "sum_integers": 3321, "avg_floats": 0.42150738537610277}, {"random_integers": [655, 115, 26, 760, 282, 251, 229, 143, 755, 105], "random_floats": [0.6766994874229113, 0.8921795677048454, 0.08693883262941615, 0.4219218196852704, 0.029797219438070344], "sum_integers": 3321, "avg_floats": 0.42150738537610277}], "consistency_check": true, "seed_verification": 42}, "level_2_integrity": {"status": "PASS", "file_hashes": {"tokenizer_config": {"path": "training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer.json", "hash": "618aaeed477c8affef39b5ac18da98780b47aa9c4fa62c9303b791ec2a20f84b", "size_bytes": 5064727}, "verification_script": {"path": "training/tokenizer/detailed_verification_2_3.py", "hash": "d6de5d8a44e1f98d65b13791dbded49df305346cb50de3e7be97bf6dcd386480", "size_bytes": 47848}, "symbol_registry": {"path": "neuroglyph_ULTIMATE_registry.json", "hash": "4c2a5743242d9e52733ebcc752e4cb316c2bfe7e525d21d16143926a19a57fe0", "size_bytes": 4500322}, "hybrid_tokenizer_config": {"path": "training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer_config.json", "hash": "4789c617740332e82287ff1a4cdf3a67816c953368b322b53e09f43825734877", "size_bytes": 1429770}}, "missing_files": [], "hash_verification": {}}, "level_3_environment": {"status": "PASS", "python_version": [3, 9, 6, "final", 0], "required_packages": {"transformers": {"installed": true, "version": "4.52.4", "min_required": "4.0.0"}, "tokenizers": {"installed": true, "version": "0.21.1", "min_required": "0.10.0"}}, "environment_clean": true}, "level_4_coverage": {"status": "PASS", "sample_verification": {"total_samples": 100, "sample_types_distribution": {"markup": 18, "pure_symbols": 20, "code_fragment": 17, "mixed_text": 16, "edge_case": 17, "natural_language": 12}, "first_10_samples": [{"id": 1, "type": "markup", "content": "markup_1"}, {"id": 2, "type": "pure_symbols", "content": "symbols_1_2"}, {"id": 3, "type": "markup", "content": "markup_3"}, {"id": 4, "type": "code_fragment", "content": "code_fragment_4"}, {"id": 5, "type": "mixed_text", "content": "process_mixed_5"}, {"id": 6, "type": "mixed_text", "content": "function_mixed_6"}, {"id": 7, "type": "markup", "content": "markup_7"}, {"id": 8, "type": "markup", "content": "markup_8"}, {"id": 9, "type": "edge_case", "content": "edge_case_9"}, {"id": 10, "type": "pure_symbols", "content": "symbols_7_10"}]}, "seed_consistency": true}, "level_5_exhaustive": {"status": "PASS", "total_symbols": 7767, "successful_roundtrips": 7767, "failed_roundtrips": [], "tokenizer_loaded": true, "registry_loaded": true}, "overall_status": "PASS"}