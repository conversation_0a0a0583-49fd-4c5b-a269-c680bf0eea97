{"add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "<UNK>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<PAD>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "<MASK>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "<NG_START>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "<NG_END>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "5": {"content": "<NG_THINK>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "6": {"content": "<NG_REASON>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "7": {"content": "<NG_MEMORY>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "8": {"content": "<NG_VALIDATE>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "9": {"content": "<NG_ERROR>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "10": {"content": "<NG_CORRECT>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": ["<NG_START>", "<NG_END>", "<NG_THINK>", "<NG_REASON>", "<NG_MEMORY>", "<NG_VALIDATE>", "<NG_ERROR>", "<NG_CORRECT>"], "clean_up_tokenization_spaces": false, "extra_special_tokens": {}, "mask_token": "<MASK>", "model_max_length": 1000000000000000019884624838656, "pad_token": "<PAD>", "tokenizer_class": "PreTrainedTokenizer", "unk_token": "<UNK>"}