#!/usr/bin/env python3
"""
Verifica Rapida NEUROGLYPH v2.0 - Primi 4 Livelli

Test rapido dei primi 4 livelli di verifica senza il test exhaustivo.
"""

import os
import sys
import json
import hashlib
import random
import time
from pathlib import Path
from typing import Dict, List, Any
import platform

# Imposta seed deterministico
RANDOM_SEED = 42
os.environ['PYTHONHASHSEED'] = '0'
random.seed(RANDOM_SEED)

def calculate_file_hash(file_path: str) -> str:
    """Calcola hash SHA256 di un file."""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        return hashlib.sha256(content).hexdigest()
    except Exception as e:
        return f"ERROR: {str(e)}"

def level_1_reproducibility_test() -> Dict[str, Any]:
    """Livello 1: Test reproducibilità deterministica."""
    print("🔍 LIVELLO 1: Test Reproducibilità Deterministica")
    print("-" * 50)
    
    results = {'status': 'PENDING', 'runs': [], 'consistency_check': False}
    
    # Esegui 3 run con stesso seed
    for run_id in range(3):
        random.seed(RANDOM_SEED)
        test_numbers = [random.randint(1, 1000) for _ in range(10)]
        results['runs'].append({'random_integers': test_numbers})
    
    # Verifica consistenza
    first_run = results['runs'][0]
    all_consistent = all(
        run['random_integers'] == first_run['random_integers']
        for run in results['runs']
    )
    
    results['consistency_check'] = all_consistent
    results['status'] = 'PASS' if all_consistent else 'FAIL'
    
    print(f"✅ Consistenza: {all_consistent}")
    print(f"   Primi 3 numeri: {first_run['random_integers'][:3]}")
    
    return results

def level_2_integrity_test() -> Dict[str, Any]:
    """Livello 2: Test integrità file."""
    print("\n🔍 LIVELLO 2: Test Integrità File (SHA256)")
    print("-" * 50)
    
    critical_files = {
        'tokenizer_config': 'training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer.json',
        'verification_script': 'training/tokenizer/detailed_verification_2_3.py',
        'symbol_registry': 'neuroglyph_ULTIMATE_registry.json',
        'hybrid_config': 'training/tokenizer/neuroglyph_tokenizer_hybrid/tokenizer_config.json'
    }
    
    results = {'status': 'PENDING', 'file_hashes': {}, 'missing_files': []}
    
    for file_key, file_path in critical_files.items():
        print(f"  {file_key}: {file_path}")
        
        if Path(file_path).exists():
            file_hash = calculate_file_hash(file_path)
            results['file_hashes'][file_key] = {
                'path': file_path,
                'hash': file_hash,
                'size_bytes': Path(file_path).stat().st_size
            }
            print(f"    Hash: {file_hash[:16]}... ✅")
        else:
            results['missing_files'].append(file_path)
            print(f"    ❌ File mancante!")
    
    has_missing = len(results['missing_files']) > 0
    results['status'] = 'FAIL' if has_missing else 'PASS'
    
    return results

def level_3_environment_test() -> Dict[str, Any]:
    """Livello 3: Test ambiente immutabile."""
    print("\n🔍 LIVELLO 3: Test Ambiente Immutabile")
    print("-" * 50)
    
    results = {'status': 'PENDING', 'required_packages': {}, 'environment_clean': True}
    
    # Verifica pacchetti
    required_packages = {'transformers': '4.0.0', 'tokenizers': '0.10.0'}
    
    for package, min_version in required_packages.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'UNKNOWN')
            results['required_packages'][package] = {
                'installed': True,
                'version': version,
                'min_required': min_version
            }
            print(f"  ✅ {package}: {version}")
        except ImportError:
            results['required_packages'][package] = {
                'installed': False,
                'version': None,
                'min_required': min_version
            }
            print(f"  ❌ {package}: NON INSTALLATO")
            results['environment_clean'] = False
    
    # Verifica variabili ambiente
    pythonhashseed = os.environ.get('PYTHONHASHSEED', 'NOT_SET')
    print(f"  PYTHONHASHSEED: {pythonhashseed} {'✅' if pythonhashseed == '0' else '❌'}")
    
    if pythonhashseed != '0':
        results['environment_clean'] = False
    
    results['status'] = 'PASS' if results['environment_clean'] else 'FAIL'
    
    return results

def level_4_coverage_test() -> Dict[str, Any]:
    """Livello 4: Test copertura campioni."""
    print("\n🔍 LIVELLO 4: Test Copertura Campioni")
    print("-" * 50)
    
    results = {'status': 'PENDING', 'sample_verification': {}}
    
    # Rigenera campioni con seed deterministico
    random.seed(RANDOM_SEED)
    
    sample_types = ['pure_symbols', 'mixed_text', 'code_fragment', 
                   'natural_language', 'edge_case', 'markup']
    
    regenerated_samples = []
    for i in range(100):
        sample_type = random.choice(sample_types)
        sample_id = i + 1
        sample_content = f"{sample_type}_{sample_id}"
        
        regenerated_samples.append({
            'id': sample_id,
            'type': sample_type,
            'content': sample_content
        })
    
    # Conta distribuzione
    distribution = {}
    for sample in regenerated_samples:
        sample_type = sample['type']
        distribution[sample_type] = distribution.get(sample_type, 0) + 1
    
    results['sample_verification'] = {
        'total_samples': len(regenerated_samples),
        'sample_types_distribution': distribution,
        'first_10_samples': regenerated_samples[:10]
    }
    
    print(f"  Campioni rigenerati: {len(regenerated_samples)}")
    print(f"  Distribuzione tipi:")
    for sample_type, count in distribution.items():
        print(f"    {sample_type}: {count}")
    
    results['status'] = 'PASS'
    
    return results

def level_5_quick_roundtrip_test() -> Dict[str, Any]:
    """Livello 5: Test round-trip rapido (campione)."""
    print("\n🔍 LIVELLO 5: Test Round-trip Rapido (Campione)")
    print("-" * 50)
    
    results = {
        'status': 'PENDING',
        'total_symbols_tested': 0,
        'successful_roundtrips': 0,
        'failed_roundtrips': [],
        'tokenizer_loaded': False
    }
    
    try:
        # Carica registry simboli
        registry_path = "neuroglyph_ULTIMATE_registry.json"
        
        if not Path(registry_path).exists():
            results['status'] = 'FAIL_REGISTRY_MISSING'
            print(f"❌ Registry non trovato: {registry_path}")
            return results
        
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        symbols = []
        for symbol_data in registry.get('approved_symbols', []):
            symbol = symbol_data.get('symbol')
            if symbol:
                symbols.append(symbol)
        
        print(f"✅ Registry caricato: {len(symbols)} simboli")
        
        # Carica tokenizer
        tokenizer_path = "training/tokenizer/neuroglyph_tokenizer_hybrid"
        
        if not Path(tokenizer_path).exists():
            results['status'] = 'FAIL_TOKENIZER_MISSING'
            print(f"❌ Tokenizer non trovato: {tokenizer_path}")
            return results
        
        from transformers import PreTrainedTokenizerFast
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        results['tokenizer_loaded'] = True
        print(f"✅ Tokenizer caricato: {len(tokenizer)} token")
        
        # Test campione di 100 simboli
        sample_symbols = random.sample(symbols, min(100, len(symbols)))
        results['total_symbols_tested'] = len(sample_symbols)
        
        print(f"Testando campione di {len(sample_symbols)} simboli...")
        
        for symbol in sample_symbols:
            try:
                token_ids = tokenizer.encode(symbol, add_special_tokens=False)
                decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
                
                if symbol == decoded:
                    results['successful_roundtrips'] += 1
                else:
                    results['failed_roundtrips'].append({
                        'symbol': symbol,
                        'decoded': decoded
                    })
            except Exception as e:
                results['failed_roundtrips'].append({
                    'symbol': symbol,
                    'error': str(e)
                })
        
        success_rate = (results['successful_roundtrips'] / results['total_symbols_tested']) * 100
        
        print(f"   Round-trip riusciti: {results['successful_roundtrips']}/{results['total_symbols_tested']} ({success_rate:.1f}%)")
        print(f"   Round-trip falliti: {len(results['failed_roundtrips'])}")
        
        results['status'] = 'PASS' if len(results['failed_roundtrips']) == 0 else 'FAIL'
        
        if results['status'] == 'PASS':
            print("✅ Tutti i simboli del campione passano il test!")
        else:
            print("❌ Alcuni simboli falliscono:")
            for failure in results['failed_roundtrips'][:3]:
                if 'error' in failure:
                    print(f"     {failure['symbol']}: ERROR")
                else:
                    print(f"     {failure['symbol']} → {failure['decoded']}")
    
    except Exception as e:
        results['status'] = 'FAIL_EXCEPTION'
        results['error'] = str(e)
        print(f"❌ Errore: {e}")
    
    return results

def main():
    """Funzione principale."""
    print("🛡️ NEUROGLYPH v2.0 - Verifica Rapida (4+1 Livelli)")
    print("=" * 70)
    print(f"Seed deterministico: {RANDOM_SEED}")
    print(f"PYTHONHASHSEED: {os.environ.get('PYTHONHASHSEED', 'NOT_SET')}")
    print()
    
    # Esegui tutti i livelli
    results = {}
    results['level_1'] = level_1_reproducibility_test()
    results['level_2'] = level_2_integrity_test()
    results['level_3'] = level_3_environment_test()
    results['level_4'] = level_4_coverage_test()
    results['level_5'] = level_5_quick_roundtrip_test()
    
    # Determina status complessivo
    all_passed = all(
        results[level].get('status') == 'PASS'
        for level in results
    )
    
    overall_status = 'PASS' if all_passed else 'FAIL'
    
    # Risultato finale
    print(f"\n🎯 RISULTATO VERIFICA RAPIDA:")
    print(f"   Status: {overall_status}")
    
    passed_levels = sum(1 for level in results if results[level].get('status') == 'PASS')
    print(f"   Livelli superati: {passed_levels}/5")
    
    for level, result in results.items():
        status = result.get('status', 'UNKNOWN')
        icon = "✅" if status == 'PASS' else "❌"
        print(f"   {level}: {status} {icon}")
    
    if overall_status == 'PASS':
        print("\n🎊 VERIFICA RAPIDA SUPERATA!")
        print("✅ Risultati Fase 2.3 sembrano ATTENDIBILI")
        print("💡 Esegui verifica completa per conferma definitiva")
    else:
        print("\n❌ VERIFICA RAPIDA FALLITA!")
        print("⚠️ Risultati Fase 2.3 NECESSITANO REVISIONE")
    
    # Salva risultati
    results_path = Path("training/tokenizer/quick_verification_results.json")
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Risultati salvati: {results_path}")
    
    return overall_status == 'PASS'

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
