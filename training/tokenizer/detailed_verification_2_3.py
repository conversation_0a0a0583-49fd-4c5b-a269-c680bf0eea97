#!/usr/bin/env python3
"""
NEUROGLYPH v2.0 - Verifica Dettagliata Fase 2.3

Implementazione completa e dettagliata di tutti i punti della fase 2.3:
2.3.1 - Lista frasi-tipo con neuroglifi mappati
2.3.2 - Verifica zero-splitting (una unità per simbolo)
2.3.3 - Verifica detokenizzazione perfetta
2.3.4 - Test di sicurezza su 100 esempi casuali
2.3.5 - Rilevamento anomalie e correzioni
"""

import json
import random
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple, Set
from transformers import PreTrainedTokenizerFast
from collections import defaultdict
import re

class DetailedVerification23:
    """Verificatore dettagliato per fase 2.3."""
    
    def __init__(self, 
                 tokenizer_path: str = "training/tokenizer/neuroglyph_tokenizer_hybrid",
                 registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """Inizializza verificatore."""
        self.tokenizer_path = tokenizer_path
        self.registry_path = registry_path
        self.symbols = []
        self.tokenizer = None
        self._load_data()
    
    def _load_data(self):
        """Carica simboli e tokenizer."""
        # Carica simboli
        with open(self.registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        for symbol_data in registry.get('approved_symbols', []):
            symbol = symbol_data.get('symbol')
            if symbol:
                self.symbols.append(symbol)
        
        # Carica tokenizer
        self.tokenizer = PreTrainedTokenizerFast.from_pretrained(self.tokenizer_path)
        
        print(f"✅ Caricati {len(self.symbols)} simboli")
        print(f"✅ Tokenizer caricato: {len(self.tokenizer)} token nel vocabolario")
    
    def step_2_3_1_prepare_test_phrases(self) -> Dict[str, Any]:
        """
        2.3.1 - Prepara lista di frasi-tipo con neuroglifi mappati.
        
        Returns:
            Dizionario con frasi categorizzate e statistiche
        """
        print("\n" + "="*80)
        print("📋 FASE 2.3.1 - PREPARAZIONE FRASI-TIPO CON NEUROGLIFI MAPPATI")
        print("="*80)
        
        # Categorie di frasi-tipo
        phrase_categories = {
            'simboli_singoli': [],
            'frammenti_codice': [],
            'logica_simbolica': [],
            'testo_naturale': [],
            'markup_neuroglyph': [],
            'casi_edge': [],
            'sequenze_miste': []
        }
        
        # 1. Simboli singoli (campione rappresentativo)
        print("🔸 Generando simboli singoli...")
        sample_symbols = random.sample(self.symbols, min(50, len(self.symbols)))
        phrase_categories['simboli_singoli'] = sample_symbols
        
        # 2. Frammenti di codice con neuroglifi
        print("🔸 Generando frammenti di codice...")
        code_templates = [
            "def {s1}_function({s2}, {s3}): return {s4}",
            "class {s1}Processor: def process(self, {s2}): return {s3}",
            "if {s1} == {s2}: {s3}() else {s4}()",
            "for {s1} in {s2}_list: process({s3})",
            "while {s1} > 0: {s2} -= {s3}",
            "try: {s1}() except {s2}Error: {s3}()",
            "lambda {s1}: {s2}({s3}) + {s4}",
            "async def {s1}(): await {s2}(); return {s3}",
            "{s1}.{s2}({s3}).{s4}({s5})",
            "import {s1}; from {s2} import {s3}"
        ]
        
        for template in code_templates:
            symbols_needed = template.count('{s')
            selected_symbols = random.sample(self.symbols, symbols_needed)
            code_fragment = template.format(
                s1=selected_symbols[0] if len(selected_symbols) > 0 else '⟨',
                s2=selected_symbols[1] if len(selected_symbols) > 1 else '⟩',
                s3=selected_symbols[2] if len(selected_symbols) > 2 else '◊',
                s4=selected_symbols[3] if len(selected_symbols) > 3 else '⊢',
                s5=selected_symbols[4] if len(selected_symbols) > 4 else '⊣'
            )
            phrase_categories['frammenti_codice'].append(code_fragment)
        
        # 3. Logica simbolica
        print("🔸 Generando logica simbolica...")
        logic_templates = [
            "{s1} ⊢ {s2}",
            "{s1} ∧ {s2} → {s3}",
            "¬{s1} ∨ {s2}",
            "{s1} ↔ {s2}",
            "∀x({s1}(x) → {s2}(x))",
            "∃y({s1}(y) ∧ {s2}(y))",
            "({s1} ∧ {s2}) → {s3}",
            "{s1} ⊕ {s2} ≡ {s3}"
        ]
        
        for template in logic_templates:
            symbols_needed = template.count('{s')
            selected_symbols = random.sample(self.symbols, symbols_needed)
            logic_expr = template.format(
                s1=selected_symbols[0] if len(selected_symbols) > 0 else '⟨',
                s2=selected_symbols[1] if len(selected_symbols) > 1 else '⟩',
                s3=selected_symbols[2] if len(selected_symbols) > 2 else '◊'
            )
            phrase_categories['logica_simbolica'].append(logic_expr)
        
        # 4. Testo naturale con simboli
        print("🔸 Generando testo naturale...")
        natural_templates = [
            "The function {s1} processes {s2} data efficiently",
            "When {s1} occurs, the system executes {s2}",
            "The {s1} algorithm uses {s2} optimization techniques",
            "Create a {s1} that handles {s2} operations",
            "The {s1} operator combines {s2} with {s3}",
            "Given {s1}, we can deduce {s2}",
            "The relationship between {s1} and {s2} is {s3}",
            "Processing {s1} requires {s2} validation"
        ]
        
        for template in natural_templates:
            symbols_needed = template.count('{s')
            selected_symbols = random.sample(self.symbols, symbols_needed)
            natural_text = template.format(
                s1=selected_symbols[0] if len(selected_symbols) > 0 else '⟨',
                s2=selected_symbols[1] if len(selected_symbols) > 1 else '⟩',
                s3=selected_symbols[2] if len(selected_symbols) > 2 else '◊'
            )
            phrase_categories['testo_naturale'].append(natural_text)
        
        # 5. Markup NEUROGLYPH
        print("🔸 Generando markup NEUROGLYPH...")
        markup_templates = [
            "<NG_START> {s1} {s2} {s3} <NG_END>",
            "<NG_THINK> {s1} ⊢ {s2} </NG_THINK>",
            "<NG_REASON> {s1} ∧ {s2} → {s3} </NG_REASON>",
            "<NG_MEMORY> {s1} stored as {s2} </NG_MEMORY>",
            "<NG_VALIDATE> {s1} == {s2} </NG_VALIDATE>",
            "<NG_ERROR> {s1} failed validation </NG_ERROR>",
            "<NG_CORRECT> {s1} → {s2} </NG_CORRECT>"
        ]
        
        for template in markup_templates:
            symbols_needed = template.count('{s')
            selected_symbols = random.sample(self.symbols, symbols_needed)
            markup_text = template.format(
                s1=selected_symbols[0] if len(selected_symbols) > 0 else '⟨',
                s2=selected_symbols[1] if len(selected_symbols) > 1 else '⟩',
                s3=selected_symbols[2] if len(selected_symbols) > 2 else '◊'
            )
            phrase_categories['markup_neuroglyph'].append(markup_text)
        
        # 6. Casi edge
        print("🔸 Generando casi edge...")
        edge_cases = []
        for i in range(10):
            symbol = random.choice(self.symbols)
            edge_cases.extend([
                f" {symbol} ",      # Spazi attorno
                f"\t{symbol}\n",    # Tab e newline
                f"{symbol},",       # Con virgola
                f"({symbol})",      # Tra parentesi
                f"[{symbol}]",      # Tra quadre
                f"{{{symbol}}}",    # Tra graffe
                f'"{symbol}"',      # Tra virgolette
                f"{symbol}{symbol}", # Simboli consecutivi
            ])
        phrase_categories['casi_edge'] = edge_cases
        
        # 7. Sequenze miste
        print("🔸 Generando sequenze miste...")
        mixed_sequences = []
        for i in range(15):
            symbols_seq = random.sample(self.symbols, 5)
            mixed_sequences.extend([
                f"{symbols_seq[0]} {symbols_seq[1]} {symbols_seq[2]}",
                f"{symbols_seq[0]}→{symbols_seq[1]}→{symbols_seq[2]}",
                f"process({symbols_seq[0]}, {symbols_seq[1]})",
                f"if {symbols_seq[0]} then {symbols_seq[1]} else {symbols_seq[2]}",
                f"def func_{symbols_seq[0]}(): return {symbols_seq[1]}"
            ])
        phrase_categories['sequenze_miste'] = mixed_sequences
        
        # Statistiche
        total_phrases = sum(len(phrases) for phrases in phrase_categories.values())
        unique_symbols_used = set()
        
        for phrases in phrase_categories.values():
            for phrase in phrases:
                for symbol in self.symbols:
                    if symbol in phrase:
                        unique_symbols_used.add(symbol)
        
        results = {
            'phrase_categories': phrase_categories,
            'statistics': {
                'total_phrases': total_phrases,
                'categories_count': len(phrase_categories),
                'unique_symbols_used': len(unique_symbols_used),
                'symbol_coverage_percentage': (len(unique_symbols_used) / len(self.symbols)) * 100
            }
        }
        
        print(f"\n📊 RISULTATI 2.3.1:")
        print(f"   - Frasi totali generate: {total_phrases}")
        print(f"   - Categorie: {len(phrase_categories)}")
        for category, phrases in phrase_categories.items():
            print(f"     • {category}: {len(phrases)} frasi")
        print(f"   - Simboli unici utilizzati: {len(unique_symbols_used)}/{len(self.symbols)} ({results['statistics']['symbol_coverage_percentage']:.1f}%)")
        
        return results
    
    def step_2_3_2_verify_zero_splitting(self, test_phrases: Dict[str, Any]) -> Dict[str, Any]:
        """
        2.3.2 - Verifica zero-splitting: una unità per ogni simbolo.
        
        Args:
            test_phrases: Frasi da testare dalla fase 2.3.1
            
        Returns:
            Risultati dettagliati del test zero-splitting
        """
        print("\n" + "="*80)
        print("🔍 FASE 2.3.2 - VERIFICA ZERO-SPLITTING (UNA UNITÀ PER SIMBOLO)")
        print("="*80)
        
        results = {
            'symbol_analysis': {},
            'phrase_analysis': {},
            'splitting_violations': [],
            'perfect_splits': [],
            'statistics': {}
        }
        
        # Test 1: Analisi simboli individuali
        print("🔸 Test 1: Analisi simboli individuali...")
        symbol_results = {}
        
        for i, symbol in enumerate(self.symbols):
            if i % 1000 == 0:
                print(f"   Testando simbolo {i+1}/{len(self.symbols)}...")
            
            tokens = self.tokenizer.tokenize(symbol)
            token_ids = self.tokenizer.encode(symbol, add_special_tokens=False)
            
            symbol_results[symbol] = {
                'tokens': tokens,
                'token_count': len(tokens),
                'token_ids': token_ids,
                'is_zero_split': len(tokens) == 1 and tokens[0] == symbol,
                'in_vocabulary': symbol in self.tokenizer.get_vocab()
            }
            
            if not symbol_results[symbol]['is_zero_split']:
                results['splitting_violations'].append({
                    'symbol': symbol,
                    'tokens': tokens,
                    'expected': 1,
                    'actual': len(tokens),
                    'issue_type': 'symbol_splitting'
                })
            else:
                results['perfect_splits'].append(symbol)
        
        results['symbol_analysis'] = symbol_results
        
        # Test 2: Analisi frasi per categoria
        print("🔸 Test 2: Analisi frasi per categoria...")
        phrase_results = {}
        
        for category, phrases in test_phrases['phrase_categories'].items():
            print(f"   Testando categoria: {category} ({len(phrases)} frasi)")
            
            category_results = {
                'total_phrases': len(phrases),
                'perfect_tokenization': 0,
                'problematic_phrases': [],
                'symbol_preservation': 0,
                'total_symbols_in_phrases': 0
            }
            
            for phrase in phrases:
                tokens = self.tokenizer.tokenize(phrase)
                
                # Conta simboli nella frase
                symbols_in_phrase = []
                for symbol in self.symbols:
                    if symbol in phrase:
                        symbols_in_phrase.append(symbol)
                        category_results['total_symbols_in_phrases'] += 1
                
                # Verifica preservazione simboli
                symbols_preserved = 0
                for symbol in symbols_in_phrase:
                    if symbol in tokens:
                        symbols_preserved += 1
                
                category_results['symbol_preservation'] += symbols_preserved
                
                # Verifica problemi di tokenizzazione
                has_issues = False
                issues = []
                
                for symbol in symbols_in_phrase:
                    if symbol not in tokens:
                        has_issues = True
                        issues.append(f"Simbolo {symbol} non preservato")
                
                if '<UNK>' in tokens:
                    has_issues = True
                    issues.append("Token <UNK> presente")
                
                if not has_issues:
                    category_results['perfect_tokenization'] += 1
                else:
                    category_results['problematic_phrases'].append({
                        'phrase': phrase,
                        'tokens': tokens,
                        'issues': issues,
                        'symbols_in_phrase': symbols_in_phrase
                    })
            
            phrase_results[category] = category_results
        
        results['phrase_analysis'] = phrase_results
        
        # Calcola statistiche finali
        total_symbols_tested = len(self.symbols)
        perfect_symbols = len(results['perfect_splits'])
        violation_symbols = len(results['splitting_violations'])
        
        total_phrases_tested = sum(cat['total_phrases'] for cat in phrase_results.values())
        perfect_phrases = sum(cat['perfect_tokenization'] for cat in phrase_results.values())
        
        results['statistics'] = {
            'symbol_zero_split_percentage': (perfect_symbols / total_symbols_tested) * 100,
            'symbol_violations': violation_symbols,
            'phrase_perfect_percentage': (perfect_phrases / total_phrases_tested) * 100 if total_phrases_tested > 0 else 0,
            'total_symbols_tested': total_symbols_tested,
            'total_phrases_tested': total_phrases_tested
        }
        
        print(f"\n📊 RISULTATI 2.3.2:")
        print(f"   - Simboli testati: {total_symbols_tested}")
        print(f"   - Zero-splitting perfetto: {perfect_symbols} ({results['statistics']['symbol_zero_split_percentage']:.1f}%)")
        print(f"   - Violazioni zero-splitting: {violation_symbols}")
        print(f"   - Frasi testate: {total_phrases_tested}")
        print(f"   - Frasi con tokenizzazione perfetta: {perfect_phrases} ({results['statistics']['phrase_perfect_percentage']:.1f}%)")
        
        if violation_symbols > 0:
            print(f"\n⚠️  VIOLAZIONI RILEVATE:")
            for violation in results['splitting_violations'][:10]:  # Mostra prime 10
                print(f"     • {violation['symbol']} → {violation['tokens']} ({violation['actual']} token)")
        
        return results

    def step_2_3_3_verify_detokenization(self, test_phrases: Dict[str, Any]) -> Dict[str, Any]:
        """
        2.3.3 - Verifica detokenizzazione: ogni token si traduce perfettamente nel simbolo originale.

        Args:
            test_phrases: Frasi da testare

        Returns:
            Risultati dettagliati del test detokenizzazione
        """
        print("\n" + "="*80)
        print("🔄 FASE 2.3.3 - VERIFICA DETOKENIZZAZIONE PERFETTA")
        print("="*80)

        results = {
            'roundtrip_tests': {},
            'perfect_roundtrips': [],
            'failed_roundtrips': [],
            'symbol_roundtrip_analysis': {},
            'statistics': {}
        }

        # Test 1: Roundtrip simboli individuali
        print("🔸 Test 1: Roundtrip simboli individuali...")
        symbol_roundtrip = {}
        perfect_symbol_roundtrips = 0

        sample_symbols = random.sample(self.symbols, min(200, len(self.symbols)))

        for symbol in sample_symbols:
            # Encode → Decode
            token_ids = self.tokenizer.encode(symbol, add_special_tokens=False)
            decoded = self.tokenizer.decode(token_ids, skip_special_tokens=False)

            is_perfect = symbol == decoded

            symbol_roundtrip[symbol] = {
                'original': symbol,
                'token_ids': token_ids,
                'decoded': decoded,
                'is_perfect': is_perfect,
                'length_original': len(symbol),
                'length_decoded': len(decoded)
            }

            if is_perfect:
                perfect_symbol_roundtrips += 1
                results['perfect_roundtrips'].append(symbol)
            else:
                results['failed_roundtrips'].append({
                    'type': 'symbol',
                    'original': symbol,
                    'decoded': decoded,
                    'tokens': self.tokenizer.tokenize(symbol)
                })

        results['symbol_roundtrip_analysis'] = symbol_roundtrip

        # Test 2: Roundtrip frasi per categoria
        print("🔸 Test 2: Roundtrip frasi per categoria...")
        phrase_roundtrip = {}

        for category, phrases in test_phrases['phrase_categories'].items():
            print(f"   Testando categoria: {category}")

            category_results = {
                'total_tested': len(phrases),
                'perfect_roundtrips': 0,
                'failed_roundtrips': [],
                'partial_failures': [],
                'encoding_errors': []
            }

            for phrase in phrases:
                try:
                    # Test con e senza special tokens
                    for add_special in [False, True]:
                        token_ids = self.tokenizer.encode(phrase, add_special_tokens=add_special)
                        decoded = self.tokenizer.decode(token_ids, skip_special_tokens=not add_special)

                        # Normalizza per confronto
                        original_norm = ' '.join(phrase.split())
                        decoded_norm = ' '.join(decoded.split())

                        is_perfect = original_norm == decoded_norm

                        if is_perfect:
                            category_results['perfect_roundtrips'] += 1
                            break  # Almeno una versione funziona
                    else:
                        # Nessuna versione ha funzionato
                        tokens = self.tokenizer.tokenize(phrase)
                        token_ids_no_special = self.tokenizer.encode(phrase, add_special_tokens=False)
                        decoded_no_special = self.tokenizer.decode(token_ids_no_special, skip_special_tokens=False)

                        failure_info = {
                            'phrase': phrase,
                            'tokens': tokens,
                            'decoded': decoded_no_special,
                            'original_length': len(phrase),
                            'decoded_length': len(decoded_no_special),
                            'has_unk': '<UNK>' in tokens
                        }

                        if abs(len(phrase) - len(decoded_no_special)) <= 2:
                            category_results['partial_failures'].append(failure_info)
                        else:
                            category_results['failed_roundtrips'].append(failure_info)

                except Exception as e:
                    category_results['encoding_errors'].append({
                        'phrase': phrase,
                        'error': str(e)
                    })

            phrase_roundtrip[category] = category_results

        results['roundtrip_tests'] = phrase_roundtrip

        # Calcola statistiche
        total_symbol_tests = len(sample_symbols)
        symbol_success_rate = (perfect_symbol_roundtrips / total_symbol_tests) * 100

        total_phrase_tests = sum(cat['total_tested'] for cat in phrase_roundtrip.values())
        total_phrase_success = sum(cat['perfect_roundtrips'] for cat in phrase_roundtrip.values())
        phrase_success_rate = (total_phrase_success / total_phrase_tests) * 100 if total_phrase_tests > 0 else 0

        results['statistics'] = {
            'symbol_roundtrip_percentage': symbol_success_rate,
            'phrase_roundtrip_percentage': phrase_success_rate,
            'total_symbol_tests': total_symbol_tests,
            'total_phrase_tests': total_phrase_tests,
            'perfect_symbol_roundtrips': perfect_symbol_roundtrips,
            'perfect_phrase_roundtrips': total_phrase_success
        }

        print(f"\n📊 RISULTATI 2.3.3:")
        print(f"   - Simboli testati: {total_symbol_tests}")
        print(f"   - Roundtrip simboli perfetti: {perfect_symbol_roundtrips} ({symbol_success_rate:.1f}%)")
        print(f"   - Frasi testate: {total_phrase_tests}")
        print(f"   - Roundtrip frasi perfetti: {total_phrase_success} ({phrase_success_rate:.1f}%)")
        print(f"   - Fallimenti totali: {len(results['failed_roundtrips'])}")

        return results

    def step_2_3_4_security_test_100_random(self) -> Dict[str, Any]:
        """
        2.3.4 - Test di sicurezza: tokenizza e detokenizza 100 esempi casuali.

        Returns:
            Risultati dettagliati del test di sicurezza
        """
        print("\n" + "="*80)
        print("🛡️  FASE 2.3.4 - TEST DI SICUREZZA (100 ESEMPI CASUALI)")
        print("="*80)

        results = {
            'random_samples': [],
            'perfect_equivalences': [],
            'failed_equivalences': [],
            'anomalies': [],
            'statistics': {},
            'security_issues': []
        }

        print("🔸 Generazione 100 esempi casuali...")

        # Genera 100 esempi casuali diversificati
        random_samples = []

        for i in range(100):
            sample_type = random.choice([
                'pure_symbols', 'mixed_text', 'code_fragment',
                'natural_language', 'edge_case', 'markup'
            ])

            if sample_type == 'pure_symbols':
                # Solo simboli
                num_symbols = random.randint(1, 8)
                symbols = random.sample(self.symbols, num_symbols)
                sample = ' '.join(symbols)

            elif sample_type == 'mixed_text':
                # Mix simboli e testo
                words = ['function', 'process', 'data', 'result', 'value', 'item']
                symbols = random.sample(self.symbols, 3)
                sample = f"{random.choice(words)} {symbols[0]} {random.choice(words)} {symbols[1]} {symbols[2]}"

            elif sample_type == 'code_fragment':
                # Frammento di codice
                symbols = random.sample(self.symbols, 2)
                templates = [
                    f"def {symbols[0]}_func(): return {symbols[1]}",
                    f"if {symbols[0]} == {symbols[1]}: process()",
                    f"for item in {symbols[0]}: yield {symbols[1]}",
                    f"class {symbols[0]}: def __init__(self): self.{symbols[1]} = None"
                ]
                sample = random.choice(templates)

            elif sample_type == 'natural_language':
                # Linguaggio naturale con simboli
                symbols = random.sample(self.symbols, 2)
                templates = [
                    f"The {symbols[0]} algorithm processes {symbols[1]} efficiently",
                    f"When {symbols[0]} occurs, execute {symbols[1]}",
                    f"The relationship between {symbols[0]} and {symbols[1]} is complex"
                ]
                sample = random.choice(templates)

            elif sample_type == 'edge_case':
                # Casi edge
                symbol = random.choice(self.symbols)
                edge_cases = [
                    f" {symbol} ",
                    f"\t{symbol}\n",
                    f"{symbol},{symbol}",
                    f"({symbol})",
                    f"[{symbol}]",
                    f"{{{symbol}}}",
                    f'"{symbol}"',
                    f"{symbol}{symbol}{symbol}"
                ]
                sample = random.choice(edge_cases)

            else:  # markup
                # Markup NEUROGLYPH
                symbols = random.sample(self.symbols, 2)
                markups = [
                    f"<NG_START> {symbols[0]} {symbols[1]} <NG_END>",
                    f"<NG_THINK> {symbols[0]} ⊢ {symbols[1]} </NG_THINK>",
                    f"<NG_REASON> {symbols[0]} ∧ {symbols[1]} </NG_REASON>"
                ]
                sample = random.choice(markups)

            random_samples.append({
                'id': i + 1,
                'type': sample_type,
                'content': sample
            })

        results['random_samples'] = random_samples

        print("🔸 Test equivalenza tokenizzazione/detokenizzazione...")

        # Test ogni campione
        for sample_info in random_samples:
            sample = sample_info['content']
            sample_id = sample_info['id']

            try:
                # Test equivalenza
                start_time = time.time()

                # Tokenizza
                tokens = self.tokenizer.tokenize(sample)
                token_ids = self.tokenizer.encode(sample, add_special_tokens=False)

                # Detokenizza
                decoded = self.tokenizer.decode(token_ids, skip_special_tokens=False)

                encoding_time = time.time() - start_time

                # Verifica equivalenza
                original_norm = ' '.join(sample.split())
                decoded_norm = ' '.join(decoded.split())
                is_equivalent = original_norm == decoded_norm

                test_result = {
                    'sample_id': sample_id,
                    'original': sample,
                    'tokens': tokens,
                    'token_count': len(tokens),
                    'decoded': decoded,
                    'is_equivalent': is_equivalent,
                    'encoding_time_ms': encoding_time * 1000,
                    'character_count': len(sample),
                    'compression_ratio': len(sample) / len(tokens) if len(tokens) > 0 else 0
                }

                if is_equivalent:
                    results['perfect_equivalences'].append(test_result)
                else:
                    results['failed_equivalences'].append(test_result)

                # Rileva anomalie
                if len(tokens) > len(sample) * 2:
                    results['anomalies'].append({
                        'type': 'over_tokenization',
                        'sample_id': sample_id,
                        'sample': sample,
                        'token_count': len(tokens),
                        'char_count': len(sample),
                        'ratio': len(tokens) / len(sample)
                    })

                if '<UNK>' in tokens:
                    results['security_issues'].append({
                        'type': 'unknown_token',
                        'sample_id': sample_id,
                        'sample': sample,
                        'tokens': tokens
                    })

                if encoding_time > 0.1:  # Più di 100ms
                    results['anomalies'].append({
                        'type': 'slow_encoding',
                        'sample_id': sample_id,
                        'sample': sample,
                        'encoding_time_ms': encoding_time * 1000
                    })

                if any(len(token) > 50 for token in tokens):
                    results['anomalies'].append({
                        'type': 'oversized_token',
                        'sample_id': sample_id,
                        'sample': sample,
                        'oversized_tokens': [t for t in tokens if len(t) > 50]
                    })

            except Exception as e:
                results['failed_equivalences'].append({
                    'sample_id': sample_id,
                    'original': sample,
                    'error': str(e),
                    'is_equivalent': False
                })

                results['security_issues'].append({
                    'type': 'encoding_error',
                    'sample_id': sample_id,
                    'sample': sample,
                    'error': str(e)
                })

        # Calcola statistiche
        total_tests = len(random_samples)
        perfect_count = len(results['perfect_equivalences'])
        failed_count = len(results['failed_equivalences'])

        equivalence_rate = (perfect_count / total_tests) * 100

        # Statistiche temporali
        encoding_times = [r['encoding_time_ms'] for r in results['perfect_equivalences'] if 'encoding_time_ms' in r]
        avg_encoding_time = sum(encoding_times) / len(encoding_times) if encoding_times else 0

        # Statistiche compressione
        compression_ratios = [r['compression_ratio'] for r in results['perfect_equivalences'] if 'compression_ratio' in r]
        avg_compression = sum(compression_ratios) / len(compression_ratios) if compression_ratios else 0

        results['statistics'] = {
            'total_tests': total_tests,
            'perfect_equivalences': perfect_count,
            'failed_equivalences': failed_count,
            'equivalence_percentage': equivalence_rate,
            'anomalies_count': len(results['anomalies']),
            'security_issues_count': len(results['security_issues']),
            'avg_encoding_time_ms': avg_encoding_time,
            'avg_compression_ratio': avg_compression
        }

        print(f"\n📊 RISULTATI 2.3.4:")
        print(f"   - Campioni testati: {total_tests}")
        print(f"   - Equivalenza perfetta: {perfect_count} ({equivalence_rate:.1f}%)")
        print(f"   - Equivalenza fallita: {failed_count}")
        print(f"   - Anomalie rilevate: {len(results['anomalies'])}")
        print(f"   - Problemi di sicurezza: {len(results['security_issues'])}")
        print(f"   - Tempo medio encoding: {avg_encoding_time:.2f}ms")
        print(f"   - Rapporto compressione medio: {avg_compression:.2f}")

        if results['security_issues']:
            print(f"\n⚠️  PROBLEMI DI SICUREZZA RILEVATI:")
            issue_types = defaultdict(int)
            for issue in results['security_issues']:
                issue_types[issue['type']] += 1

            for issue_type, count in issue_types.items():
                print(f"     • {issue_type}: {count} casi")

        return results

    def step_2_3_5_anomaly_detection_and_fixes(self,
                                              zero_split_results: Dict[str, Any],
                                              detokenization_results: Dict[str, Any],
                                              security_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        2.3.5 - Rilevamento anomalie e raccomandazioni per correzioni.

        Args:
            zero_split_results: Risultati test zero-splitting
            detokenization_results: Risultati test detokenizzazione
            security_results: Risultati test sicurezza

        Returns:
            Analisi anomalie e raccomandazioni
        """
        print("\n" + "="*80)
        print("🔧 FASE 2.3.5 - RILEVAMENTO ANOMALIE E RACCOMANDAZIONI")
        print("="*80)

        results = {
            'anomalies_detected': [],
            'critical_issues': [],
            'recommendations': [],
            'tokenizer_health_score': 0,
            'required_actions': [],
            'optional_improvements': []
        }

        print("🔸 Analisi anomalie rilevate...")

        # 1. Analisi violazioni zero-splitting
        splitting_violations = zero_split_results.get('splitting_violations', [])
        if splitting_violations:
            critical_issue = {
                'type': 'zero_splitting_violations',
                'severity': 'CRITICAL',
                'count': len(splitting_violations),
                'description': f"{len(splitting_violations)} simboli non rispettano zero-splitting",
                'examples': splitting_violations[:5],
                'impact': 'Simboli divisi compromettono l\'integrità semantica'
            }
            results['critical_issues'].append(critical_issue)
            results['anomalies_detected'].append(critical_issue)

            # Raccomandazione specifica
            results['required_actions'].append({
                'action': 'fix_splitting_violations',
                'priority': 'HIGH',
                'description': 'Aggiungere simboli problematici come AddedToken',
                'implementation': 'tokenizer.add_tokens([AddedToken(symbol, single_word=True) for symbol in problematic_symbols])'
            })

        # 2. Analisi fallimenti roundtrip
        failed_roundtrips = detokenization_results.get('failed_roundtrips', [])
        if failed_roundtrips:
            roundtrip_issue = {
                'type': 'roundtrip_failures',
                'severity': 'HIGH' if len(failed_roundtrips) > 10 else 'MEDIUM',
                'count': len(failed_roundtrips),
                'description': f"{len(failed_roundtrips)} test roundtrip falliti",
                'examples': failed_roundtrips[:3],
                'impact': 'Perdita di informazioni durante encoding/decoding'
            }
            results['anomalies_detected'].append(roundtrip_issue)

            if len(failed_roundtrips) > 10:
                results['critical_issues'].append(roundtrip_issue)
                results['required_actions'].append({
                    'action': 'fix_roundtrip_failures',
                    'priority': 'HIGH',
                    'description': 'Rivedere post-processor e gestione spazi',
                    'implementation': 'Configurare clean_up_tokenization_spaces=False'
                })
            else:
                results['optional_improvements'].append({
                    'action': 'improve_roundtrip',
                    'priority': 'MEDIUM',
                    'description': 'Ottimizzare casi edge per roundtrip perfetto'
                })

        # 3. Analisi problemi di sicurezza
        security_issues = security_results.get('security_issues', [])
        if security_issues:
            security_issue = {
                'type': 'security_vulnerabilities',
                'severity': 'HIGH',
                'count': len(security_issues),
                'description': f"{len(security_issues)} problemi di sicurezza rilevati",
                'issue_types': defaultdict(int),
                'impact': 'Potenziali vulnerabilità o comportamenti imprevisti'
            }

            for issue in security_issues:
                security_issue['issue_types'][issue['type']] += 1

            results['anomalies_detected'].append(security_issue)

            if len(security_issues) > 5:
                results['critical_issues'].append(security_issue)
                results['required_actions'].append({
                    'action': 'address_security_issues',
                    'priority': 'HIGH',
                    'description': 'Risolvere problemi di sicurezza identificati',
                    'details': dict(security_issue['issue_types'])
                })

        # 4. Analisi performance
        avg_encoding_time = security_results.get('statistics', {}).get('avg_encoding_time_ms', 0)
        if avg_encoding_time > 10:  # Più di 10ms è lento
            performance_issue = {
                'type': 'performance_degradation',
                'severity': 'MEDIUM',
                'avg_time_ms': avg_encoding_time,
                'description': f"Encoding lento: {avg_encoding_time:.2f}ms medio",
                'impact': 'Performance sub-ottimali per uso in produzione'
            }
            results['anomalies_detected'].append(performance_issue)
            results['optional_improvements'].append({
                'action': 'optimize_performance',
                'priority': 'MEDIUM',
                'description': 'Ottimizzare velocità di encoding',
                'target': 'Ridurre tempo medio sotto 5ms'
            })

        # 5. Analisi copertura simboli
        symbol_coverage = zero_split_results.get('statistics', {}).get('symbol_zero_split_percentage', 0)
        if symbol_coverage < 95:
            coverage_issue = {
                'type': 'insufficient_symbol_coverage',
                'severity': 'CRITICAL',
                'coverage_percentage': symbol_coverage,
                'description': f"Copertura simboli insufficiente: {symbol_coverage:.1f}%",
                'impact': 'Molti simboli non gestiti correttamente'
            }
            results['critical_issues'].append(coverage_issue)
            results['anomalies_detected'].append(coverage_issue)
            results['required_actions'].append({
                'action': 'improve_symbol_coverage',
                'priority': 'CRITICAL',
                'description': 'Aumentare copertura simboli oltre 95%',
                'target': '100% copertura simboli'
            })

        # 6. Calcola health score
        base_score = 100

        # Penalità per problemi critici
        base_score -= len(results['critical_issues']) * 20

        # Penalità per violazioni zero-splitting
        if splitting_violations:
            violation_penalty = min(30, len(splitting_violations) * 0.5)
            base_score -= violation_penalty

        # Penalità per fallimenti roundtrip
        if failed_roundtrips:
            roundtrip_penalty = min(20, len(failed_roundtrips) * 2)
            base_score -= roundtrip_penalty

        # Penalità per problemi sicurezza
        if security_issues:
            security_penalty = min(15, len(security_issues) * 1.5)
            base_score -= security_penalty

        # Bonus per performance buone
        if avg_encoding_time < 5:
            base_score += 5

        results['tokenizer_health_score'] = max(0, base_score)

        # 7. Genera raccomandazioni generali
        if results['tokenizer_health_score'] >= 90:
            results['recommendations'].append({
                'level': 'EXCELLENT',
                'message': 'Tokenizer in condizioni eccellenti, pronto per produzione',
                'actions': ['Monitoraggio continuo', 'Test periodici']
            })
        elif results['tokenizer_health_score'] >= 75:
            results['recommendations'].append({
                'level': 'GOOD',
                'message': 'Tokenizer in buone condizioni, miglioramenti minori raccomandati',
                'actions': ['Implementare miglioramenti opzionali', 'Test aggiuntivi']
            })
        elif results['tokenizer_health_score'] >= 50:
            results['recommendations'].append({
                'level': 'NEEDS_IMPROVEMENT',
                'message': 'Tokenizer necessita miglioramenti significativi',
                'actions': ['Implementare azioni richieste', 'Re-training parziale']
            })
        else:
            results['recommendations'].append({
                'level': 'CRITICAL',
                'message': 'Tokenizer in condizioni critiche, re-training necessario',
                'actions': ['Re-training completo', 'Revisione architettura']
            })

        print(f"\n📊 RISULTATI 2.3.5:")
        print(f"   - Anomalie rilevate: {len(results['anomalies_detected'])}")
        print(f"   - Problemi critici: {len(results['critical_issues'])}")
        print(f"   - Azioni richieste: {len(results['required_actions'])}")
        print(f"   - Miglioramenti opzionali: {len(results['optional_improvements'])}")
        print(f"   - Health Score: {results['tokenizer_health_score']}/100")

        if results['critical_issues']:
            print(f"\n🚨 PROBLEMI CRITICI:")
            for issue in results['critical_issues']:
                print(f"     • {issue['type']}: {issue['description']}")

        if results['required_actions']:
            print(f"\n🔧 AZIONI RICHIESTE:")
            for action in results['required_actions']:
                print(f"     • {action['action']} (Priorità: {action['priority']})")
                print(f"       {action['description']}")

        return results

    def generate_comprehensive_report(self, all_results: Dict[str, Any]) -> str:
        """Genera report completo di tutte le fasi 2.3."""

        report = []
        report.append("# NEUROGLYPH v2.0 - Report Dettagliato Fase 2.3")
        report.append("=" * 80)
        report.append("")

        # Sommario esecutivo
        health_score = all_results.get('step_2_3_5', {}).get('tokenizer_health_score', 0)

        if health_score >= 90:
            status = "🎊 ECCELLENTE"
        elif health_score >= 75:
            status = "✅ BUONO"
        elif health_score >= 50:
            status = "⚠️ NECESSITA MIGLIORAMENTI"
        else:
            status = "🚨 CRITICO"

        report.append(f"## {status} - Health Score: {health_score}/100")
        report.append("")

        # Risultati per fase
        for phase in ['step_2_3_1', 'step_2_3_2', 'step_2_3_3', 'step_2_3_4', 'step_2_3_5']:
            if phase in all_results:
                phase_data = all_results[phase]

                if phase == 'step_2_3_1':
                    report.append("## 📋 FASE 2.3.1 - FRASI-TIPO PREPARATE")
                    stats = phase_data.get('statistics', {})
                    report.append(f"- **Frasi totali**: {stats.get('total_phrases', 0)}")
                    report.append(f"- **Categorie**: {stats.get('categories_count', 0)}")
                    report.append(f"- **Simboli utilizzati**: {stats.get('unique_symbols_used', 0)}")
                    report.append(f"- **Copertura simboli**: {stats.get('symbol_coverage_percentage', 0):.1f}%")

                elif phase == 'step_2_3_2':
                    report.append("## 🔍 FASE 2.3.2 - ZERO-SPLITTING")
                    stats = phase_data.get('statistics', {})
                    report.append(f"- **Zero-splitting**: {stats.get('symbol_zero_split_percentage', 0):.1f}%")
                    report.append(f"- **Violazioni**: {stats.get('symbol_violations', 0)}")
                    report.append(f"- **Frasi perfette**: {stats.get('phrase_perfect_percentage', 0):.1f}%")

                elif phase == 'step_2_3_3':
                    report.append("## 🔄 FASE 2.3.3 - DETOKENIZZAZIONE")
                    stats = phase_data.get('statistics', {})
                    report.append(f"- **Roundtrip simboli**: {stats.get('symbol_roundtrip_percentage', 0):.1f}%")
                    report.append(f"- **Roundtrip frasi**: {stats.get('phrase_roundtrip_percentage', 0):.1f}%")
                    report.append(f"- **Fallimenti**: {len(phase_data.get('failed_roundtrips', []))}")

                elif phase == 'step_2_3_4':
                    report.append("## 🛡️ FASE 2.3.4 - TEST SICUREZZA")
                    stats = phase_data.get('statistics', {})
                    report.append(f"- **Equivalenza**: {stats.get('equivalence_percentage', 0):.1f}%")
                    report.append(f"- **Anomalie**: {stats.get('anomalies_count', 0)}")
                    report.append(f"- **Problemi sicurezza**: {stats.get('security_issues_count', 0)}")
                    report.append(f"- **Tempo medio**: {stats.get('avg_encoding_time_ms', 0):.2f}ms")

                elif phase == 'step_2_3_5':
                    report.append("## 🔧 FASE 2.3.5 - ANOMALIE E RACCOMANDAZIONI")
                    report.append(f"- **Anomalie rilevate**: {len(phase_data.get('anomalies_detected', []))}")
                    report.append(f"- **Problemi critici**: {len(phase_data.get('critical_issues', []))}")
                    report.append(f"- **Azioni richieste**: {len(phase_data.get('required_actions', []))}")

                report.append("")

        # Raccomandazioni finali
        if 'step_2_3_5' in all_results:
            recommendations = all_results['step_2_3_5'].get('recommendations', [])
            if recommendations:
                report.append("## 🎯 RACCOMANDAZIONI FINALI")
                for rec in recommendations:
                    report.append(f"**{rec['level']}**: {rec['message']}")
                    for action in rec.get('actions', []):
                        report.append(f"- {action}")
                report.append("")

        return "\n".join(report)

    def run_complete_verification(self) -> Dict[str, Any]:
        """Esegue verifica completa di tutte le fasi 2.3."""

        print("🧠 NEUROGLYPH v2.0 - VERIFICA COMPLETA FASE 2.3")
        print("=" * 80)
        print("Esecuzione di tutti i test dettagliati per la fase 2.3...")
        print()

        all_results = {}

        # 2.3.1 - Preparazione frasi-tipo
        all_results['step_2_3_1'] = self.step_2_3_1_prepare_test_phrases()

        # 2.3.2 - Verifica zero-splitting
        all_results['step_2_3_2'] = self.step_2_3_2_verify_zero_splitting(all_results['step_2_3_1'])

        # 2.3.3 - Verifica detokenizzazione
        all_results['step_2_3_3'] = self.step_2_3_3_verify_detokenization(all_results['step_2_3_1'])

        # 2.3.4 - Test sicurezza
        all_results['step_2_3_4'] = self.step_2_3_4_security_test_100_random()

        # 2.3.5 - Analisi anomalie
        all_results['step_2_3_5'] = self.step_2_3_5_anomaly_detection_and_fixes(
            all_results['step_2_3_2'],
            all_results['step_2_3_3'],
            all_results['step_2_3_4']
        )

        # Genera report completo
        report = self.generate_comprehensive_report(all_results)

        # Salva report
        report_path = Path("training/tokenizer/detailed_verification_2_3_report.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"\n📄 Report completo salvato: {report_path}")

        # Risultato finale
        health_score = all_results.get('step_2_3_5', {}).get('tokenizer_health_score', 0)

        print(f"\n🎯 RISULTATO FINALE FASE 2.3:")
        print(f"   - Health Score: {health_score}/100")

        if health_score >= 90:
            print("🎊 FASE 2.3 COMPLETATA CON SUCCESSO!")
            print("✅ Tokenizer validato e pronto per produzione")
        elif health_score >= 75:
            print("✅ FASE 2.3 COMPLETATA CON BUONI RISULTATI")
            print("⚠️ Alcuni miglioramenti raccomandati")
        else:
            print("⚠️ FASE 2.3 COMPLETATA CON PROBLEMI")
            print("🔧 Azioni correttive necessarie")

        return all_results

def main():
    """Funzione principale."""
    verifier = DetailedVerification23()
    results = verifier.run_complete_verification()
    return results

if __name__ == "__main__":
    main()
