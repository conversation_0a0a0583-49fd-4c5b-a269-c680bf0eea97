#!/usr/bin/env python3
"""
Correttore Tokenizer NEUROGLYPH v2.0

Corregge il tokenizer per garantire:
1. Simboli come token normali (non speciali)
2. Roundtrip fidelity perfetta
3. Zero-splitting mantenuto
4. Compatibilità con training LLM
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Set
from tokenizers import Tokenizer, models, normalizers, pre_tokenizers, processors, trainers
from tokenizers.models import BPE
from tokenizers.trainers import BpeTrainer
from tokenizers.pre_tokenizers import Whitespace, Sequence
from tokenizers.processors import TemplateProcessing
from transformers import PreTrainedTokenizerFast

class TokenizerFixer:
    """Correttore per tokenizer NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il correttore.
        
        Args:
            registry_path: Percorso al registry simbolico
        """
        self.registry_path = registry_path
        self.symbols = []
        self._load_symbols()
    
    def _load_symbols(self) -> None:
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbols.append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def create_fixed_tokenizer(self, 
                              corpus_path: str = "training/tokenizer/neuroglyph_corpus.txt",
                              vocab_size: int = 34000,
                              output_dir: str = "training/tokenizer/neuroglyph_tokenizer_fixed") -> str:
        """
        Crea tokenizer corretto con simboli come token normali.
        
        Args:
            corpus_path: Percorso al corpus
            vocab_size: Dimensione vocabolario
            output_dir: Directory output
            
        Returns:
            Percorso al tokenizer corretto
        """
        print("🔧 Creazione tokenizer corretto...")
        
        # 1. Inizializza tokenizer BPE
        tokenizer = Tokenizer(BPE(unk_token="<UNK>"))
        
        # 2. Normalizzazione minima
        tokenizer.normalizer = normalizers.Sequence([
            normalizers.NFD(),
            normalizers.StripAccents()
        ])
        
        # 3. Pre-tokenizer che preserva simboli
        tokenizer.pre_tokenizer = pre_tokenizers.Sequence([
            pre_tokenizers.Whitespace(),
            pre_tokenizers.Punctuation()
        ])
        
        # 4. Solo token speciali essenziali (NON i simboli)
        essential_special_tokens = [
            "<UNK>",         # Token sconosciuto
            "<PAD>",         # Padding
            "<BOS>",         # Begin of sequence
            "<EOS>",         # End of sequence
            "<MASK>",        # Masked token
            "<NG_START>",    # Inizio neuroglyph
            "<NG_END>",      # Fine neuroglyph
            "<NG_THINK>",    # Modalità pensiero
            "<NG_REASON>",   # Ragionamento
            "<NG_MEMORY>",   # Memoria
            "<NG_VALIDATE>", # Validazione
            "<NG_ERROR>",    # Errore
            "<NG_CORRECT>"   # Correzione
        ]
        
        # 5. Trainer BPE con simboli nel corpus (non come special tokens)
        trainer = BpeTrainer(
            vocab_size=vocab_size,
            special_tokens=essential_special_tokens,  # Solo token speciali essenziali
            min_frequency=1,
            show_progress=True,
            continuing_subword_prefix="##"
        )
        
        # 6. Addestra tokenizer
        print(f"   - Training con {len(essential_special_tokens)} token speciali")
        print(f"   - Simboli NEUROGLYPH saranno appresi dal corpus")
        
        tokenizer.train([corpus_path], trainer)
        
        # 7. Post-processor semplice
        tokenizer.post_processor = TemplateProcessing(
            single="<BOS> $A <EOS>",
            pair="<BOS> $A <EOS> $B:1 <EOS>:1",
            special_tokens=[
                ("<BOS>", tokenizer.token_to_id("<BOS>")),
                ("<EOS>", tokenizer.token_to_id("<EOS>"))
            ]
        )
        
        # 8. Salva tokenizer
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        tokenizer.save(str(output_path / "tokenizer.json"))
        
        # 9. Crea tokenizer HF
        hf_tokenizer = PreTrainedTokenizerFast(
            tokenizer_object=tokenizer,
            unk_token="<UNK>",
            pad_token="<PAD>",
            bos_token="<BOS>",
            eos_token="<EOS>",
            mask_token="<MASK>"
        )
        
        # Salva tokenizer HF
        hf_tokenizer.save_pretrained(output_path)
        
        print(f"✅ Tokenizer corretto salvato in: {output_path}")
        
        return str(output_path)
    
    def validate_fixed_tokenizer(self, tokenizer_path: str) -> Dict[str, Any]:
        """Valida il tokenizer corretto."""
        print("🔍 Validazione tokenizer corretto...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        # Test simboli
        symbols_in_vocab = 0
        zero_split_symbols = 0
        
        for symbol in self.symbols[:100]:  # Test primi 100 simboli
            if symbol in tokenizer.get_vocab():
                symbols_in_vocab += 1
                
                # Test zero-splitting
                tokens = tokenizer.tokenize(symbol)
                if len(tokens) == 1 and tokens[0] == symbol:
                    zero_split_symbols += 1
        
        # Test roundtrip
        test_cases = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "Simple text without symbols"
        ]
        
        perfect_roundtrips = 0
        
        for test_case in test_cases:
            # Test senza special tokens
            token_ids = tokenizer.encode(test_case, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            if test_case == decoded:
                perfect_roundtrips += 1
        
        results = {
            'symbols_tested': 100,
            'symbols_in_vocab': symbols_in_vocab,
            'zero_split_symbols': zero_split_symbols,
            'roundtrip_tests': len(test_cases),
            'perfect_roundtrips': perfect_roundtrips,
            'symbol_coverage': (symbols_in_vocab / 100) * 100,
            'zero_split_percentage': (zero_split_symbols / 100) * 100,
            'roundtrip_percentage': (perfect_roundtrips / len(test_cases)) * 100
        }
        
        print(f"📊 Risultati Validazione:")
        print(f"   - Simboli in vocabolario: {symbols_in_vocab}/100 ({results['symbol_coverage']:.1f}%)")
        print(f"   - Zero-splitting: {zero_split_symbols}/100 ({results['zero_split_percentage']:.1f}%)")
        print(f"   - Roundtrip perfetti: {perfect_roundtrips}/{len(test_cases)} ({results['roundtrip_percentage']:.1f}%)")
        
        return results
    
    def test_fixed_tokenizer(self, tokenizer_path: str) -> None:
        """Testa il tokenizer corretto con esempi."""
        print("🧪 Test tokenizer corretto...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        test_examples = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "Simple text",
            "def func(): return result"
        ]
        
        for example in test_examples:
            # Test senza special tokens per vedere i simboli
            tokens = tokenizer.tokenize(example)
            token_ids = tokenizer.encode(example, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            print(f"\n📝 Esempio: {example}")
            print(f"   Tokens: {tokens}")
            print(f"   Decoded: {decoded}")
            print(f"   Roundtrip OK: {example == decoded}")

def main():
    """Funzione principale."""
    print("🧠 NEUROGLYPH v2.0 - Tokenizer Fixer")
    print("=" * 60)
    
    # Inizializza fixer
    fixer = TokenizerFixer()
    
    # Verifica corpus
    corpus_path = "training/tokenizer/neuroglyph_corpus.txt"
    if not Path(corpus_path).exists():
        print(f"❌ Corpus non trovato: {corpus_path}")
        return False
    
    # Crea tokenizer corretto
    fixed_tokenizer_path = fixer.create_fixed_tokenizer(
        corpus_path=corpus_path,
        vocab_size=34000,
        output_dir="training/tokenizer/neuroglyph_tokenizer_fixed"
    )
    
    # Valida tokenizer corretto
    results = fixer.validate_fixed_tokenizer(fixed_tokenizer_path)
    
    # Test esempi
    fixer.test_fixed_tokenizer(fixed_tokenizer_path)
    
    # Risultato finale
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Copertura simboli: {results['symbol_coverage']:.1f}%")
    print(f"   - Zero-splitting: {results['zero_split_percentage']:.1f}%")
    print(f"   - Roundtrip fidelity: {results['roundtrip_percentage']:.1f}%")
    
    if (results['zero_split_percentage'] >= 80 and 
        results['roundtrip_percentage'] >= 90):
        print("✅ TOKENIZER CORRETTO CON SUCCESSO!")
        return True
    else:
        print("⚠️  TOKENIZER NECESSITA ULTERIORI MIGLIORAMENTI")
        return False

if __name__ == "__main__":
    success = main()
