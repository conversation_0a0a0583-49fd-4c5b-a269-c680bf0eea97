#!/usr/bin/env python3
"""
Validatore e Correttore Tokenizer NEUROGLYPH v2.0

Verifica e corregge il tokenizer per garantire:
1. Zero splitting per tutti i neuroglifi
2. Corretta gestione token speciali
3. Roundtrip fidelity perfetta
4. Compatibilità con training LLM
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Set
from transformers import PreTrainedTokenizerFast
import re

class TokenizerValidator:
    """Validatore per tokenizer NEUROGLYPH."""
    
    def __init__(self, 
                 tokenizer_path: str = "training/tokenizer/neuroglyph_tokenizer",
                 registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il validatore.
        
        Args:
            tokenizer_path: Percorso al tokenizer
            registry_path: Percorso al registry simbolico
        """
        self.tokenizer_path = tokenizer_path
        self.registry_path = registry_path
        self.symbols = []
        self._load_symbols()
        
        # Carica tokenizer se esiste
        if Path(tokenizer_path).exists():
            self.tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        else:
            self.tokenizer = None
    
    def _load_symbols(self) -> None:
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbols.append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli per validazione")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def comprehensive_validation(self) -> Dict[str, Any]:
        """Validazione completa del tokenizer."""
        if not self.tokenizer:
            return {"error": "Tokenizer non trovato"}
        
        print("🔍 Validazione completa tokenizer...")
        
        results = {
            'vocab_analysis': self._analyze_vocabulary(),
            'symbol_coverage': self._test_symbol_coverage(),
            'roundtrip_fidelity': self._test_roundtrip_fidelity(),
            'special_tokens': self._test_special_tokens(),
            'encoding_quality': self._test_encoding_quality()
        }
        
        return results
    
    def _analyze_vocabulary(self) -> Dict[str, Any]:
        """Analizza il vocabolario del tokenizer."""
        vocab = self.tokenizer.get_vocab()
        
        analysis = {
            'total_vocab_size': len(vocab),
            'symbols_in_vocab': 0,
            'special_tokens_count': 0,
            'regular_tokens_count': 0,
            'unknown_tokens': []
        }
        
        # Conta simboli nel vocabolario
        for symbol in self.symbols:
            if symbol in vocab:
                analysis['symbols_in_vocab'] += 1
            else:
                analysis['unknown_tokens'].append(symbol)
        
        # Conta token speciali
        special_tokens = [
            '<NG_START>', '<NG_END>', '<NG_THINK>', '<NG_REASON>',
            '<NG_MEMORY>', '<NG_VALIDATE>', '<NG_ERROR>', '<NG_CORRECT>',
            '<UNK>', '<PAD>', '<BOS>', '<EOS>', '<MASK>'
        ]
        
        for token in special_tokens:
            if token in vocab:
                analysis['special_tokens_count'] += 1
        
        analysis['regular_tokens_count'] = (
            analysis['total_vocab_size'] - 
            analysis['symbols_in_vocab'] - 
            analysis['special_tokens_count']
        )
        
        print(f"📊 Analisi Vocabolario:")
        print(f"   - Dimensione totale: {analysis['total_vocab_size']:,}")
        print(f"   - Simboli NEUROGLYPH: {analysis['symbols_in_vocab']:,}")
        print(f"   - Token speciali: {analysis['special_tokens_count']}")
        print(f"   - Token regolari: {analysis['regular_tokens_count']:,}")
        print(f"   - Simboli mancanti: {len(analysis['unknown_tokens'])}")
        
        return analysis
    
    def _test_symbol_coverage(self) -> Dict[str, Any]:
        """Testa la copertura dei simboli."""
        coverage = {
            'total_symbols': len(self.symbols),
            'covered_symbols': 0,
            'zero_split_symbols': 0,
            'problematic_symbols': [],
            'coverage_percentage': 0.0,
            'zero_split_percentage': 0.0
        }
        
        for symbol in self.symbols:
            # Test encoding
            tokens = self.tokenizer.tokenize(symbol)
            token_ids = self.tokenizer.encode(symbol, add_special_tokens=False)
            
            # Verifica se il simbolo è coperto
            if len(tokens) > 0 and '<UNK>' not in tokens:
                coverage['covered_symbols'] += 1
                
                # Verifica zero-splitting
                if len(tokens) == 1:
                    coverage['zero_split_symbols'] += 1
                else:
                    coverage['problematic_symbols'].append({
                        'symbol': symbol,
                        'tokens': tokens,
                        'token_count': len(tokens)
                    })
        
        coverage['coverage_percentage'] = (coverage['covered_symbols'] / coverage['total_symbols']) * 100
        coverage['zero_split_percentage'] = (coverage['zero_split_symbols'] / coverage['total_symbols']) * 100
        
        print(f"📊 Copertura Simboli:")
        print(f"   - Simboli coperti: {coverage['covered_symbols']}/{coverage['total_symbols']} ({coverage['coverage_percentage']:.1f}%)")
        print(f"   - Zero-splitting: {coverage['zero_split_symbols']}/{coverage['total_symbols']} ({coverage['zero_split_percentage']:.1f}%)")
        print(f"   - Problematici: {len(coverage['problematic_symbols'])}")
        
        return coverage
    
    def _test_roundtrip_fidelity(self) -> Dict[str, Any]:
        """Testa la fedeltà roundtrip."""
        test_cases = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "The algorithm 🠫 uses 🟓 for optimization",
            "<NG_START> ⟨ ⟩ ◊ ⊢ <NG_END>",
            "def func_⟨(): return ⟩_result",
            "⟨ ⟩ ◊ ⊢ ⊣ ∧ ∨ ¬",
            "🠫 🟓 🝙 🞡 ℆ ◡",
            "for item in ⟨list⟩: process(item)"
        ]
        
        fidelity = {
            'total_tests': len(test_cases),
            'perfect_roundtrips': 0,
            'failed_roundtrips': [],
            'fidelity_percentage': 0.0
        }
        
        for test_case in test_cases:
            # Encode e decode
            token_ids = self.tokenizer.encode(test_case)
            decoded = self.tokenizer.decode(token_ids, skip_special_tokens=True)
            
            # Verifica fedeltà
            if test_case.strip() == decoded.strip():
                fidelity['perfect_roundtrips'] += 1
            else:
                fidelity['failed_roundtrips'].append({
                    'original': test_case,
                    'decoded': decoded,
                    'tokens': self.tokenizer.tokenize(test_case)
                })
        
        fidelity['fidelity_percentage'] = (fidelity['perfect_roundtrips'] / fidelity['total_tests']) * 100
        
        print(f"📊 Fedeltà Roundtrip:")
        print(f"   - Test perfetti: {fidelity['perfect_roundtrips']}/{fidelity['total_tests']} ({fidelity['fidelity_percentage']:.1f}%)")
        print(f"   - Test falliti: {len(fidelity['failed_roundtrips'])}")
        
        return fidelity
    
    def _test_special_tokens(self) -> Dict[str, Any]:
        """Testa i token speciali."""
        special_tokens = [
            '<NG_START>', '<NG_END>', '<NG_THINK>', '<NG_REASON>',
            '<NG_MEMORY>', '<NG_VALIDATE>', '<NG_ERROR>', '<NG_CORRECT>',
            '<UNK>', '<PAD>', '<BOS>', '<EOS>', '<MASK>'
        ]
        
        special_test = {
            'total_special_tokens': len(special_tokens),
            'working_tokens': 0,
            'broken_tokens': [],
            'special_token_percentage': 0.0
        }
        
        for token in special_tokens:
            try:
                # Test encoding/decoding
                token_id = self.tokenizer.convert_tokens_to_ids(token)
                decoded_token = self.tokenizer.convert_ids_to_tokens(token_id)
                
                if decoded_token == token:
                    special_test['working_tokens'] += 1
                else:
                    special_test['broken_tokens'].append({
                        'token': token,
                        'token_id': token_id,
                        'decoded': decoded_token
                    })
            except Exception as e:
                special_test['broken_tokens'].append({
                    'token': token,
                    'error': str(e)
                })
        
        special_test['special_token_percentage'] = (special_test['working_tokens'] / special_test['total_special_tokens']) * 100
        
        print(f"📊 Token Speciali:")
        print(f"   - Funzionanti: {special_test['working_tokens']}/{special_test['total_special_tokens']} ({special_test['special_token_percentage']:.1f}%)")
        print(f"   - Problematici: {len(special_test['broken_tokens'])}")
        
        return special_test
    
    def _test_encoding_quality(self) -> Dict[str, Any]:
        """Testa la qualità dell'encoding."""
        test_samples = [
            "Simple text without symbols",
            "Text with symbols: ⟨ ⟩ ◊",
            "Mixed: def func_⟨() → ⟩",
            "Complex: <NG_START> ⟨logic⟩ ⊢ ⟨result⟩ <NG_END>",
            "Code: for item in ⟨list⟩: if ◊(item): yield ⊢(item)"
        ]
        
        quality = {
            'total_samples': len(test_samples),
            'efficient_encodings': 0,
            'inefficient_encodings': [],
            'avg_tokens_per_char': 0.0,
            'efficiency_percentage': 0.0
        }
        
        total_chars = 0
        total_tokens = 0
        
        for sample in test_samples:
            tokens = self.tokenizer.tokenize(sample)
            token_count = len(tokens)
            char_count = len(sample)
            
            total_chars += char_count
            total_tokens += token_count
            
            # Considera efficiente se tokens/chars < 0.8
            efficiency = token_count / char_count if char_count > 0 else 1.0
            
            if efficiency < 0.8:
                quality['efficient_encodings'] += 1
            else:
                quality['inefficient_encodings'].append({
                    'sample': sample,
                    'tokens': tokens,
                    'efficiency': efficiency
                })
        
        quality['avg_tokens_per_char'] = total_tokens / total_chars if total_chars > 0 else 0.0
        quality['efficiency_percentage'] = (quality['efficient_encodings'] / quality['total_samples']) * 100
        
        print(f"📊 Qualità Encoding:")
        print(f"   - Encoding efficienti: {quality['efficient_encodings']}/{quality['total_samples']} ({quality['efficiency_percentage']:.1f}%)")
        print(f"   - Token/carattere medio: {quality['avg_tokens_per_char']:.3f}")
        
        return quality
    
    def generate_validation_report(self, results: Dict[str, Any]) -> str:
        """Genera report di validazione."""
        report = []
        report.append("# NEUROGLYPH Tokenizer Validation Report")
        report.append("=" * 60)
        report.append("")
        
        # Sommario
        vocab_analysis = results.get('vocab_analysis', {})
        symbol_coverage = results.get('symbol_coverage', {})
        roundtrip_fidelity = results.get('roundtrip_fidelity', {})
        
        report.append("## 📊 SOMMARIO ESECUTIVO")
        report.append(f"- **Vocabolario**: {vocab_analysis.get('total_vocab_size', 0):,} token")
        report.append(f"- **Simboli NEUROGLYPH**: {vocab_analysis.get('symbols_in_vocab', 0):,}/{len(self.symbols):,}")
        report.append(f"- **Zero-splitting**: {symbol_coverage.get('zero_split_percentage', 0):.1f}%")
        report.append(f"- **Roundtrip fidelity**: {roundtrip_fidelity.get('fidelity_percentage', 0):.1f}%")
        report.append("")
        
        # Dettagli
        if symbol_coverage.get('problematic_symbols'):
            report.append("## ⚠️ SIMBOLI PROBLEMATICI")
            for item in symbol_coverage['problematic_symbols'][:10]:
                report.append(f"- `{item['symbol']}` → {item['tokens']} ({item['token_count']} token)")
            report.append("")
        
        if roundtrip_fidelity.get('failed_roundtrips'):
            report.append("## ❌ ROUNDTRIP FALLITI")
            for item in roundtrip_fidelity['failed_roundtrips'][:5]:
                report.append(f"- Original: `{item['original']}`")
                report.append(f"  Decoded: `{item['decoded']}`")
                report.append("")
        
        # Raccomandazioni
        report.append("## 🎯 RACCOMANDAZIONI")
        
        if symbol_coverage.get('zero_split_percentage', 0) < 100:
            report.append("1. **Migliorare zero-splitting** - Alcuni simboli vengono ancora divisi")
        
        if roundtrip_fidelity.get('fidelity_percentage', 0) < 95:
            report.append("2. **Migliorare roundtrip fidelity** - Problemi di encoding/decoding")
        
        if vocab_analysis.get('unknown_tokens'):
            report.append("3. **Aggiungere simboli mancanti** - Alcuni simboli non sono nel vocabolario")
        
        report.append("")
        
        return "\n".join(report)

def main():
    """Funzione principale."""
    print("🧠 NEUROGLYPH v2.0 - Tokenizer Validator")
    print("=" * 60)
    
    # Inizializza validatore
    validator = TokenizerValidator()
    
    if not validator.tokenizer:
        print("❌ Tokenizer non trovato. Esegui prima il training.")
        return False
    
    # Esegui validazione completa
    results = validator.comprehensive_validation()
    
    # Genera report
    report = validator.generate_validation_report(results)
    
    # Salva report
    report_path = Path("training/tokenizer/validation_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 Report salvato: {report_path}")
    
    # Risultato finale
    zero_split_pct = results.get('symbol_coverage', {}).get('zero_split_percentage', 0)
    fidelity_pct = results.get('roundtrip_fidelity', {}).get('fidelity_percentage', 0)
    
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Zero-splitting: {zero_split_pct:.1f}%")
    print(f"   - Roundtrip fidelity: {fidelity_pct:.1f}%")
    
    if zero_split_pct >= 95 and fidelity_pct >= 90:
        print("✅ TOKENIZER VALIDATO CON SUCCESSO!")
        return True
    else:
        print("⚠️  TOKENIZER NECESSITA MIGLIORAMENTI")
        return False

if __name__ == "__main__":
    success = main()
