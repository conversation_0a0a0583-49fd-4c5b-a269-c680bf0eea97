{"<BOS>": 0, "<EOS>": 1, "<PAD>": 2, "<UNK>": 3, "<MASK>": 4, "#": 5, "(": 6, ")": 7, ",": 8, "-": 9, "0": 10, "1": 11, "2": 12, "3": 13, "4": 14, "5": 15, "6": 16, "7": 17, "8": 18, "9": 19, ":": 20, ";": 21, "<": 22, "=": 23, ">": 24, "A": 25, "B": 26, "C": 27, "D": 28, "E": 29, "F": 30, "G": 31, "I": 32, "N": 33, "P": 34, "R": 35, "S": 36, "T": 37, "W": 38, "[": 39, "]": 40, "_": 41, "a": 42, "b": 43, "c": 44, "d": 45, "e": 46, "f": 47, "g": 48, "h": 49, "i": 50, "j": 51, "k": 52, "l": 53, "m": 54, "n": 55, "o": 56, "p": 57, "q": 58, "r": 59, "s": 60, "t": 61, "u": 62, "v": 63, "w": 64, "x": 65, "y": 66, "z": 67, "{": 68, "}": 69, "‐": 70, "‑": 71, "‒": 72, "–": 73, "—": 74, "―": 75, "‖": 76, "‗": 77, "‘": 78, "”": 79, "„": 80, "•": 81, "‧": 82, "‴": 83, "‷": 84, "⁅": 85, "⁆": 86, "⁎": 87, "⁏": 88, "⁵": 89, "⁸": 90, "⁺": 91, "ⁿ": 92, "₀": 93, "ₐ": 94, "ₒ": 95, "ₓ": 96, "ₔ": 97, "ₛ": 98, "₠": 99, "₣": 100, "₦": 101, "₪": 102, "₭": 103, "₮": 104, "₱": 105, "₵": 106, "₸": 107, "₼": 108, "℀": 109, "℁": 110, "ℂ": 111, "℃": 112, "℆": 113, "℈": 114, "ℌ": 115, "ℎ": 116, "ℏ": 117, "ℐ": 118, "ℓ": 119, "℔": 120, "ℕ": 121, "№": 122, "ℙ": 123, "ℛ": 124, "ℜ": 125, "ℝ": 126, "℞": 127, "℠": 128, "℡": 129, "ℤ": 130, "℥": 131, "Ω": 132, "Å": 133, "ℬ": 134, "ℭ": 135, "℮": 136, "ℯ": 137, "ℱ": 138, "ℳ": 139, "ℵ": 140, "ℶ": 141, "ℷ": 142, "ℸ": 143, "℺": 144, "℻": 145, "ℽ": 146, "ℿ": 147, "⅀": 148, "ⅅ": 149, "ⅆ": 150, "ⅇ": 151, "ⅉ": 152, "⅌": 153, "⅍": 154, "ⅎ": 155, "⅏": 156, "⅐": 157, "⅑": 158, "⅖": 159, "⅘": 160, "⅙": 161, "⅛": 162, "⅝": 163, "Ⅳ": 164, "Ⅴ": 165, "Ⅶ": 166, "Ⅹ": 167, "Ⅺ": 168, "ⅰ": 169, "ⅴ": 170, "ⅷ": 171, "ⅸ": 172, "ⅻ": 173, "ⅼ": 174, "ⅽ": 175, "ⅿ": 176, "ↀ": 177, "ↇ": 178, "←": 179, "↑": 180, "→": 181, "↔": 182, "↕": 183, "↖": 184, "↗": 185, "↘": 186, "↚": 187, "↛": 188, "↜": 189, "↝": 190, "↞": 191, "↟": 192, "↠": 193, "↡": 194, "↢": 195, "↣": 196, "↤": 197, "↥": 198, "↦": 199, "↧": 200, "↨": 201, "↪": 202, "↫": 203, "↬": 204, "↮": 205, "↱": 206, "↲": 207, "↳": 208, "↴": 209, "↷": 210, "↸": 211, "↹": 212, "↺": 213, "↻": 214, "↾": 215, "⇀": 216, "⇁": 217, "⇂": 218, "⇅": 219, "⇇": 220, "⇉": 221, "⇊": 222, "⇋": 223, "⇌": 224, "⇍": 225, "⇎": 226, "⇏": 227, "⇐": 228, "⇑": 229, "⇓": 230, "⇔": 231, "⇕": 232, "⇖": 233, "⇗": 234, "⇘": 235, "⇚": 236, "⇜": 237, "⇝": 238, "⇞": 239, "⇟": 240, "⇠": 241, "⇡": 242, "⇢": 243, "⇣": 244, "⇤": 245, "⇥": 246, "⇦": 247, "⇧": 248, "⇨": 249, "⇪": 250, "⇫": 251, "⇭": 252, "⇮": 253, "⇯": 254, "⇱": 255, "⇲": 256, "⇳": 257, "⇴": 258, "⇵": 259, "⇷": 260, "⇸": 261, "⇹": 262, "⇺": 263, "⇻": 264, "⇿": 265, "∀": 266, "∁": 267, "∂": 268, "∃": 269, "∄": 270, "∅": 271, "∆": 272, "∇": 273, "∊": 274, "∋": 275, "∌": 276, "∍": 277, "∎": 278, "∐": 279, "∑": 280, "−": 281, "∔": 282, "∖": 283, "∗": 284, "∙": 285, "∜": 286, "∞": 287, "∠": 288, "∢": 289, "∣": 290, "∥": 291, "∧": 292, "∨": 293, "∩": 294, "∪": 295, "∭": 296, "∮": 297, "∯": 298, "∰": 299, "∱": 300, "∲": 301, "∴": 302, "∶": 303, "∷": 304, "∹": 305, "∺": 306, "∼": 307, "∿": 308, "≀": 309, "≁": 310, "≂": 311, "≃": 312, "≄": 313, "≅": 314, "≆": 315, "≇": 316, "≉": 317, "≊": 318, "≋": 319, "≌": 320, "≍": 321, "≎": 322, "≑": 323, "≒": 324, "≔": 325, "≕": 326, "≘": 327, "≙": 328, "≚": 329, "≜": 330, "≝": 331, "≞": 332, "≟": 333, "≠": 334, "≡": 335, "≤": 336, "≥": 337, "≦": 338, "≧": 339, "≨": 340, "≪": 341, "≫": 342, "≬": 343, "≭": 344, "≮": 345, "≯": 346, "≰": 347, "≱": 348, "≲": 349, "≳": 350, "≴": 351, "≵": 352, "≶": 353, "≷": 354, "≸": 355, "≹": 356, "≺": 357, "≻": 358, "≼": 359, "≽": 360, "≾": 361, "≿": 362, "⊀": 363, "⊁": 364, "⊂": 365, "⊃": 366, "⊄": 367, "⊅": 368, "⊆": 369, "⊈": 370, "⊉": 371, "⊊": 372, "⊌": 373, "⊍": 374, "⊎": 375, "⊐": 376, "⊑": 377, "⊓": 378, "⊔": 379, "⊕": 380, "⊖": 381, "⊗": 382, "⊘": 383, "⊙": 384, "⊚": 385, "⊛": 386, "⊜": 387, "⊝": 388, "⊞": 389, "⊟": 390, "⊠": 391, "⊡": 392, "⊢": 393, "⊣": 394, "⊤": 395, "⊥": 396, "⊦": 397, "⊧": 398, "⊨": 399, "⊩": 400, "⊫": 401, "⊬": 402, "⊭": 403, "⊮": 404, "⊯": 405, "⊰": 406, "⊳": 407, "⊵": 408, "⊶": 409, "⊸": 410, "⊹": 411, "⊺": 412, "⊻": 413, "⊼": 414, "⊽": 415, "⊾": 416, "⊿": 417, "⋀": 418, "⋂": 419, "⋃": 420, "⋄": 421, "⋅": 422, "⋇": 423, "⋈": 424, "⋉": 425, "⋊": 426, "⋋": 427, "⋌": 428, "⋍": 429, "⋎": 430, "⋏": 431, "⋐": 432, "⋒": 433, "⋔": 434, "⋕": 435, "⋖": 436, "⋗": 437, "⋘": 438, "⋙": 439, "⋚": 440, "⋛": 441, "⋜": 442, "⋝": 443, "⋞": 444, "⋟": 445, "⋢": 446, "⋤": 447, "⋥": 448, "⋦": 449, "⋩": 450, "⋪": 451, "⋫": 452, "⋬": 453, "⋭": 454, "⋮": 455, "⋯": 456, "⋰": 457, "⋱": 458, "⋲": 459, "⋳": 460, "⋴": 461, "⋵": 462, "⋶": 463, "⋷": 464, "⋸": 465, "⋹": 466, "⋺": 467, "⋻": 468, "⋽": 469, "⋾": 470, "⋿": 471, "⌀": 472, "⌁": 473, "⌂": 474, "⌃": 475, "⌄": 476, "⌅": 477, "⌆": 478, "⌇": 479, "⌈": 480, "⌊": 481, "⌋": 482, "⌌": 483, "⌍": 484, "⌎": 485, "⌑": 486, "⌒": 487, "⌓": 488, "⌕": 489, "⌖": 490, "⌘": 491, "⌚": 492, "⌛": 493, "⌜": 494, "⌞": 495, "⌟": 496, "⌠": 497, "⌡": 498, "⌣": 499, "⌤": 500, "⌥": 501, "⌧": 502, "⌨": 503, "〈": 504, "〉": 505, "⌫": 506, "⌭": 507, "⌮": 508, "⌯": 509, "⌱": 510, "⌳": 511, "⌴": 512, "⌵": 513, "⌶": 514, "⌷": 515, "⌸": 516, "⌹": 517, "⌺": 518, "⌻": 519, "⌼": 520, "⌽": 521, "⌾": 522, "⍂": 523, "⍃": 524, "⍄": 525, "⍅": 526, "⍆": 527, "⍈": 528, "⍉": 529, "⍊": 530, "⍋": 531, "⍌": 532, "⍍": 533, "⍎": 534, "⍏": 535, "⍐": 536, "⍑": 537, "⍒": 538, "⍔": 539, "⍖": 540, "⍗": 541, "⍘": 542, "⍙": 543, "⍚": 544, "⍛": 545, "⍟": 546, "⍠": 547, "⍡": 548, "⍢": 549, "⍣": 550, "⍤": 551, "⍥": 552, "⍦": 553, "⍧": 554, "⍨": 555, "⍩": 556, "⍪": 557, "⍫": 558, "⍬": 559, "⍭": 560, "⍮": 561, "⍯": 562, "⍰": 563, "⍲": 564, "⍴": 565, "⍶": 566, "⍷": 567, "⍹": 568, "⍻": 569, "⍽": 570, "⍾": 571, "⍿": 572, "⎂": 573, "⎃": 574, "⎄": 575, "⎅": 576, "⎆": 577, "⎇": 578, "⎉": 579, "⎊": 580, "⎋": 581, "⎌": 582, "⎍": 583, "⎎": 584, "⎏": 585, "⎐": 586, "⎑": 587, "⎓": 588, "⎔": 589, "⎕": 590, "⎖": 591, "⎗": 592, "⎘": 593, "⎛": 594, "⎜": 595, "⎟": 596, "⎠": 597, "⎡": 598, "⎣": 599, "⎦": 600, "⎨": 601, "⎪": 602, "⎫": 603, "⎭": 604, "⎮": 605, "⎰": 606, "⎱": 607, "⎳": 608, "⎴": 609, "⎵": 610, "⎶": 611, "⎹": 612, "⎺": 613, "⎻": 614, "⎼": 615, "⎽": 616, "⎾": 617, "⎿": 618, "⏂": 619, "⏅": 620, "⏆": 621, "⏇": 622, "⏈": 623, "⏉": 624, "⏊": 625, "⏍": 626, "⏎": 627, "⏏": 628, "⏒": 629, "⏔": 630, "⏕": 631, "⏖": 632, "⏜": 633, "⏠": 634, "⏡": 635, "⏢": 636, "⏤": 637, "⏥": 638, "⏦": 639, "⏭": 640, "⏰": 641, "⏱": 642, "⏲": 643, "⏳": 644, "⏶": 645, "⏽": 646, "⏾": 647, "⏿": 648, "②": 649, "③": 650, "⑧": 651, "⑬": 652, "⑭": 653, "⑮": 654, "⑵": 655, "⑶": 656, "⑸": 657, "⑽": 658, "⒃": 659, "⒌": 660, "⒐": 661, "⒘": 662, "⒙": 663, "⒜": 664, "⒤": 665, "⒦": 666, "■": 667, "▢": 668, "▤": 669, "▦": 670, "▧": 671, "▨": 672, "▩": 673, "▭": 674, "▮": 675, "▰": 676, "▱": 677, "▲": 678, "△": 679, "▴": 680, "▵": 681, "▶": 682, "▹": 683, "▻": 684, "▽": 685, "▿": 686, "◀": 687, "◁": 688, "◂": 689, "◃": 690, "◅": 691, "◆": 692, "◇": 693, "◈": 694, "○": 695, "◌": 696, "◍": 697, "◎": 698, "●": 699, "◐": 700, "◑": 701, "◒": 702, "◓": 703, "◔": 704, "◕": 705, "◗": 706, "◙": 707, "◚": 708, "◛": 709, "◜": 710, "◝": 711, "◞": 712, "◟": 713, "◠": 714, "◡": 715, "◢": 716, "◥": 717, "◦": 718, "◧": 719, "◨": 720, "◩": 721, "◪": 722, "◫": 723, "◬": 724, "◭": 725, "◮": 726, "◯": 727, "◰": 728, "◲": 729, "◳": 730, "◴": 731, "◵": 732, "◶": 733, "◸": 734, "◹": 735, "◺": 736, "◻": 737, "◽": 738, "◾": 739, "◿": 740, "✀": 741, "✁": 742, "✂": 743, "✃": 744, "✅": 745, "✈": 746, "✋": 747, "✌": 748, "✍": 749, "✎": 750, "✏": 751, "✐": 752, "✑": 753, "✒": 754, "✔": 755, "✗": 756, "✙": 757, "✚": 758, "✞": 759, "✠": 760, "✢": 761, "✣": 762, "✥": 763, "✨": 764, "✩": 765, "✪": 766, "✫": 767, "✬": 768, "✮": 769, "✯": 770, "✰": 771, "✲": 772, "✳": 773, "✵": 774, "✶": 775, "✷": 776, "✸": 777, "✹": 778, "✺": 779, "✻": 780, "✼": 781, "✽": 782, "❀": 783, "❁": 784, "❂": 785, "❄": 786, "❅": 787, "❆": 788, "❈": 789, "❉": 790, "❊": 791, "❋": 792, "❌": 793, "❍": 794, "❎": 795, "❏": 796, "❐": 797, "❑": 798, "❒": 799, "❔": 800, "❕": 801, "❖": 802, "❗": 803, "❘": 804, "❚": 805, "❜": 806, "❝": 807, "❠": 808, "❡": 809, "❢": 810, "❤": 811, "❥": 812, "❦": 813, "❨": 814, "❪": 815, "❫": 816, "❮": 817, "❰": 818, "❲": 819, "❳": 820, "❵": 821, "❶": 822, "❷": 823, "❹": 824, "❺": 825, "❻": 826, "❼": 827, "❽": 828, "❾": 829, "➀": 830, "➁": 831, "➂": 832, "➃": 833, "➄": 834, "➅": 835, "➆": 836, "➇": 837, "➈": 838, "➉": 839, "➊": 840, "➋": 841, "➌": 842, "➍": 843, "➎": 844, "➏": 845, "➐": 846, "➑": 847, "➒": 848, "➓": 849, "➔": 850, "➖": 851, "➗": 852, "➘": 853, "➚": 854, "➜": 855, "➝": 856, "➞": 857, "➟": 858, "➣": 859, "➦": 860, "➧": 861, "➩": 862, "➪": 863, "➫": 864, "➬": 865, "➭": 866, "➮": 867, "➯": 868, "➰": 869, "➱": 870, "➲": 871, "➳": 872, "➶": 873, "➸": 874, "➹": 875, "➻": 876, "➼": 877, "➽": 878, "➾": 879, "➿": 880, "⟖": 881, "⟢": 882, "⠃": 883, "⠐": 884, "⠨": 885, "⠳": 886, "⡃": 887, "⡕": 888, "⡨": 889, "⡩": 890, "⡮": 891, "⢅": 892, "⢈": 893, "⢉": 894, "⢟": 895, "⢮": 896, "⣔": 897, "⣲": 898, "⤀": 899, "⤁": 900, "⤂": 901, "⤄": 902, "⤅": 903, "⤆": 904, "⤇": 905, "⤈": 906, "⤉": 907, "⤋": 908, "⤌": 909, "⤎": 910, "⤑": 911, "⤒": 912, "⤔": 913, "⤕": 914, "⤙": 915, "⤚": 916, "⤠": 917, "⤡": 918, "⤢": 919, "⤣": 920, "⤤": 921, "⤦": 922, "⤧": 923, "⤨": 924, "⤪": 925, "⤫": 926, "⤬": 927, "⤮": 928, "⤯": 929, "⤰": 930, "⤱": 931, "⤲": 932, "⤸": 933, "⤹": 934, "⤺": 935, "⤻": 936, "⤼": 937, "⤾": 938, "⥀": 939, "⥁": 940, "⥃": 941, "⥄": 942, "⥅": 943, "⥆": 944, "⥇": 945, "⥈": 946, "⥉": 947, "⥋": 948, "⥌": 949, "⥎": 950, "⥐": 951, "⥓": 952, "⥖": 953, "⥗": 954, "⥘": 955, "⥙": 956, "⥚": 957, "⥛": 958, "⥟": 959, "⥣": 960, "⥤": 961, "⥥": 962, "⥦": 963, "⥧": 964, "⥨": 965, "⥪": 966, "⥫": 967, "⥬": 968, "⥭": 969, "⥰": 970, "⥱": 971, "⥲": 972, "⥵": 973, "⥶": 974, "⥷": 975, "⥸": 976, "⥹": 977, "⥺": 978, "⥽": 979, "⥾": 980, "⦀": 981, "⦁": 982, "⦂": 983, "⦃": 984, "⦄": 985, "⦅": 986, "⦆": 987, "⦇": 988, "⦈": 989, "⦉": 990, "⦊": 991, "⦋": 992, "⦏": 993, "⦐": 994, "⦑": 995, "⦓": 996, "⦔": 997, "⦖": 998, "⦜": 999, "⦝": 1000, "⦞": 1001, "⦟": 1002, "⦡": 1003, "⦣": 1004, "⦥": 1005, "⦧": 1006, "⦨": 1007, "⦪": 1008, "⦭": 1009, "⦮": 1010, "⦯": 1011, "⦲": 1012, "⦳": 1013, "⦴": 1014, "⦶": 1015, "⦷": 1016, "⦹": 1017, "⦻": 1018, "⦼": 1019, "⦽": 1020, "⦾": 1021, "⦿": 1022, "⧀": 1023, "⧂": 1024, "⧇": 1025, "⧊": 1026, "⧋": 1027, "⧍": 1028, "⧎": 1029, "⧏": 1030, "⧑": 1031, "⧒": 1032, "⧕": 1033, "⧖": 1034, "⧘": 1035, "⧙": 1036, "⧜": 1037, "⧞": 1038, "⧢": 1039, "⧣": 1040, "⧥": 1041, "⧧": 1042, "⧨": 1043, "⧪": 1044, "⧭": 1045, "⧳": 1046, "⧴": 1047, "⧵": 1048, "⧷": 1049, "⧹": 1050, "⧼": 1051, "⧾": 1052, "⨀": 1053, "⨁": 1054, "⨂": 1055, "⨃": 1056, "⨅": 1057, "⨇": 1058, "⨉": 1059, "⨍": 1060, "⨎": 1061, "⨏": 1062, "⨐": 1063, "⨒": 1064, "⨔": 1065, "⨖": 1066, "⨙": 1067, "⨚": 1068, "⨛": 1069, "⨡": 1070, "⨣": 1071, "⨤": 1072, "⨥": 1073, "⨧": 1074, "⨩": 1075, "⨫": 1076, "⨬": 1077, "⨭": 1078, "⨮": 1079, "⨰": 1080, "⨱": 1081, "⨳": 1082, "⨴": 1083, "⨵": 1084, "⨷": 1085, "⨸": 1086, "⨺": 1087, "⨻": 1088, "⨼": 1089, "⨾": 1090, "⩁": 1091, "⩃": 1092, "⩄": 1093, "⩅": 1094, "⩈": 1095, "⩉": 1096, "⩊": 1097, "⩋": 1098, "⩌": 1099, "⩍": 1100, "⩎": 1101, "⩏": 1102, "⩐": 1103, "⩑": 1104, "⩒": 1105, "⩔": 1106, "⩕": 1107, "⩖": 1108, "⩗": 1109, "⩘": 1110, "⩙": 1111, "⩚": 1112, "⩛": 1113, "⩜": 1114, "⩝": 1115, "⩞": 1116, "⩟": 1117, "⩠": 1118, "⩤": 1119, "⩧": 1120, "⩨": 1121, "⩩": 1122, "⩪": 1123, "⩫": 1124, "⩬": 1125, "⩭": 1126, "⩮": 1127, "⩯": 1128, "⩰": 1129, "⩱": 1130, "⩲": 1131, "⩴": 1132, "⩵": 1133, "⩸": 1134, "⩹": 1135, "⩺": 1136, "⩻": 1137, "⩼": 1138, "⩽": 1139, "⩾": 1140, "⪁": 1141, "⪄": 1142, "⪅": 1143, "⪆": 1144, "⪇": 1145, "⪉": 1146, "⪊": 1147, "⪋": 1148, "⪌": 1149, "⪎": 1150, "⪏": 1151, "⪐": 1152, "⪒": 1153, "⪓": 1154, "⪔": 1155, "⪕": 1156, "⪗": 1157, "⪙": 1158, "⪝": 1159, "⪞": 1160, "⪡": 1161, "⪥": 1162, "⪨": 1163, "⪪": 1164, "⪭": 1165, "⪮": 1166, "⪰": 1167, "⪱": 1168, "⪶": 1169, "⪷": 1170, "⪹": 1171, "⪼": 1172, "⪽": 1173, "⪾": 1174, "⪿": 1175, "⫀": 1176, "⫁": 1177, "⫂": 1178, "⫄": 1179, "⫆": 1180, "⫉": 1181, "⫋": 1182, "⫍": 1183, "⫎": 1184, "⫏": 1185, "⫐": 1186, "⫑": 1187, "⫒": 1188, "⫓": 1189, "⫖": 1190, "⫗": 1191, "⫝": 1192, "⫟": 1193, "⫣": 1194, "⫤": 1195, "⫥": 1196, "⫧": 1197, "⫨": 1198, "⫪": 1199, "⫫": 1200, "⫮": 1201, "⫱": 1202, "⫲": 1203, "⫵": 1204, "⫶": 1205, "⫷": 1206, "⫸": 1207, "⫹": 1208, "⫽": 1209, "⬀": 1210, "⬂": 1211, "⬃": 1212, "⬅": 1213, "⬇": 1214, "⬉": 1215, "⬌": 1216, "⬍": 1217, "⬎": 1218, "⬏": 1219, "⬑": 1220, "⬒": 1221, "⬔": 1222, "⬕": 1223, "⬖": 1224, "⬗": 1225, "⬘": 1226, "⬚": 1227, "⬛": 1228, "⬡": 1229, "⬢": 1230, "⬥": 1231, "⬧": 1232, "⬨": 1233, "⬬": 1234, "⬭": 1235, "⬮": 1236, "⬯": 1237, "⬱": 1238, "⬲": 1239, "⬳": 1240, "⬵": 1241, "⬶": 1242, "⬸": 1243, "⬺": 1244, "⬻": 1245, "⬼": 1246, "⬽": 1247, "⭁": 1248, "⭃": 1249, "⭄": 1250, "⭅": 1251, "⭆": 1252, "⭇": 1253, "⭈": 1254, "⭊": 1255, "⭌": 1256, "⭍": 1257, "⭎": 1258, "⭏": 1259, "⭑": 1260, "⭕": 1261, "⭖": 1262, "⭙": 1263, "⭚": 1264, "⭛": 1265, "⭞": 1266, "⭠": 1267, "⭡": 1268, "⭢": 1269, "⭣": 1270, "⭤": 1271, "⭥": 1272, "⭧": 1273, "⭨": 1274, "⭩": 1275, "⭪": 1276, "⭬": 1277, "⭮": 1278, "⭯": 1279, "⭰": 1280, "⭳": 1281, "⭴": 1282, "⭶": 1283, "⭷": 1284, "⭸": 1285, "⭹": 1286, "⭺": 1287, "⭻": 1288, "⭼": 1289, "⭽": 1290, "⭾": 1291, "⮂": 1292, "⮄": 1293, "⮅": 1294, "⮆": 1295, "⮇": 1296, "⮉": 1297, "⮊": 1298, "⮋": 1299, "⮌": 1300, "⮍": 1301, "⮏": 1302, "⮐": 1303, "⮑": 1304, "⮒": 1305, "⮓": 1306, "⮕": 1307, "⮖": 1308, "⮗": 1309, "⮘": 1310, "⮙": 1311, "⮜": 1312, "⮝": 1313, "⮟": 1314, "⮠": 1315, "⮡": 1316, "⮣": 1317, "⮤": 1318, "⮥": 1319, "⮦": 1320, "⮩": 1321, "⮪": 1322, "⮫": 1323, "⮮": 1324, "⮱": 1325, "⮲": 1326, "⮳": 1327, "⮴": 1328, "⮵": 1329, "⮶": 1330, "⮷": 1331, "⮺": 1332, "⮻": 1333, "⮾": 1334, "⮿": 1335, "⯁": 1336, "⯅": 1337, "⯆": 1338, "⯇": 1339, "⯈": 1340, "⯊": 1341, "⯋": 1342, "⯌": 1343, "⯍": 1344, "⯏": 1345, "⯑": 1346, "⯒": 1347, "⯓": 1348, "⯔": 1349, "⯕": 1350, "⯗": 1351, "⯚": 1352, "⯛": 1353, "⯜": 1354, "⯟": 1355, "⯢": 1356, "⯥": 1357, "⯦": 1358, "⯧": 1359, "⯨": 1360, "⯩": 1361, "⯪": 1362, "⯫": 1363, "⯭": 1364, "⯮": 1365, "⯰": 1366, "⯱": 1367, "⯳": 1368, "⯴": 1369, "⯵": 1370, "⯶": 1371, "⯷": 1372, "⯸": 1373, "⯹": 1374, "⯺": 1375, "⯻": 1376, "⯽": 1377, "Ɫ": 1378, "ⱴ": 1379, "ⱻ": 1380, "ⷡ": 1381, "ⷩ": 1382, "ⷸ": 1383, "⸊": 1384, "⸩": 1385, "⸪": 1386, "⸬": 1387, "⹁": 1388, "ꜥ": 1389, "Ꝗ": 1390, "ꝙ": 1391, "ꝿ": 1392, "ꞏ": 1393, "Ɡ": 1394, "ꟃ": 1395, "Ꞔ": 1396, "ꟳ": 1397, "ꬵ": 1398, "ꬾ": 1399, "ꭐ": 1400, "ꭖ": 1401, "ꭞ": 1402, "𝐋": 1403, "𝐡": 1404, "𝑍": 1405, "𝑎": 1406, "𝑜": 1407, "𝑨": 1408, "𝑶": 1409, "𝑷": 1410, "𝒀": 1411, "𝒊": 1412, "𝒍": 1413, "𝒒": 1414, "𝒞": 1415, "𝒪": 1416, "𝒴": 1417, "𝒹": 1418, "𝓏": 1419, "𝓝": 1420, "𝓹": 1421, "𝔅": 1422, "𝔪": 1423, "𝔻": 1424, "𝕃": 1425, "𝕚": 1426, "𝕫": 1427, "𝕱": 1428, "𝖦": 1429, "𝖸": 1430, "𝗀": 1431, "𝗇": 1432, "𝗗": 1433, "𝘉": 1434, "𝘓": 1435, "𝘥": 1436, "𝘶": 1437, "𝙯": 1438, "𝙺": 1439, "𝚎": 1440, "𝚢": 1441, "𝚳": 1442, "𝚶": 1443, "𝛂": 1444, "𝛺": 1445, "𝛽": 1446, "𝜁": 1447, "𝜬": 1448, "𝝘": 1449, "𝝟": 1450, "𝞂": 1451, "𝞚": 1452, "𝞝": 1453, "𝞤": 1454, "𝞼": 1455, "𝞽": 1456, "𝟈": 1457, "𝟏": 1458, "𝟜": 1459, "𝟫": 1460, "𝟬": 1461, "𝟷": 1462, "🚕": 1463, "🚖": 1464, "🚗": 1465, "🚘": 1466, "🚙": 1467, "🚚": 1468, "🚛": 1469, "🚜": 1470, "🚝": 1471, "🚞": 1472, "🚩": 1473, "🚪": 1474, "🚫": 1475, "🚬": 1476, "🚸": 1477, "🚹": 1478, "🚻": 1479, "🚿": 1480, "🛁": 1481, "🛄": 1482, "🛅": 1483, "🛆": 1484, "🛇": 1485, "🛈": 1486, "🛉": 1487, "🜁": 1488, "🜂": 1489, "🜃": 1490, "🜈": 1491, "🜉": 1492, "🜊": 1493, "🜍": 1494, "🜎": 1495, "🜏": 1496, "🜔": 1497, "🜗": 1498, "🜘": 1499, "🜛": 1500, "🜝": 1501, "🜢": 1502, "🜭": 1503, "🜱": 1504, "🜲": 1505, "🜳": 1506, "🜵": 1507, "🜷": 1508, "🜼": 1509, "🜿": 1510, "🝁": 1511, "🝃": 1512, "🝅": 1513, "🝇": 1514, "🝉": 1515, "🝊": 1516, "🝋": 1517, "🝏": 1518, "🝐": 1519, "🝓": 1520, "🝔": 1521, "🝕": 1522, "🝗": 1523, "🝘": 1524, "🝙": 1525, "🝚": 1526, "🝜": 1527, "🝝": 1528, "🝠": 1529, "🝩": 1530, "🝪": 1531, "🝭": 1532, "🝲": 1533, "🝵": 1534, "🝸": 1535, "🞀": 1536, "🞂": 1537, "🞆": 1538, "🞇": 1539, "🞊": 1540, "🞌": 1541, "🞎": 1542, "🞏": 1543, "🞒": 1544, "🞕": 1545, "🞘": 1546, "🞚": 1547, "🞛": 1548, "🞞": 1549, "🞟": 1550, "🞠": 1551, "🞡": 1552, "🞤": 1553, "🞧": 1554, "🞨": 1555, "🞫": 1556, "🞬": 1557, "🞮": 1558, "🞯": 1559, "🞰": 1560, "🞱": 1561, "🞲": 1562, "🞳": 1563, "🞴": 1564, "🞵": 1565, "🞶": 1566, "🞷": 1567, "🞹": 1568, "🞾": 1569, "🞿": 1570, "🟀": 1571, "🟁": 1572, "🟂": 1573, "🟃": 1574, "🟄": 1575, "🟆": 1576, "🟇": 1577, "🟈": 1578, "🟉": 1579, "🟊": 1580, "🟌": 1581, "🟍": 1582, "🟎": 1583, "🟏": 1584, "🟐": 1585, "🟒": 1586, "🟓": 1587, "🟔": 1588, "🟕": 1589, "🟗": 1590, "🟘": 1591, "🟙": 1592, "🟚": 1593, "🟝": 1594, "🟠": 1595, "🟡": 1596, "🟢": 1597, "🟣": 1598, "🟫": 1599, "🟮": 1600, "🟱": 1601, "🟳": 1602, "🟶": 1603, "🟷": 1604, "🟹": 1605, "🟻": 1606, "🠀": 1607, "🠁": 1608, "🠂": 1609, "🠃": 1610, "🠆": 1611, "🠈": 1612, "🠘": 1613, "🠚": 1614, "🠛": 1615, "🠝": 1616, "🠠": 1617, "🠡": 1618, "🠢": 1619, "🠤": 1620, "🠥": 1621, "🠦": 1622, "🠧": 1623, "🠪": 1624, "🠫": 1625, "🠭": 1626, "🠲": 1627, "🠳": 1628, "🠴": 1629, "🠷": 1630, "🠽": 1631, "🠾": 1632, "🠿": 1633, "🡅": 1634, "🡇": 1635, "🡈": 1636, "🡌": 1637, "🡎": 1638, "🡔": 1639, "🡘": 1640, "🡛": 1641, "🡞": 1642, "🡠": 1643, "🡨": 1644, "🡩": 1645, "🡴": 1646, "🡵": 1647, "🡼": 1648, "🢀": 1649, "🢁": 1650, "🢂": 1651, "🢃": 1652, "🢋": 1653, "🢎": 1654, "🢓": 1655, "🢔": 1656, "🢕": 1657, "🢘": 1658, "🢙": 1659, "🢚": 1660, "🢛": 1661, "🢣": 1662, "🢥": 1663, "🢧": 1664, "🢯": 1665, "🢱": 1666, "🢳": 1667, "🢺": 1668, "🣂": 1669, "🣃": 1670, "🣆": 1671, "ng": 1672, "on": 1673, "ti": 1674, "tion": 1675, "or": 1676, "og": 1677, "te": 1678, "nc": 1679, "re": 1680, "er": 1681, "ation": 1682, "ma": 1683, "ic": 1684, "ri": 1685, "th": 1686, "nt": 1687, "al": 1688, "per": 1689, "log": 1690, "di": 1691, "me": 1692, "su": 1693, "logic": 1694, "de": 1695, "se": 1696, "st": 1697, "nce": 1698, "co": 1699, "for": 1700, "ta": 1701, "ition": 1702, "ty": 1703, "qu": 1704, "cu": 1705, "meta": 1706, "ant": 1707, "um": 1708, "quant": 1709, "quantum": 1710, "ai": 1711, "cog": 1712, "nition": 1713, "cognition": 1714, "ing": 1715, "sul": 1716, "rity": 1717, "sult": 1718, "bu": 1719, "ted": 1720, "ribu": 1721, "dist": 1722, "ributed": 1723, "distributed": 1724, "secu": 1725, "security": 1726, "math": 1727, "mance": 1728, "perfor": 1729, "performance": 1730, "code": 1731, "result": 1732, "in": 1733, "as": 1734, "mp": 1735, "le": 1736, "oper": 1737, "fu": 1738, "con": 1739, "operation": 1740, "iz": 1741, "rn": 1742, "at": 1743, "he": 1744, "ro": 1745, "ss": 1746, "nction": 1747, "ization": 1748, "op": 1749, "ra": 1750, "tu": 1751, "ce": 1752, "the": 1753, "au": 1754, "atte": 1755, "ction": 1756, "ne": 1757, "li": 1758, "retu": 1759, "return": 1760, "pro": 1761, "The": 1762, "an": 1763, "el": 1764, "function": 1765, "def": 1766, "ent": 1767, "teg": 1768, "lic": 1769, "ali": 1770, "cess": 1771, "ral": 1772, "ment": 1773, "NG": 1774, "ct": 1775, "mb": 1776, "enc": 1777, "da": 1778, "sure": 1779, "process": 1780, "tem": 1781, "lication": 1782, "tim": 1783, "optim": 1784, "ith": 1785, "else": 1786, "ly": 1787, "imp": 1788, "lo": 1789, "la": 1790, "ch": 1791, "ctor": 1792, "to": 1793, "fi": 1794, "then": 1795, "ory": 1796, "pa": 1797, "las": 1798, "lass": 1799, "king": 1800, "attern": 1801, "us": 1802, "cons": 1803, "hi": 1804, "integ": 1805, "comp": 1806, "bi": 1807, "ror": 1808, "ref": 1809, "item": 1810, "zy": 1811, "zzy": 1812, "fuzzy": 1813, "ntion": 1814, "attention": 1815, "ca": 1816, "ync": 1817, "async": 1818, "lement": 1819, "and": 1820, "rep": 1821, "if": 1822, "os": 1823, "osition": 1824, "dal": 1825, "gor": 1826, "mo": 1827, "algor": 1828, "ithm": 1829, "modal": 1830, "algorithm": 1831, "ry": 1832, "ns": 1833, "ate": 1834, "oral": 1835, "temp": 1836, "temporal": 1837, "bic": 1838, "pattern": 1839, "ondi": 1840, "tional": 1841, "optimization": 1842, "bicondi": 1843, "biconditional": 1844, "tic": 1845, "rs": 1846, "curs": 1847, "implication": 1848, "gation": 1849, "negation": 1850, "list": 1851, "32": 1852, "ju": 1853, "conju": 1854, "conjunction": 1855, "AR": 1856, "EN": 1857, "ST": 1858, "ART": 1859, "END": 1860, "START": 1861, "data": 1862, "38": 1863, "ru": 1864, "25": 1865, "ve": 1866, "func": 1867, "74": 1868, "49": 1869, "mbda": 1870, "lambda": 1871, "42": 1872, "66": 1873, "oning": 1874, "reas": 1875, "reasoning": 1876, "70": 1877, "71": 1878, "class": 1879, "56": 1880, "57": 1881, "75": 1882, "ex": 1883, "mat": 1884, "77": 1885, "60": 1886, "79": 1887, "If": 1888, "30": 1889, "45": 1890, "whi": 1891, "while": 1892, "73": 1893, "85": 1894, "rog": 1895, "hand": 1896, "handle": 1897, "48": 1898, "52": 1899, "ag": 1900, "that": 1901, "agent": 1902, "53": 1903, "59": 1904, "68": 1905, "ine": 1906, "80": 1907, "neu": 1908, "40": 1909, "26": 1910, "43": 1911, "86": 1912, "33": 1913, "76": 1914, "83": 1915, "67": 1916, "81": 1917, "metacognition": 1918, "31": 1919, "36": 1920, "61": 1921, "63": 1922, "gra": 1923, "lan": 1924, "ner": 1925, "plan": 1926, "dient": 1927, "gradient": 1928, "planner": 1929, "39": 1930, "55": 1931, "benc": 1932, "hma": 1933, "rking": 1934, "error": 1935, "benchma": 1936, "benchmarking": 1937, "41": 1938, "78": 1939, "tegory": 1940, "category": 1941, "34": 1942, "46": 1943, "50": 1944, "mory": 1945, "memory": 1946, "27": 1947, "29": 1948, "35": 1949, "69": 1950, "84": 1951, "her": 1952, "ita": 1953, "rti": 1954, "rence": 1955, "deco": 1956, "inher": 1957, "lection": 1958, "herence": 1959, "parti": 1960, "using": 1961, "reflection": 1962, "itance": 1963, "decoherence": 1964, "inheritance": 1965, "partition": 1966, "is": 1967, "nation": 1968, "ordi": 1969, "tenc": 1970, "coordi": 1971, "consis": 1972, "tency": 1973, "coordination": 1974, "consistency": 1975, "24": 1976, "47": 1977, "62": 1978, "lization": 1979, "sum": 1980, "lelization": 1981, "rallelization": 1982, "parallelization": 1983, "82": 1984, "72": 1985, "nti": 1986, "denti": 1987, "confi": 1988, "ality": 1989, "dentiality": 1990, "confidentiality": 1991, "integral": 1992, "28": 1993, "58": 1994, "ence": 1995, "sali": 1996, "salience": 1997, "51": 1998, "Whe": 1999, "loop": 2000, "composition": 2001, "When": 2002, "65": 2003, "av": 2004, "asure": 2005, "measure": 2006, "aila": 2007, "lity": 2008, "bility": 2009, "replication": 2010, "availa": 2011, "measurement": 2012, "availability": 2013, "nking": 2014, "unking": 2015, "chunking": 2016, "44": 2017, "dit": 2018, "authen": 2019, "audit": 2020, "tication": 2021, "authentication": 2022, "ance": 2023, "ci": 2024, "gate": 2025, "ous": 2026, "orization": 2027, "thorization": 2028, "authorization": 2029, "ness": 2030, "liance": 2031, "consci": 2032, "compliance": 2033, "bias": 2034, "ousness": 2035, "consciousness": 2036, "37": 2037, "64": 2038, "integrity": 2039, "cor": 2040, "ning": 2041, "rection": 2042, "tuning": 2043, "correction": 2044, "tens": 2045, "quali": 2046, "tensor": 2047, "qualia": 2048, "clo": 2049, "olog": 2050, "top": 2051, "closure": 2052, "ology": 2053, "topology": 2054, "cal": 2055, "ion": 2056, "scal": 2057, "recurs": 2058, "scaling": 2059, "recursion": 2060, "ast": 2061, "ize": 2062, "optimize": 2063, "ctorization": 2064, "vectorization": 2065, "go": 2066, "ip": 2067, "nsu": 2068, "ption": 2069, "sensu": 2070, "consensu": 2071, "ssip": 2072, "encry": 2073, "gossip": 2074, "consensus": 2075, "encryption": 2076, "actor": 2077, "mm": 2078, "prog": 2079, "metaprog": 2080, "ramm": 2081, "refactor": 2082, "metaprogramm": 2083, "metaprogramming": 2084, "non": 2085, "udi": 2086, "repudi": 2087, "nonrepudi": 2088, "nonrepudiation": 2089, "54": 2090, "Er": 2091, "ft": 2092, "pt": 2093, "try": 2094, "raft": 2095, "cept": 2096, "except": 2097, "Error": 2098, "ection": 2099, "int": 2100, "position": 2101, "pection": 2102, "spection": 2103, "perposition": 2104, "superposition": 2105, "rospection": 2106, "introspection": 2107, "17": 2108, "ddi": 2109, "emb": 2110, "eddi": 2111, "embeddi": 2112, "embedding": 2113, "Class": 2114, "neural": 2115, "ching": 2116, "caching": 2117, "ap": 2118, "ang": 2119, "sulation": 2120, "entang": 2121, "encap": 2122, "entanglement": 2123, "encapsulation": 2124, "15": 2125, "ling": 2126, "profi": 2127, "profiling": 2128, "rix": 2129, "matrix": 2130, "mor": 2131, "mer": 2132, "oly": 2133, "phi": 2134, "poly": 2135, "sm": 2136, "tra": 2137, "former": 2138, "const": 2139, "nsformer": 2140, "morphi": 2141, "polymorphi": 2142, "transformer": 2143, "polymorphism": 2144, "it": 2145, "ab": 2146, "lf": 2147, "self": 2148, "stra": 2149, "abstra": 2150, "abstraction": 2151, "De": 2152, "fine": 2153, "Define": 2154, "Cre": 2155, "pu": 2156, "inpu": 2157, "Create": 2158, "input": 2159, "Ap": 2160, "be": 2161, "ply": 2162, "tte": 2163, "Apply": 2164, "bette": 2165, "better": 2166, "Imp": 2167, "Implement": 2168, "14": 2169, "16": 2170, "19": 2171, "87": 2172, "eld": 2173, "13": 2174, "en": 2175, "sures": 2176, "ensures": 2177, "20": 2178, "ccurs": 2179, "occurs": 2180, "sy": 2181, "stem": 2182, "matic": 2183, "ally": 2184, "auto": 2185, "system": 2186, "matically": 2187, "automatically": 2188, "rom": 2189, "use": 2190, "ruct": 2191, "uses": 2192, "18": 2193, "returns": 2194, "12": 2195, "ort": 2196, "import": 2197, "21": 2198, "10": 2199, "Re": 2200, "aw": 2201, "fn": 2202, "ait": 2203, "Result": 2204, "await": 2205, "init": 2206, "of": 2207, "let": 2208, "Si": 2209, "refor": 2210, "therefor": 2211, "Since": 2212, "therefore": 2213, "11": 2214, "ar": 2215, "var": 2216, "ructor": 2217, "constructor": 2218, "Sy": 2219, "olic": 2220, "mbolic": 2221, "Symbolic": 2222, "Pattern": 2223, "ield": 2224, "lt": 2225, "ue": 2226, "val": 2227, "yield": 2228, "ault": 2229, "default": 2230, "match": 2231, "value": 2232, "No": 2233, "Not": 2234, "From": 2235, "fo": 2236, "llo": 2237, "ws": 2238, "follo": 2239, "follows": 2240, "Eith": 2241, "Pro": 2242, "with": 2243, "comb": 2244, "operat": 2245, "cessor": 2246, "ines": 2247, "Either": 2248, "Processor": 2249, "combines": 2250, "operator": 2251, "Gi": 2252, "can": 2253, "du": 2254, "we": 2255, "dedu": 2256, "ven": 2257, "Given": 2258, "deduce": 2259, "Tru": 2260, "ecu": 2261, "execu": 2262, "True": 2263, "execute": 2264, "St": 2265, "i32": 2266, "struct": 2267, "implement": 2268, "field": 2269, "Struct": 2270, "implements": 2271, "22": 2272, "from": 2273, "main": 2274, "Be": 2275, "gin": 2276, "ph": 2277, "lyph": 2278, "roglyph": 2279, "neuroglyph": 2280, "Begin": 2281, "78ng": 2282, "56ng": 2283, "68ng": 2284, "32ng": 2285, "75ng": 2286, "82ng": 2287, "48ng": 2288, "86ng": 2289, "83ng": 2290, "16ng": 2291, "25ng": 2292, "70ng": 2293, "57ng": 2294, "30ng": 2295, "80ng": 2296, "31ng": 2297, "36ng": 2298, "55ng": 2299, "41ng": 2300, "51ng": 2301, "20ng": 2302, "21ng": 2303, "74ng": 2304, "52ng": 2305, "59ng": 2306, "43ng": 2307, "76ng": 2308, "34ng": 2309, "29ng": 2310, "35ng": 2311, "58ng": 2312, "17ng": 2313, "87ng": 2314, "49Error": 2315, "42ng": 2316, "77ng": 2317, "79ng": 2318, "45ng": 2319, "45Processor": 2320, "73ng": 2321, "85ng": 2322, "26ng": 2323, "33ng": 2324, "81ng": 2325, "63ng": 2326, "41Error": 2327, "24ng": 2328, "62ng": 2329, "72ng": 2330, "65ng": 2331, "44ng": 2332, "64ng": 2333, "15ng": 2334, "23": 2335, "6Error": 2336, "7ng": 2337, "8ng": 2338, "38ng": 2339, "49ng": 2340, "42Processor": 2341, "71ng": 2342, "75Error": 2343, "30Error": 2344, "53ng": 2345, "40ng": 2346, "83Processor": 2347, "61ng": 2348, "39ng": 2349, "50ng": 2350, "27ng": 2351, "29Error": 2352, "69ng": 2353, "47ng": 2354, "47Error": 2355, "62Error": 2356, "62Processor": 2357, "82Error": 2358, "28ng": 2359, "28Error": 2360, "44Error": 2361, "37ng": 2362, "17Error": 2363, "14Processor": 2364, "19Error": 2365, "13ng": 2366, "11ng": 2367, "1ng": 2368, "1Error": 2369, "3ng": 2370, "4ng": 2371, "5ng": 2372, "6ng": 2373, "8Processor": 2374, "9ng": 2375, "ℛng": 2376, "Ⅳng": 2377, "Ⅴng": 2378, "↨⎼": 2379, "⇊⅐": 2380, "≵⍊": 2381, "⊖⋌": 2382, "⋎⧏": 2383, "⋖⬬": 2384, "⌍⊅": 2385, "⎘⧎": 2386, "⏅⧜": 2387, "⏜⮩": 2388, "⏡⫨": 2389, "⦾🞳": 2390, "⯌🛇": 2391, "ꞏng": 2392, "𝐋Processor": 2393, "𝚎ng": 2394, "𝟬ng": 2395, "🢃⭻": 2396, "38Processor": 2397, "49Processor": 2398, "42Ⅳng": 2399, "66ng": 2400, "66Processor": 2401, "70Error": 2402, "70Processor": 2403, "56Error": 2404, "57Error": 2405, "57Processor": 2406, "60ng": 2407, "60Error": 2408, "60Processor": 2409, "79Processor": 2410, "30Processor": 2411, "73Processor": 2412, "48ⅴ": 2413, "48𝟬ng": 2414, "52Error": 2415, "53Error": 2416, "59Error": 2417, "68Error": 2418, "68Processor": 2419, "80Processor": 2420, "26Processor": 2421, "43Error": 2422, "83Error": 2423, "31Processor": 2424, "36Processor": 2425, "61Processor": 2426, "63Error": 2427, "63Processor": 2428, "39Processor": 2429, "55Error": 2430, "78Error": 2431, "78Processor": 2432, "46ng": 2433, "46Error": 2434, "46Processor": 2435, "27Error": 2436, "27Processor": 2437, "35Processor": 2438, "69Error": 2439, "69Processor": 2440, "82𝚶": 2441, "58Error": 2442, "51𝑜": 2443, "51Processor": 2444, "44Processor": 2445, "64Error": 2446, "54ng": 2447, "54Processor": 2448, "15Ⅴng": 2449, "14ng": 2450, "19ng": 2451, "13Error": 2452, "20Processor": 2453, "18ng": 2454, "18Error": 2455, "21Error": 2456, "10ng": 2457, "10𝚎ng": 2458, "11Error": 2459, "⯌🛇⇊⅐": 2460}