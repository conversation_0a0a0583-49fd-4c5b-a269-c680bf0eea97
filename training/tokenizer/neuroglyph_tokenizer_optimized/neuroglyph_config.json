{"model_type": "neuroglyph", "tokenizer_class": "PreTrainedTokenizerFast", "vocab_size": 8742, "zero_splitting_guaranteed": true, "symbol_count": 7767, "special_tokens": {"unk_token": "<UNK>", "pad_token": "<PAD>", "mask_token": "<MASK>", "additional_special_tokens": ["<NG_START>", "<NG_END>", "<NG_THINK>", "<NG_REASON>", "<NG_MEMORY>", "<NG_VALIDATE>", "<NG_ERROR>", "<NG_CORRECT>"]}, "encoding_settings": {"add_special_tokens": false, "skip_special_tokens": false, "clean_up_tokenization_spaces": false, "add_prefix_space": false}, "usage_recommendations": ["Usa add_special_tokens=False per preservare formato originale", "Usa skip_special_tokens=False per mantenere simboli NEUROGLYPH", "Normalizza spazi solo se necessario per confronti", "Testa sempre roundtrip fidelity prima dell'uso in produzione"]}