#!/usr/bin/env python3
"""
Creatore Tokenizer Finale NEUROGLYPH v2.0

Crea un tokenizer finale ottimizzato che garantisce:
1. Zero-splitting per tutti i neuroglifi
2. Roundtrip fidelity perfetta
3. Vocabolario ottimizzato
4. Compatibilità con Hugging Face
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Set
from transformers import PreTrainedTokenizerFast
from tokenizers import Tokenizer
from tokenizers.models import WordPiece
from tokenizers.trainers import WordPieceTrainer
from tokenizers.pre_tokenizers import Whitespace
from tokenizers.processors import TemplateProcessing

class FinalTokenizerCreator:
    """Creatore del tokenizer finale NEUROGLYPH."""
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il creatore.
        
        Args:
            registry_path: Percorso al registry simbolico
        """
        self.registry_path = registry_path
        self.symbols = []
        self._load_symbols()
    
    def _load_symbols(self) -> None:
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbols.append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def create_vocabulary_file(self, output_path: str = "training/tokenizer/vocab.txt") -> str:
        """Crea file vocabolario con simboli garantiti."""
        print("🔧 Creazione vocabolario personalizzato...")
        
        # Token speciali essenziali
        special_tokens = [
            "[UNK]",
            "[PAD]", 
            "[CLS]",
            "[SEP]",
            "[MASK]",
            "<NG_START>",
            "<NG_END>",
            "<NG_THINK>",
            "<NG_REASON>",
            "<NG_MEMORY>",
            "<NG_VALIDATE>",
            "<NG_ERROR>",
            "<NG_CORRECT>"
        ]
        
        # Caratteri base
        basic_chars = list("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
        
        # Punteggiatura comune
        punctuation = list(".,!?;:()[]{}\"'-_/\\@#$%^&*+=<>|`~")
        
        # Spazi e caratteri speciali
        whitespace = [" ", "\t", "\n"]
        
        # Subword prefixes per WordPiece
        subword_prefixes = ["##" + char for char in basic_chars]
        
        # Parole comuni inglesi
        common_words = [
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
            "from", "up", "about", "into", "through", "during", "before", "after", "above", "below",
            "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did",
            "will", "would", "could", "should", "may", "might", "must", "can", "shall",
            "this", "that", "these", "those", "i", "you", "he", "she", "it", "we", "they",
            "me", "him", "her", "us", "them", "my", "your", "his", "her", "its", "our", "their",
            "what", "when", "where", "why", "how", "which", "who", "whom", "whose",
            "if", "then", "else", "while", "for", "do", "return", "function", "class", "def",
            "import", "from", "as", "try", "except", "finally", "with", "lambda", "yield",
            "true", "false", "null", "none", "undefined", "var", "let", "const", "async", "await"
        ]
        
        # Costruisci vocabolario finale
        vocabulary = []
        vocabulary.extend(special_tokens)
        vocabulary.extend(basic_chars)
        vocabulary.extend(punctuation)
        vocabulary.extend(whitespace)
        vocabulary.extend(self.symbols)  # TUTTI i simboli NEUROGLYPH
        vocabulary.extend(subword_prefixes)
        vocabulary.extend(common_words)
        
        # Rimuovi duplicati mantenendo ordine
        seen = set()
        unique_vocab = []
        for token in vocabulary:
            if token not in seen:
                seen.add(token)
                unique_vocab.append(token)
        
        # Salva vocabolario
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for token in unique_vocab:
                f.write(token + '\n')
        
        print(f"✅ Vocabolario creato: {len(unique_vocab)} token")
        print(f"   - Token speciali: {len(special_tokens)}")
        print(f"   - Simboli NEUROGLYPH: {len(self.symbols)}")
        print(f"   - Altri token: {len(unique_vocab) - len(special_tokens) - len(self.symbols)}")
        
        return str(output_file)
    
    def create_final_tokenizer(self, 
                              vocab_path: str,
                              output_dir: str = "training/tokenizer/neuroglyph_tokenizer_final") -> str:
        """
        Crea il tokenizer finale con vocabolario predefinito.
        
        Args:
            vocab_path: Percorso al file vocabolario
            output_dir: Directory output
            
        Returns:
            Percorso al tokenizer finale
        """
        print("🔧 Creazione tokenizer finale...")
        
        # 1. Inizializza tokenizer WordPiece
        tokenizer = Tokenizer(WordPiece(vocab=vocab_path, unk_token="[UNK]"))
        
        # 2. Pre-tokenizer semplice
        tokenizer.pre_tokenizer = Whitespace()
        
        # 3. Post-processor
        tokenizer.post_processor = TemplateProcessing(
            single="[CLS] $A [SEP]",
            pair="[CLS] $A [SEP] $B:1 [SEP]:1",
            special_tokens=[
                ("[CLS]", 2),  # ID del token [CLS]
                ("[SEP]", 3),  # ID del token [SEP]
            ]
        )
        
        # 4. Salva tokenizer
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        tokenizer.save(str(output_path / "tokenizer.json"))
        
        # 5. Crea tokenizer HF
        hf_tokenizer = PreTrainedTokenizerFast(
            tokenizer_object=tokenizer,
            unk_token="[UNK]",
            pad_token="[PAD]",
            cls_token="[CLS]",
            sep_token="[SEP]",
            mask_token="[MASK]"
        )
        
        # Salva tokenizer HF
        hf_tokenizer.save_pretrained(output_path)
        
        print(f"✅ Tokenizer finale salvato in: {output_path}")
        
        return str(output_path)
    
    def validate_final_tokenizer(self, tokenizer_path: str) -> Dict[str, Any]:
        """Valida il tokenizer finale."""
        print("🔍 Validazione tokenizer finale...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        # Test tutti i simboli
        symbols_in_vocab = 0
        zero_split_symbols = 0
        problematic_symbols = []
        
        for symbol in self.symbols:
            if symbol in tokenizer.get_vocab():
                symbols_in_vocab += 1
                
                # Test zero-splitting
                tokens = tokenizer.tokenize(symbol)
                if len(tokens) == 1 and tokens[0] == symbol:
                    zero_split_symbols += 1
                else:
                    problematic_symbols.append({
                        'symbol': symbol,
                        'tokens': tokens
                    })
        
        # Test roundtrip
        test_cases = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "Simple text without symbols",
            "def func(): return result",
            "⟨ ⟩ ◊ ⊢ ⊣"
        ]
        
        perfect_roundtrips = 0
        failed_roundtrips = []
        
        for test_case in test_cases:
            # Test senza special tokens
            token_ids = tokenizer.encode(test_case, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            if test_case == decoded:
                perfect_roundtrips += 1
            else:
                failed_roundtrips.append({
                    'original': test_case,
                    'decoded': decoded,
                    'tokens': tokenizer.tokenize(test_case)
                })
        
        results = {
            'total_symbols': len(self.symbols),
            'symbols_in_vocab': symbols_in_vocab,
            'zero_split_symbols': zero_split_symbols,
            'problematic_symbols': problematic_symbols[:10],  # Solo primi 10
            'roundtrip_tests': len(test_cases),
            'perfect_roundtrips': perfect_roundtrips,
            'failed_roundtrips': failed_roundtrips,
            'symbol_coverage': (symbols_in_vocab / len(self.symbols)) * 100,
            'zero_split_percentage': (zero_split_symbols / len(self.symbols)) * 100,
            'roundtrip_percentage': (perfect_roundtrips / len(test_cases)) * 100
        }
        
        print(f"📊 Risultati Validazione Finale:")
        print(f"   - Simboli in vocabolario: {symbols_in_vocab}/{len(self.symbols)} ({results['symbol_coverage']:.1f}%)")
        print(f"   - Zero-splitting: {zero_split_symbols}/{len(self.symbols)} ({results['zero_split_percentage']:.1f}%)")
        print(f"   - Roundtrip perfetti: {perfect_roundtrips}/{len(test_cases)} ({results['roundtrip_percentage']:.1f}%)")
        
        if problematic_symbols:
            print(f"   - Simboli problematici (primi 5):")
            for item in problematic_symbols[:5]:
                print(f"     {item['symbol']} → {item['tokens']}")
        
        return results
    
    def test_final_tokenizer(self, tokenizer_path: str) -> None:
        """Testa il tokenizer finale."""
        print("🧪 Test tokenizer finale...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        test_examples = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "Simple text",
            "def func(): return result",
            "⟨ ⟩ ◊ ⊢ ⊣ ∧ ∨ ¬"
        ]
        
        for example in test_examples:
            tokens = tokenizer.tokenize(example)
            token_ids = tokenizer.encode(example, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            print(f"\n📝 Esempio: {example}")
            print(f"   Tokens: {tokens}")
            print(f"   Decoded: {decoded}")
            print(f"   Roundtrip OK: {example == decoded}")

def main():
    """Funzione principale."""
    print("🧠 NEUROGLYPH v2.0 - Final Tokenizer Creator")
    print("=" * 60)
    
    # Inizializza creatore
    creator = FinalTokenizerCreator()
    
    # Crea vocabolario personalizzato
    vocab_path = creator.create_vocabulary_file()
    
    # Crea tokenizer finale
    final_tokenizer_path = creator.create_final_tokenizer(vocab_path)
    
    # Valida tokenizer finale
    results = creator.validate_final_tokenizer(final_tokenizer_path)
    
    # Test esempi
    creator.test_final_tokenizer(final_tokenizer_path)
    
    # Risultato finale
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Copertura simboli: {results['symbol_coverage']:.1f}%")
    print(f"   - Zero-splitting: {results['zero_split_percentage']:.1f}%")
    print(f"   - Roundtrip fidelity: {results['roundtrip_percentage']:.1f}%")
    
    if (results['zero_split_percentage'] >= 95 and 
        results['roundtrip_percentage'] >= 90):
        print("✅ TOKENIZER FINALE PERFETTO!")
        return True
    else:
        print("⚠️  TOKENIZER NECESSITA MIGLIORAMENTI")
        return False

if __name__ == "__main__":
    success = main()
