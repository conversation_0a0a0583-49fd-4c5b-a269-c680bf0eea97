#!/usr/bin/env python3
"""
Trainer Tokenizer NEUROGLYPH v2.0 - Zero Splitting Garantito

Addestra un tokenizer personalizzato che garantisce:
1. Zero splitting per tutti i neuroglifi
2. Mappatura 1:1 simbolo-token
3. Token speciali per markup NEUROGLYPH
4. Compatibilità con Hugging Face Transformers
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Set
from tokenizers import Tokenizer, models, normalizers, pre_tokenizers, processors, trainers
from tokenizers.models import BPE
from tokenizers.trainers import BpeTrainer
from tokenizers.pre_tokenizers import Whitespace, Sequence
from tokenizers.processors import TemplateProcessing
from transformers import PreTrainedTokenizerFast
import re

class NeuroglyphTokenizerTrainer:
    """Trainer per tokenizer NEUROGLYPH con zero-splitting."""
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il trainer.
        
        Args:
            registry_path: Percorso al registry simbolico
        """
        self.registry_path = registry_path
        self.symbols = []
        self.special_tokens = []
        self._load_symbols()
        self._prepare_special_tokens()
    
    def _load_symbols(self) -> None:
        """Carica tutti i simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbols.append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli per zero-splitting")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def _prepare_special_tokens(self) -> None:
        """Prepara token speciali NEUROGLYPH."""
        self.special_tokens = [
            "<NG_START>",    # Inizio sequenza neuroglyph
            "<NG_END>",      # Fine sequenza neuroglyph
            "<NG_THINK>",    # Modalità pensiero
            "<NG_REASON>",   # Ragionamento simbolico
            "<NG_MEMORY>",   # Accesso memoria
            "<NG_VALIDATE>", # Validazione
            "<NG_ERROR>",    # Errore simbolico
            "<NG_CORRECT>",  # Correzione
            "<UNK>",         # Token sconosciuto
            "<PAD>",         # Padding
            "<BOS>",         # Begin of sequence
            "<EOS>",         # End of sequence
            "<MASK>"         # Masked token
        ]
        
        # Aggiungi tutti i simboli come token speciali per garantire zero-splitting
        self.special_tokens.extend(self.symbols)
        
        print(f"✅ Preparati {len(self.special_tokens)} token speciali")
    
    def create_custom_pre_tokenizer(self):
        """Crea pre-tokenizer personalizzato che preserva i neuroglifi."""
        
        class NeuroglyphPreTokenizer:
            """Pre-tokenizer che preserva i neuroglifi come unità atomiche."""
            
            def __init__(self, symbols: List[str]):
                self.symbols = set(symbols)
                # Crea pattern regex per identificare simboli
                escaped_symbols = [re.escape(symbol) for symbol in symbols]
                self.symbol_pattern = '|'.join(escaped_symbols)
                self.regex = re.compile(f'({self.symbol_pattern})|(\S+|\s+)')
            
            def pre_tokenize(self, pretok):
                """Pre-tokenizza preservando i neuroglifi."""
                text = pretok.text
                tokens = []
                
                for match in self.regex.finditer(text):
                    token_text = match.group()
                    start = match.start()
                    end = match.end()
                    
                    # Se è un simbolo, preservalo come unità atomica
                    if token_text in self.symbols:
                        tokens.append((token_text, (start, end)))
                    # Altrimenti, tokenizza normalmente
                    elif token_text.strip():  # Ignora spazi vuoti
                        tokens.append((token_text, (start, end)))
                
                return tokens
        
        return NeuroglyphPreTokenizer(self.symbols)
    
    def train_tokenizer(self, 
                       corpus_path: str,
                       vocab_size: int = 34000,
                       output_dir: str = "training/tokenizer/neuroglyph_tokenizer") -> str:
        """
        Addestra il tokenizer con zero-splitting garantito.
        
        Args:
            corpus_path: Percorso al corpus di training
            vocab_size: Dimensione vocabolario target
            output_dir: Directory output
            
        Returns:
            Percorso al tokenizer addestrato
        """
        print("🔧 Addestramento tokenizer NEUROGLYPH...")
        
        # 1. Inizializza tokenizer BPE
        tokenizer = Tokenizer(BPE(unk_token="<UNK>"))
        
        # 2. Configura normalizzazione (minima per preservare simboli)
        tokenizer.normalizer = normalizers.Sequence([
            normalizers.NFD(),  # Normalizzazione Unicode
            normalizers.StripAccents()  # Rimuovi accenti (ma preserva simboli)
        ])
        
        # 3. Pre-tokenizer personalizzato
        custom_pre_tokenizer = self.create_custom_pre_tokenizer()
        
        # Usa pre-tokenizer standard per ora (implementazione custom complessa)
        tokenizer.pre_tokenizer = pre_tokenizers.Sequence([
            pre_tokenizers.Whitespace(),
            pre_tokenizers.Punctuation()
        ])
        
        # 4. Configura trainer BPE
        trainer = BpeTrainer(
            vocab_size=vocab_size,
            special_tokens=self.special_tokens,
            min_frequency=1,  # Frequenza minima bassa per includere tutti i simboli
            show_progress=True,
            continuing_subword_prefix="##"
        )
        
        # 5. Addestra su corpus
        print(f"   - Training su corpus: {corpus_path}")
        print(f"   - Vocab size target: {vocab_size}")
        print(f"   - Token speciali: {len(self.special_tokens)}")
        
        tokenizer.train([corpus_path], trainer)
        
        # 6. Configura post-processor
        tokenizer.post_processor = TemplateProcessing(
            single="<BOS> $A <EOS>",
            pair="<BOS> $A <EOS> $B:1 <EOS>:1",
            special_tokens=[
                ("<BOS>", tokenizer.token_to_id("<BOS>")),
                ("<EOS>", tokenizer.token_to_id("<EOS>"))
            ]
        )
        
        # 7. Salva tokenizer
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        tokenizer.save(str(output_path / "tokenizer.json"))
        
        # 8. Crea tokenizer Hugging Face compatibile
        hf_tokenizer = PreTrainedTokenizerFast(
            tokenizer_object=tokenizer,
            unk_token="<UNK>",
            pad_token="<PAD>",
            bos_token="<BOS>",
            eos_token="<EOS>",
            mask_token="<MASK>"
        )
        
        # Salva tokenizer HF
        hf_tokenizer.save_pretrained(output_path)
        
        print(f"✅ Tokenizer salvato in: {output_path}")
        
        return str(output_path)
    
    def validate_zero_splitting(self, tokenizer_path: str) -> Dict[str, Any]:
        """
        Valida che il tokenizer garantisca zero-splitting per tutti i simboli.
        
        Args:
            tokenizer_path: Percorso al tokenizer addestrato
            
        Returns:
            Report di validazione
        """
        print("🔍 Validazione zero-splitting...")
        
        # Carica tokenizer
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        validation_results = {
            'total_symbols': len(self.symbols),
            'symbols_in_vocab': 0,
            'zero_split_guaranteed': 0,
            'problematic_symbols': [],
            'missing_symbols': [],
            'split_symbols': []
        }
        
        # Testa ogni simbolo
        for symbol in self.symbols:
            # Tokenizza il simbolo
            tokens = tokenizer.tokenize(symbol)
            token_ids = tokenizer.encode(symbol, add_special_tokens=False)
            
            # Verifica se il simbolo è nel vocabolario
            if symbol in tokenizer.get_vocab():
                validation_results['symbols_in_vocab'] += 1
                
                # Verifica zero-splitting (deve essere 1 token)
                if len(tokens) == 1 and len(token_ids) == 1:
                    validation_results['zero_split_guaranteed'] += 1
                else:
                    validation_results['split_symbols'].append({
                        'symbol': symbol,
                        'tokens': tokens,
                        'token_count': len(tokens)
                    })
            else:
                validation_results['missing_symbols'].append(symbol)
        
        # Calcola percentuali
        total = validation_results['total_symbols']
        in_vocab_pct = (validation_results['symbols_in_vocab'] / total) * 100
        zero_split_pct = (validation_results['zero_split_guaranteed'] / total) * 100
        
        print(f"📊 Risultati Validazione:")
        print(f"   - Simboli totali: {total}")
        print(f"   - Simboli in vocabolario: {validation_results['symbols_in_vocab']} ({in_vocab_pct:.1f}%)")
        print(f"   - Zero-splitting garantito: {validation_results['zero_split_guaranteed']} ({zero_split_pct:.1f}%)")
        print(f"   - Simboli mancanti: {len(validation_results['missing_symbols'])}")
        print(f"   - Simboli divisi: {len(validation_results['split_symbols'])}")
        
        # Mostra esempi problematici
        if validation_results['missing_symbols']:
            print(f"\n⚠️  Primi 10 simboli mancanti:")
            for symbol in validation_results['missing_symbols'][:10]:
                print(f"   - {symbol}")
        
        if validation_results['split_symbols']:
            print(f"\n❌ Primi 10 simboli divisi:")
            for item in validation_results['split_symbols'][:10]:
                print(f"   - {item['symbol']} → {item['tokens']} ({item['token_count']} token)")
        
        return validation_results
    
    def test_tokenizer(self, tokenizer_path: str) -> None:
        """Testa il tokenizer con esempi."""
        print("🧪 Test tokenizer...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        test_examples = [
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "The algorithm 🠫 uses 🟓 for optimization",
            "<NG_START> ⟨ ⟩ ◊ ⊢ <NG_END>",
            "def func_⟨(): return ⟩_result"
        ]
        
        for example in test_examples:
            tokens = tokenizer.tokenize(example)
            token_ids = tokenizer.encode(example)
            decoded = tokenizer.decode(token_ids)
            
            print(f"\n📝 Esempio: {example}")
            print(f"   Tokens: {tokens}")
            print(f"   IDs: {token_ids}")
            print(f"   Decoded: {decoded}")
            print(f"   Roundtrip OK: {example.strip() == decoded.strip()}")

def main():
    """Funzione principale."""
    print("🧠 NEUROGLYPH v2.0 - Tokenizer Trainer")
    print("=" * 60)
    
    # Inizializza trainer
    trainer = NeuroglyphTokenizerTrainer()
    
    # Addestra tokenizer
    corpus_path = "training/tokenizer/neuroglyph_corpus.txt"
    if not Path(corpus_path).exists():
        print(f"❌ Corpus non trovato: {corpus_path}")
        print("   Esegui prima: python training/tokenizer/corpus_generator.py")
        return False
    
    tokenizer_path = trainer.train_tokenizer(
        corpus_path=corpus_path,
        vocab_size=34000,
        output_dir="training/tokenizer/neuroglyph_tokenizer"
    )
    
    # Valida zero-splitting
    validation_results = trainer.validate_zero_splitting(tokenizer_path)
    
    # Test esempi
    trainer.test_tokenizer(tokenizer_path)
    
    # Risultato finale
    zero_split_pct = (validation_results['zero_split_guaranteed'] / validation_results['total_symbols']) * 100
    
    print(f"\n🎯 RISULTATO FINALE:")
    if zero_split_pct >= 95:
        print(f"✅ SUCCESSO! Zero-splitting: {zero_split_pct:.1f}%")
    else:
        print(f"⚠️  PARZIALE: Zero-splitting: {zero_split_pct:.1f}%")
        print("   Necessario miglioramento per raggiungere 95%+")
    
    return zero_split_pct >= 95

if __name__ == "__main__":
    success = main()
