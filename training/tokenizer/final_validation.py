#!/usr/bin/env python3
"""
Validazione Finale Tokenizer NEUROGLYPH v2.0

Test rapido e completo per confermare che il tokenizer ibrido
funziona perfettamente con zero-splitting e roundtrip fidelity.
"""

import json
import random
from pathlib import Path
from typing import Dict, List, Any
from transformers import PreTrainedTokenizerFast

def quick_validation_test(
    tokenizer_path: str = "training/tokenizer/neuroglyph_tokenizer_hybrid",
    registry_path: str = "neuroglyph_ULTIMATE_registry.json"
) -> Dict[str, Any]:
    """Test rapido di validazione."""
    
    print("🧠 NEUROGLYPH v2.0 - Validazione Finale")
    print("=" * 60)
    
    # Carica tokenizer
    try:
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        print(f"✅ Tokenizer caricato: {tokenizer_path}")
        print(f"   - Vocabolario: {len(tokenizer)} token")
    except Exception as e:
        print(f"❌ Errore caricamento tokenizer: {e}")
        return {"error": str(e)}
    
    # Carica simboli
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            registry = json.load(f)
        
        symbols = []
        for symbol_data in registry.get('approved_symbols', []):
            symbol = symbol_data.get('symbol')
            if symbol:
                symbols.append(symbol)
        
        print(f"✅ Simboli caricati: {len(symbols)}")
    except Exception as e:
        print(f"❌ Errore caricamento simboli: {e}")
        return {"error": str(e)}
    
    # Test 1: Copertura simboli (campione)
    print("\n🔍 Test 1: Copertura Simboli (campione 100)")
    vocab = tokenizer.get_vocab()
    sample_symbols = random.sample(symbols, min(100, len(symbols)))
    
    symbols_in_vocab = 0
    for symbol in sample_symbols:
        if symbol in vocab:
            symbols_in_vocab += 1
    
    coverage_pct = (symbols_in_vocab / len(sample_symbols)) * 100
    print(f"   - Simboli in vocabolario: {symbols_in_vocab}/{len(sample_symbols)} ({coverage_pct:.1f}%)")
    
    # Test 2: Zero-splitting (campione)
    print("\n🔍 Test 2: Zero-Splitting (campione 50)")
    zero_split_symbols = 0
    test_symbols = random.sample(symbols, min(50, len(symbols)))
    
    for symbol in test_symbols:
        tokens = tokenizer.tokenize(symbol)
        if len(tokens) == 1 and tokens[0] == symbol:
            zero_split_symbols += 1
    
    zero_split_pct = (zero_split_symbols / len(test_symbols)) * 100
    print(f"   - Zero-splitting perfetto: {zero_split_symbols}/{len(test_symbols)} ({zero_split_pct:.1f}%)")
    
    # Test 3: Roundtrip Fidelity
    print("\n🔍 Test 3: Roundtrip Fidelity")
    
    test_cases = [
        # Simboli singoli
        symbols[0], symbols[1], symbols[2], symbols[3], symbols[4],
        
        # Frasi con simboli
        f"Create a function {symbols[0]} to process {symbols[1]} data",
        f"If {symbols[2]} then {symbols[3]} else {symbols[4]}",
        f"The {symbols[5]} operator combines {symbols[6]} with {symbols[7]}",
        
        # Testo normale
        "Hello world",
        "Simple text without symbols",
        "def function(): return result",
        "x == y and z != w",
        
        # Markup NEUROGLYPH
        f"<NG_START> {symbols[0]} {symbols[1]} <NG_END>",
        f"<NG_THINK> {symbols[2]} {symbols[3]} </NG_THINK>",
        
        # Casi misti
        f"def process_{symbols[0]}(): return {symbols[1]}",
        f"The function {symbols[2]} implements algorithm",
        
        # Casi edge
        f" {symbols[0]} ",
        f"{symbols[1]},{symbols[2]}",
        f"({symbols[3]})",
        f"[{symbols[4]}]"
    ]
    
    perfect_roundtrips = 0
    failed_cases = []
    
    for i, test_case in enumerate(test_cases):
        try:
            # Test roundtrip
            token_ids = tokenizer.encode(test_case, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            if test_case == decoded:
                perfect_roundtrips += 1
                print(f"   ✅ {i+1:2d}: PASS")
            else:
                failed_cases.append({
                    'original': test_case,
                    'decoded': decoded
                })
                print(f"   ❌ {i+1:2d}: FAIL - '{test_case}' → '{decoded}'")
                
        except Exception as e:
            failed_cases.append({
                'original': test_case,
                'error': str(e)
            })
            print(f"   ❌ {i+1:2d}: ERROR - {e}")
    
    roundtrip_pct = (perfect_roundtrips / len(test_cases)) * 100
    print(f"   - Roundtrip perfetti: {perfect_roundtrips}/{len(test_cases)} ({roundtrip_pct:.1f}%)")
    
    # Test 4: Performance e Efficienza
    print("\n🔍 Test 4: Performance")
    
    # Test velocità encoding
    import time
    test_text = f"This is a test with symbols {symbols[0]} and {symbols[1]} and normal text."
    
    start_time = time.time()
    for _ in range(100):
        tokens = tokenizer.encode(test_text)
    encoding_time = time.time() - start_time
    
    print(f"   - Encoding 100x: {encoding_time:.3f}s ({encoding_time*10:.1f}ms per call)")
    
    # Test efficienza token
    tokens = tokenizer.tokenize(test_text)
    efficiency = len(test_text) / len(tokens)
    print(f"   - Efficienza: {efficiency:.2f} caratteri/token")
    
    # Risultati finali
    results = {
        'coverage_percentage': coverage_pct,
        'zero_split_percentage': zero_split_pct,
        'roundtrip_percentage': roundtrip_pct,
        'failed_roundtrips': len(failed_cases),
        'encoding_time_ms': encoding_time * 10,
        'efficiency_chars_per_token': efficiency,
        'vocab_size': len(tokenizer),
        'symbols_tested': len(test_symbols),
        'roundtrip_tests': len(test_cases)
    }
    
    overall_score = (coverage_pct + zero_split_pct + roundtrip_pct) / 3
    results['overall_score'] = overall_score
    
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Copertura simboli: {coverage_pct:.1f}%")
    print(f"   - Zero-splitting: {zero_split_pct:.1f}%")
    print(f"   - Roundtrip fidelity: {roundtrip_pct:.1f}%")
    print(f"   - Punteggio complessivo: {overall_score:.1f}/100")
    
    if overall_score >= 95:
        print("🎊 TOKENIZER VALIDATO CON SUCCESSO!")
        print("✅ Pronto per l'uso in produzione")
        print("✅ Zero-splitting garantito")
        print("✅ Roundtrip fidelity eccellente")
    elif overall_score >= 85:
        print("✅ TOKENIZER BUONO")
        print("⚠️  Alcuni miglioramenti possibili")
    else:
        print("⚠️  TOKENIZER NECESSITA MIGLIORAMENTI")
    
    return results

def generate_final_report(results: Dict[str, Any], output_path: str = "training/tokenizer/final_validation_report.md") -> None:
    """Genera report finale di validazione."""
    
    report = []
    report.append("# NEUROGLYPH Tokenizer - Validazione Finale")
    report.append("=" * 60)
    report.append("")
    
    # Sommario
    overall_score = results.get('overall_score', 0)
    
    if overall_score >= 95:
        status = "🎊 ECCELLENTE"
    elif overall_score >= 85:
        status = "✅ BUONO"
    else:
        status = "⚠️ NECESSITA MIGLIORAMENTI"
    
    report.append(f"## {status}")
    report.append(f"**Punteggio Complessivo**: {overall_score:.1f}/100")
    report.append("")
    
    # Metriche
    report.append("## 📊 METRICHE PRINCIPALI")
    report.append(f"- **Copertura Simboli**: {results.get('coverage_percentage', 0):.1f}%")
    report.append(f"- **Zero-Splitting**: {results.get('zero_split_percentage', 0):.1f}%")
    report.append(f"- **Roundtrip Fidelity**: {results.get('roundtrip_percentage', 0):.1f}%")
    report.append("")
    
    # Dettagli tecnici
    report.append("## 🔧 DETTAGLI TECNICI")
    report.append(f"- **Vocabolario**: {results.get('vocab_size', 0):,} token")
    report.append(f"- **Simboli testati**: {results.get('symbols_tested', 0)}")
    report.append(f"- **Test roundtrip**: {results.get('roundtrip_tests', 0)}")
    report.append(f"- **Test falliti**: {results.get('failed_roundtrips', 0)}")
    report.append("")
    
    # Performance
    report.append("## ⚡ PERFORMANCE")
    report.append(f"- **Velocità encoding**: {results.get('encoding_time_ms', 0):.1f}ms per chiamata")
    report.append(f"- **Efficienza**: {results.get('efficiency_chars_per_token', 0):.2f} caratteri/token")
    report.append("")
    
    # Conclusioni
    report.append("## 🎯 CONCLUSIONI")
    
    if overall_score >= 95:
        report.append("✅ **TOKENIZER PERFETTO** - Pronto per produzione")
        report.append("✅ Zero-splitting garantito per tutti i simboli NEUROGLYPH")
        report.append("✅ Roundtrip fidelity eccellente")
        report.append("✅ Performance ottimali")
    elif overall_score >= 85:
        report.append("✅ **TOKENIZER BUONO** - Utilizzabile con attenzione")
        report.append("⚠️ Alcuni miglioramenti raccomandati")
    else:
        report.append("⚠️ **TOKENIZER NECESSITA MIGLIORAMENTI**")
        report.append("❌ Non raccomandato per produzione")
    
    report.append("")
    
    # Salva report
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"\n📄 Report finale salvato: {output_path}")

def main():
    """Funzione principale."""
    
    # Esegui validazione
    results = quick_validation_test()
    
    if 'error' not in results:
        # Genera report
        generate_final_report(results)
        
        # Risultato finale
        overall_score = results.get('overall_score', 0)
        
        if overall_score >= 95:
            print(f"\n🎊 MISSIONE COMPIUTA!")
            print(f"📁 Tokenizer finale: training/tokenizer/neuroglyph_tokenizer_hybrid")
            print(f"🚀 Pronto per l'integrazione con il modello LLM")
            return True
        else:
            print(f"\n⚠️ Necessari ulteriori miglioramenti")
            return False
    else:
        print(f"\n❌ Errore durante la validazione: {results['error']}")
        return False

if __name__ == "__main__":
    success = main()
