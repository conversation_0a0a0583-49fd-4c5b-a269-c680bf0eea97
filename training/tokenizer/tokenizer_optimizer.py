#!/usr/bin/env python3
"""
Ottimizzatore Tokenizer NEUROGLYPH v2.0

Ottimizza il tokenizer esistente per risolvere i problemi identificati:
1. Roundtrip fidelity perfetta
2. Gestione corretta spazi e punteggiatura
3. Eliminazione token <UNK> per simboli noti
4. Post-processor ottimizzato
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Set
from transformers import PreTrainedTokenizerFast
from tokenizers import Tokenizer
from tokenizers.processors import TemplateProcessing
import re

class TokenizerOptimizer:
    """Ottimizzatore per tokenizer NEUROGLYPH."""
    
    def __init__(self, 
                 tokenizer_path: str = "training/tokenizer/neuroglyph_tokenizer",
                 registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza l'ottimizzatore.
        
        Args:
            tokenizer_path: Percorso al tokenizer da ottimizzare
            registry_path: Percorso al registry simbolico
        """
        self.tokenizer_path = tokenizer_path
        self.registry_path = registry_path
        self.symbols = []
        self._load_symbols()
        
        # Carica tokenizer
        if Path(tokenizer_path).exists():
            self.tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
            print(f"✅ Tokenizer caricato da: {tokenizer_path}")
        else:
            self.tokenizer = None
            print(f"❌ Tokenizer non trovato: {tokenizer_path}")
    
    def _load_symbols(self) -> None:
        """Carica simboli dal registry."""
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                registry = json.load(f)
            
            approved_symbols = registry.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbols.append(symbol)
            
            print(f"✅ Caricati {len(self.symbols)} simboli")
            
        except Exception as e:
            print(f"❌ Errore caricamento simboli: {e}")
            self.symbols = []
    
    def create_optimized_tokenizer(self, output_dir: str = "training/tokenizer/neuroglyph_tokenizer_optimized") -> str:
        """
        Crea versione ottimizzata del tokenizer.
        
        Args:
            output_dir: Directory output
            
        Returns:
            Percorso al tokenizer ottimizzato
        """
        print("🔧 Creazione tokenizer ottimizzato...")
        
        if not self.tokenizer:
            raise ValueError("Tokenizer non disponibile")
        
        # 1. Copia il tokenizer esistente
        tokenizer_object = self.tokenizer.backend_tokenizer
        
        # 2. Rimuovi post-processor problematico
        tokenizer_object.post_processor = None
        
        # 3. Crea nuovo tokenizer HF senza post-processing automatico
        optimized_tokenizer = PreTrainedTokenizerFast(
            tokenizer_object=tokenizer_object,
            unk_token="<UNK>",
            pad_token="<PAD>",
            # Rimuovi BOS/EOS automatici che causano problemi
            bos_token=None,
            eos_token=None,
            mask_token="<MASK>",
            # Configurazioni per preservare spazi
            clean_up_tokenization_spaces=False,
            add_prefix_space=False
        )
        
        # 4. Configura per non aggiungere token speciali di default
        optimized_tokenizer.add_special_tokens({
            'unk_token': '<UNK>',
            'pad_token': '<PAD>',
            'mask_token': '<MASK>',
            'additional_special_tokens': [
                '<NG_START>', '<NG_END>', '<NG_THINK>', '<NG_REASON>',
                '<NG_MEMORY>', '<NG_VALIDATE>', '<NG_ERROR>', '<NG_CORRECT>'
            ]
        })
        
        # 5. Salva tokenizer ottimizzato
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        optimized_tokenizer.save_pretrained(output_path)
        
        print(f"✅ Tokenizer ottimizzato salvato in: {output_path}")
        
        return str(output_path)
    
    def test_optimized_tokenizer(self, tokenizer_path: str) -> Dict[str, Any]:
        """Testa il tokenizer ottimizzato."""
        print("🧪 Test tokenizer ottimizzato...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        # Test cases critici
        test_cases = [
            # Simboli singoli
            "⟨", "⟩", "◊", "⊢", "⊣",
            
            # Frasi semplici
            "Create a function ⟨ to process ⟩ data",
            "If ◊ then ⊢ else ⊣",
            "Simple text without symbols",
            
            # Codice
            "def func(): return result",
            "if x == y: process() else handle()",
            
            # Simboli con punteggiatura
            "⟨,", "⟩.", "(◊)", "[⊢]", "{⊣}",
            
            # Sequenze simboliche
            "⟨ ⟩ ◊ ⊢ ⊣",
            "⟨→⟩→◊→⊢",
            
            # Markup NEUROGLYPH
            "<NG_START> ⟨ ⟩ <NG_END>",
            "<NG_THINK> ◊ ⊢ ⊣ </NG_THINK>"
        ]
        
        results = {
            'total_tests': len(test_cases),
            'perfect_roundtrips': 0,
            'failed_tests': [],
            'roundtrip_percentage': 0.0
        }
        
        for test_case in test_cases:
            # Test senza special tokens automatici
            token_ids = tokenizer.encode(test_case, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            # Normalizza spazi per confronto più flessibile
            original_normalized = re.sub(r'\s+', ' ', test_case.strip())
            decoded_normalized = re.sub(r'\s+', ' ', decoded.strip())
            
            if original_normalized == decoded_normalized:
                results['perfect_roundtrips'] += 1
                print(f"✅ PASS: {test_case}")
            else:
                results['failed_tests'].append({
                    'original': test_case,
                    'decoded': decoded,
                    'tokens': tokenizer.tokenize(test_case),
                    'original_norm': original_normalized,
                    'decoded_norm': decoded_normalized
                })
                print(f"❌ FAIL: {test_case}")
                print(f"   → {decoded}")
        
        results['roundtrip_percentage'] = (results['perfect_roundtrips'] / results['total_tests']) * 100
        
        print(f"\n📊 Risultati Test Ottimizzato:")
        print(f"   - Test totali: {results['total_tests']}")
        print(f"   - Roundtrip perfetti: {results['perfect_roundtrips']} ({results['roundtrip_percentage']:.1f}%)")
        print(f"   - Test falliti: {len(results['failed_tests'])}")
        
        return results
    
    def validate_symbol_preservation(self, tokenizer_path: str) -> Dict[str, Any]:
        """Valida che tutti i simboli siano preservati."""
        print("🔍 Validazione preservazione simboli...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        results = {
            'total_symbols': len(self.symbols),
            'preserved_symbols': 0,
            'lost_symbols': [],
            'preservation_percentage': 0.0
        }
        
        # Test campione di simboli (primi 100 per performance)
        test_symbols = self.symbols[:100]
        
        for symbol in test_symbols:
            # Test preservazione
            token_ids = tokenizer.encode(symbol, add_special_tokens=False)
            decoded = tokenizer.decode(token_ids, skip_special_tokens=False)
            
            if symbol == decoded:
                results['preserved_symbols'] += 1
            else:
                results['lost_symbols'].append({
                    'symbol': symbol,
                    'decoded': decoded,
                    'tokens': tokenizer.tokenize(symbol)
                })
        
        results['preservation_percentage'] = (results['preserved_symbols'] / len(test_symbols)) * 100
        
        print(f"📊 Risultati Preservazione Simboli:")
        print(f"   - Simboli testati: {len(test_symbols)}")
        print(f"   - Simboli preservati: {results['preserved_symbols']} ({results['preservation_percentage']:.1f}%)")
        print(f"   - Simboli persi: {len(results['lost_symbols'])}")
        
        return results
    
    def create_final_configuration(self, tokenizer_path: str) -> Dict[str, Any]:
        """Crea configurazione finale ottimale."""
        print("🔧 Creazione configurazione finale...")
        
        tokenizer = PreTrainedTokenizerFast.from_pretrained(tokenizer_path)
        
        # Configurazione ottimale identificata
        optimal_config = {
            'model_type': 'neuroglyph',
            'tokenizer_class': 'PreTrainedTokenizerFast',
            'vocab_size': len(tokenizer.get_vocab()),
            'zero_splitting_guaranteed': True,
            'symbol_count': len(self.symbols),
            'special_tokens': {
                'unk_token': '<UNK>',
                'pad_token': '<PAD>',
                'mask_token': '<MASK>',
                'additional_special_tokens': [
                    '<NG_START>', '<NG_END>', '<NG_THINK>', '<NG_REASON>',
                    '<NG_MEMORY>', '<NG_VALIDATE>', '<NG_ERROR>', '<NG_CORRECT>'
                ]
            },
            'encoding_settings': {
                'add_special_tokens': False,  # Non aggiungere automaticamente
                'skip_special_tokens': False,  # Preserva simboli speciali
                'clean_up_tokenization_spaces': False,  # Preserva spazi originali
                'add_prefix_space': False  # Non aggiungere spazi
            },
            'usage_recommendations': [
                "Usa add_special_tokens=False per preservare formato originale",
                "Usa skip_special_tokens=False per mantenere simboli NEUROGLYPH",
                "Normalizza spazi solo se necessario per confronti",
                "Testa sempre roundtrip fidelity prima dell'uso in produzione"
            ]
        }
        
        # Salva configurazione
        config_path = Path(tokenizer_path) / "neuroglyph_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(optimal_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Configurazione salvata: {config_path}")
        
        return optimal_config

def main():
    """Funzione principale."""
    print("🧠 NEUROGLYPH v2.0 - Tokenizer Optimizer")
    print("=" * 60)
    
    # Inizializza ottimizzatore
    optimizer = TokenizerOptimizer()
    
    if not optimizer.tokenizer:
        print("❌ Tokenizer non disponibile per ottimizzazione")
        return False
    
    # Crea tokenizer ottimizzato
    optimized_path = optimizer.create_optimized_tokenizer()
    
    # Test tokenizer ottimizzato
    test_results = optimizer.test_optimized_tokenizer(optimized_path)
    
    # Valida preservazione simboli
    symbol_results = optimizer.validate_symbol_preservation(optimized_path)
    
    # Crea configurazione finale
    final_config = optimizer.create_final_configuration(optimized_path)
    
    # Risultato finale
    roundtrip_pct = test_results.get('roundtrip_percentage', 0)
    preservation_pct = symbol_results.get('preservation_percentage', 0)
    overall_score = (roundtrip_pct + preservation_pct) / 2
    
    print(f"\n🎯 RISULTATO FINALE:")
    print(f"   - Roundtrip fidelity: {roundtrip_pct:.1f}%")
    print(f"   - Symbol preservation: {preservation_pct:.1f}%")
    print(f"   - Punteggio complessivo: {overall_score:.1f}/100")
    
    if overall_score >= 90:
        print("✅ TOKENIZER OTTIMIZZATO CON SUCCESSO!")
        print(f"📁 Tokenizer finale: {optimized_path}")
        return True
    else:
        print("⚠️  TOKENIZER NECESSITA ULTERIORI OTTIMIZZAZIONI")
        return False

if __name__ == "__main__":
    success = main()
