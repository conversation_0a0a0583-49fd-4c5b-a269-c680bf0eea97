# tokenizer_fix_and_train.py

from tokenizers import Tokenizer, models, trainers, pre_tokenizers, normalizers
from tokenizers.processors import TemplateProcessing
from tokenizers import AddedToken
import json
import os

# ------------------ PARAMETRI ------------------

# Percorso del corpus già generato
corpus_path = "training/tokenizer/neuroglyph_corpus.txt"

# Percorso al file JSON con tutti i simboli (registry)
symbols_path = "neuroglyph_ULTIMATE_registry.json"

# Directory di output per il tokenizer ottimizzato
output_dir = "training/tokenizer/neuroglyph_tokenizer_fixed"

# Numero di token totali desiderati (inclusi simboli + vocab "normale")
vocab_size = 34000

# ------------ FINE PARAMETRI -------------------

def load_all_symbols(registry_json_path):
    """
    Carica dal registry JSON la lista di tutti i neuroglyph come stringhe.
    Adattato per la struttura NEUROGLYPH registry.
    """
    with open(registry_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    symbols = []
    
    # Struttura NEUROGLYPH: approved_symbols è una lista di oggetti
    if "approved_symbols" in data:
        for entry in data["approved_symbols"]:
            symbol = entry.get("symbol")
            if symbol:
                symbols.append(symbol)
    else:
        # Fallback per altre strutture
        if "symbols" in data:
            for entry in data["symbols"]:
                symbols.append(entry["symbol"])
        else:
            # Ultimo fallback generico
            for dom, items in data.get("symbol_categories", {}).items():
                for sym, _ in items.get("symbols", {}).items():
                    symbols.append(sym)
    
    print(f"✅ Caricati {len(symbols)} simboli dal registry")
    return symbols

# Carichiamo la lista di simboli grezzi dal registry
neuroglyph_symbols = load_all_symbols(symbols_path)

# Aggiungiamo anche i token di markup <NG_START>, <NG_END>, <NG_THINK>, ecc.
special_markers = [
    "<NG_START>",
    "<NG_END>",
    "<NG_THINK>",
    "</NG_THINK>",
    "<NG_REASON>",
    "</NG_REASON>",
    "<NG_MEMORY>",
    "</NG_MEMORY>",
    "<NG_VALIDATE>",
    "</NG_VALIDATE>",
    "<NG_ERROR>",
    "</NG_ERROR>",
    "<NG_CORRECT>",
    "</NG_CORRECT>"
]

print(f"✅ Preparati {len(special_markers)} marker speciali")

# Creiamo la lista di AddedToken
added_tokens = []
for s in neuroglyph_symbols:
    # Ogni simbolo è un token atomico, non deve essere splittato
    added_tokens.append(AddedToken(s,
                                  single_word=True,
                                  lstrip=False,
                                  rstrip=False))

for marker in special_markers:
    added_tokens.append(AddedToken(marker,
                                  single_word=True,
                                  lstrip=False,
                                  rstrip=False))

print(f"✅ Creati {len(added_tokens)} AddedToken (simboli + marker)")

# ---------------- COSTRUZIONE DEL TOKENIZER ----------------

print("🔧 Costruzione tokenizer personalizzato...")

# 1. Creiamo un tokenizer BPE vuoto
tokenizer = Tokenizer(models.BPE())

# 2. Normalizzazione minima per preservare simboli
tokenizer.normalizer = normalizers.Sequence([])

# 3. Pre-tokenizer semplice che preserva i token aggiunti
# Usiamo una sequenza di pre-tokenizer standard

# Il pre-tokenizer divide su spazi e punteggiatura
# I simboli saranno preservati dai AddedToken
tokenizer.pre_tokenizer = pre_tokenizers.Sequence([
    pre_tokenizers.Whitespace(),
    pre_tokenizers.Punctuation()
])

print("✅ Pre-tokenizer personalizzato configurato")

# 4. Aggiungiamo i AddedToken (garantisce atomicità per simboli e marker)
tokenizer.add_tokens(added_tokens)

print(f"✅ Aggiunti {len(added_tokens)} token speciali")

# 5. Creiamo un trainer BPE
trainer = trainers.BpeTrainer(
    vocab_size=vocab_size,
    special_tokens=[
        "<BOS>",
        "<EOS>", 
        "<PAD>",
        "<UNK>",
        "<MASK>"
    ],
    show_progress=True,
    min_frequency=1  # Frequenza minima bassa per includere tutti i simboli
)

print("✅ Trainer BPE configurato")

# 6. Verifica che il corpus esista
if not os.path.exists(corpus_path):
    print(f"❌ Corpus non trovato: {corpus_path}")
    print("   Esegui prima: python3 training/tokenizer/corpus_generator.py")
    exit(1)

print(f"✅ Corpus trovato: {corpus_path}")

# 7. Alleniamo il tokenizer sul corpus
print("🔧 Addestramento tokenizer in corso...")
tokenizer.train(files=[corpus_path], trainer=trainer)

print("✅ Addestramento completato")

# 8. Impostiamo il post-processing (aggiunta automatica di BOS/EOS)
tokenizer.post_processor = TemplateProcessing(
    single="<BOS> $A <EOS>",
    pair="<BOS> $A <EOS> $B:1 <EOS>:1",
    special_tokens=[
        ("<BOS>", tokenizer.token_to_id("<BOS>")),
        ("<EOS>", tokenizer.token_to_id("<EOS>"))
    ]
)

print("✅ Post-processor configurato")

# 9. Creiamo la cartella di output se non esiste
os.makedirs(output_dir, exist_ok=True)

# 10. Salviamo il tokenizer
tokenizer.save(os.path.join(output_dir, "tokenizer.json"))

# Salva anche vocabolario e config per Transformers
try:
    tokenizer.model.save(output_dir)
except:
    print("⚠️  Salvataggio modello fallito, ma tokenizer.json salvato")

# Crea un file di config per Transformers
config = {
    "model_max_length": 2048,
    "tokenizer_class": "PreTrainedTokenizerFast",
    "unk_token": "<UNK>",
    "bos_token": "<BOS>",
    "eos_token": "<EOS>",
    "pad_token": "<PAD>",
    "mask_token": "<MASK>",
    "clean_up_tokenization_spaces": False,
    "add_prefix_space": False,
    "neuroglyph_symbols_count": len(neuroglyph_symbols),
    "special_markers_count": len(special_markers),
    "zero_splitting_guaranteed": True
}

with open(os.path.join(output_dir, "tokenizer_config.json"), "w", encoding="utf-8") as f:
    json.dump(config, f, ensure_ascii=False, indent=2)

print(f"✅ Tokenizer ricostruito e salvato in: {output_dir}")

# ----------------- VALIDAZIONE RAPIDA -----------------

def test_roundtrip(text, show_tokens=False):
    """Test roundtrip con opzione di mostrare token."""
    try:
        encoding = tokenizer.encode(text)
        decoded = tokenizer.decode(encoding.ids, skip_special_tokens=True)
        
        # Normalizza spazi per confronto
        original_norm = ' '.join(text.split())
        decoded_norm = ' '.join(decoded.split())
        
        ok = original_norm == decoded_norm
        
        print(f"Input: '{text}'")
        print(f"Decoded: '{decoded}'")
        if show_tokens:
            tokens = tokenizer.encode(text).tokens
            print(f"Tokens: {tokens}")
        print(f"Roundtrip OK? {ok}")
        print("-" * 50)
        
        return ok
    except Exception as e:
        print(f"❌ Errore nel test: {e}")
        return False

print("\n🧪 Test di validazione roundtrip:")
print("=" * 50)

# Test simboli singoli
test_cases = [
    # Simboli singoli critici
    "⟨",
    "⟩", 
    "◊",
    "⊢",
    "⊣",
    
    # Frasi con simboli
    "Create a function ⟨ to process ⟩ data",
    "If ◊ then ⊢ else ⊣",
    
    # Markup NEUROGLYPH
    "<NG_START> ⟨ ◊ ⊢ <NG_END>",
    "<NG_THINK> ◊ ⊢ ⊣ </NG_THINK>",
    
    # Codice
    "def func(): return result",
    "x == y and z != w",
    
    # Testo normale
    "Hello world",
    "Simple text without symbols"
]

passed_tests = 0
total_tests = len(test_cases)

for test_case in test_cases:
    if test_roundtrip(test_case):
        passed_tests += 1

print(f"\n🎯 RISULTATO VALIDAZIONE:")
print(f"   - Test passati: {passed_tests}/{total_tests}")
print(f"   - Percentuale successo: {(passed_tests/total_tests)*100:.1f}%")

if passed_tests == total_tests:
    print("🎊 TUTTI I TEST PASSATI! Tokenizer perfetto per roundtrip.")
else:
    print("⚠️  Alcuni test falliti. Verifica la configurazione.")

print(f"\n📁 Tokenizer salvato in: {output_dir}")
print("📋 File creati:")
print(f"   - tokenizer.json")
print(f"   - tokenizer_config.json")
print(f"   - vocab.json (se disponibile)")
print(f"   - merges.txt (se disponibile)")

print("\n🔄 Prossimi passi:")
print("1. Esegui: python3 training/tokenizer/zero_splitting_verifier.py")
print("2. Esegui: python3 training/tokenizer/tokenizer_validator.py")
print("3. Verifica che entrambi riportino 100% zero-splitting e roundtrip fidelity")
