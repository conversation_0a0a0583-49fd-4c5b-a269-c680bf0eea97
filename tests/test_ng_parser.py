#!/usr/bin/env python3
"""
Test per NG_PARSER - Parsing Simbolico Deterministico

Test modulare per verificare il funzionamento del primo modulo
della pipeline NEUROGLYPH v2.0.
"""

import sys
import os
import pytest
from pathlib import Path

# Aggiungi il path del progetto
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from neuroglyph.core.parser.ng_parser import NGParser, ParsedPrompt, ParsedToken

class TestNGParser:
    """Test suite per NG_PARSER."""
    
    @pytest.fixture
    def parser(self):
        """Fixture per creare un'istanza del parser."""
        registry_path = project_root / "neuroglyph_ULTIMATE_registry.json"
        if not registry_path.exists():
            pytest.skip(f"Registry non trovato: {registry_path}")
        
        return NGParser(str(registry_path))
    
    def test_parser_initialization(self, parser):
        """Test inizializzazione del parser."""
        assert parser is not None
        assert len(parser.symbol_registry) > 0
        assert len(parser.symbol_mappings) > 0
        print(f"✅ Parser inizializzato con {len(parser.symbol_registry)} simboli")
    
    def test_basic_parsing(self, parser):
        """Test parsing base."""
        input_text = "Create a function to sort a list"
        result = parser.parse(input_text)
        
        assert isinstance(result, ParsedPrompt)
        assert result.original_text == input_text
        assert len(result.tokens) > 0
        assert result.parsing_confidence >= 0.0
        
        print(f"✅ Parsing base completato:")
        print(f"   - Tokens: {len(result.tokens)}")
        print(f"   - Simboli: {len(result.symbols_used)}")
        print(f"   - Confidence: {result.parsing_confidence:.3f}")
        print(f"   - Intenti: {result.intents}")
    
    def test_symbol_mapping(self, parser):
        """Test mappatura simbolica."""
        input_text = "function def class"
        result = parser.parse(input_text)
        
        # Verifica che almeno alcuni token abbiano simboli
        symbols_found = [token.symbol for token in result.tokens if token.symbol]
        
        print(f"✅ Mappatura simbolica:")
        print(f"   - Input: {input_text}")
        print(f"   - Simboli trovati: {len(symbols_found)}")
        for token in result.tokens:
            if token.symbol:
                print(f"   - '{token.text}' → {token.symbol} ({token.semantic_type})")
    
    def test_intent_recognition(self, parser):
        """Test riconoscimento intenti."""
        test_cases = [
            ("Create a function", ["coding"]),
            ("What is the result?", ["query"]),
            ("If x > 0 then return true", ["logic", "coding"]),
            ("Calculate the sum", ["math"])
        ]
        
        for input_text, expected_intents in test_cases:
            result = parser.parse(input_text)
            
            print(f"✅ Intent recognition:")
            print(f"   - Input: {input_text}")
            print(f"   - Intenti trovati: {result.intents}")
            print(f"   - Intenti attesi: {expected_intents}")
            
            # Verifica che almeno un intent atteso sia presente
            assert any(intent in result.intents for intent in expected_intents), \
                f"Nessun intent atteso trovato per: {input_text}"
    
    def test_semantic_segmentation(self, parser):
        """Test segmentazione semantica."""
        input_text = "Create a function to sort the list if it's not empty"
        result = parser.parse(input_text)
        
        assert len(result.semantic_segments) > 0
        
        print(f"✅ Segmentazione semantica:")
        print(f"   - Input: {input_text}")
        print(f"   - Segmenti trovati: {len(result.semantic_segments)}")
        for segment in result.semantic_segments:
            print(f"   - {segment['type']}: '{segment['text']}'")
    
    def test_validation(self, parser):
        """Test validazione parsing."""
        input_text = "Create a function to sort a list"
        result = parser.parse(input_text)
        validation = parser.validate_parsing(result)
        
        assert 'is_valid' in validation
        assert 'metrics' in validation
        
        print(f"✅ Validazione parsing:")
        print(f"   - Valido: {validation['is_valid']}")
        print(f"   - Errori: {len(validation['errors'])}")
        print(f"   - Warning: {len(validation['warnings'])}")
        print(f"   - Metriche: {validation['metrics']}")
    
    def test_symbol_info(self, parser):
        """Test informazioni simboli."""
        # Prendi il primo simbolo disponibile
        if parser.symbol_registry:
            symbol = next(iter(parser.symbol_registry.keys()))
            info = parser.get_symbol_info(symbol)
            
            assert info is not None
            assert 'id' in info
            assert 'domain' in info
            
            print(f"✅ Info simbolo {symbol}:")
            print(f"   - ID: {info.get('id')}")
            print(f"   - Dominio: {info.get('domain')}")
            print(f"   - Score: {info.get('score')}")
    
    def test_domain_symbols(self, parser):
        """Test simboli per dominio."""
        domains = ['logic', 'math', 'code', 'memory']
        
        for domain in domains:
            symbols = parser.get_symbols_by_domain(domain)
            print(f"✅ Dominio '{domain}': {len(symbols)} simboli")
            if symbols:
                print(f"   - Esempi: {symbols[:5]}")

def run_manual_test():
    """Test manuale interattivo."""
    print("🧠 NEUROGLYPH v2.0 - Test NG_PARSER")
    print("=" * 50)
    
    try:
        # Inizializza parser
        registry_path = project_root / "neuroglyph_ULTIMATE_registry.json"
        if not registry_path.exists():
            print(f"❌ Registry non trovato: {registry_path}")
            return False
        
        parser = NGParser(str(registry_path))
        print(f"✅ Parser inizializzato con {len(parser.symbol_registry)} simboli")
        
        # Test interattivo
        test_inputs = [
            "Create a function to sort a list",
            "What is the meaning of life?",
            "If x > 0 then return true else false",
            "Calculate the factorial of 5",
            "Implement a binary search algorithm"
        ]
        
        for i, input_text in enumerate(test_inputs, 1):
            print(f"\n🔍 Test {i}: {input_text}")
            print("-" * 40)
            
            result = parser.parse(input_text)
            
            print(f"📊 Risultati:")
            print(f"   - Tokens: {len(result.tokens)}")
            print(f"   - Simboli: {len(result.symbols_used)}")
            print(f"   - Confidence: {result.parsing_confidence:.3f}")
            print(f"   - Intenti: {result.intents}")
            print(f"   - Segmenti: {len(result.semantic_segments)}")
            
            # Mostra alcuni simboli mappati
            mapped_symbols = [(t.text, t.symbol) for t in result.tokens if t.symbol]
            if mapped_symbols:
                print(f"   - Mappature simboliche:")
                for text, symbol in mapped_symbols[:3]:
                    print(f"     '{text}' → {symbol}")
        
        print(f"\n🎊 Test completati con successo!")
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Esegui test manuale se chiamato direttamente
    success = run_manual_test()
    sys.exit(0 if success else 1)
