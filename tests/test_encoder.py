#!/usr/bin/env python3
"""
Test Suite NEUROGLYPH Encoder/Decoder v2.0

Suite completa di test per validare round-trip fidelity,
compressione semantica e ricostruzione intelligente.
"""

import unittest
import ast
import tempfile
import os
from pathlib import Path
from typing import Dict, List, Any

# Import moduli NEUROGLYPH
import sys
sys.path.append(str(Path(__file__).parent.parent))

from neuroglyph.core.encoder.encoder import NGEncoder
from neuroglyph.core.encoder.semantic_compressor import SemanticCompressor, CompressionLevel
from neuroglyph.core.encoder.symbol_mapper import SymbolMapper
from neuroglyph.core.encoder.decoder import NGDecoder, FormattingOptions

class TestNGEncoder(unittest.TestCase):
    """Test per NGEncoder principale."""
    
    def setUp(self):
        """Setup per ogni test."""
        self.encoder = NGEncoder(
            registry_path="neuroglyph_ULTIMATE_registry.json",
            default_encoding_level=3,
            default_language="python"
        )
        
        # Esempi di codice per test
        self.test_codes = {
            "simple_function": """
def hello_world():
    return "Hello, World!"
""".strip(),
            
            "function_with_if": """
def check_number(x):
    if x > 0:
        return "positive"
    else:
        return "negative"
""".strip(),
            
            "class_with_methods": """
class Calculator:
    def __init__(self):
        self.result = 0
    
    def add(self, x):
        self.result += x
        return self.result
    
    def multiply(self, x):
        self.result *= x
        return self.result
""".strip(),
            
            "for_loop_example": """
def sum_list(numbers):
    total = 0
    for num in numbers:
        total += num
    return total
""".strip(),
            
            "complex_algorithm": """
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quicksort(left) + middle + quicksort(right)
""".strip(),
            
            "async_function": """
async def fetch_data(url):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.text()
""".strip(),
            
            "try_except_block": """
def safe_divide(a, b):
    try:
        result = a / b
        return result
    except ZeroDivisionError:
        return None
    finally:
        print("Division attempted")
""".strip(),
            
            "list_comprehension": """
def process_data(items):
    filtered = [x for x in items if x > 0]
    squared = [x**2 for x in filtered]
    return squared
""".strip()
        }
    
    def test_basic_encoding(self):
        """Test encoding base di funzioni semplici."""
        code = self.test_codes["simple_function"]
        
        result = self.encoder.encode(code, encoding_level=1)
        
        self.assertIsNotNone(result.compressed_symbols)
        self.assertGreater(len(result.compressed_symbols), 0)
        self.assertEqual(result.encoding_level, 1)
        self.assertEqual(result.language, "python")
        self.assertIn("⟨⟩", result.compressed_symbols)  # Simbolo funzione
    
    def test_intermediate_encoding(self):
        """Test encoding intermedio con loop e classi."""
        code = self.test_codes["class_with_methods"]
        
        result = self.encoder.encode(code, encoding_level=2)
        
        self.assertGreater(result.compression_result.compression_ratio, 0)
        self.assertIn("⟡", result.compressed_symbols)  # Simbolo classe
        self.assertIn("⟨⟩", result.compressed_symbols)  # Simbolo funzione
    
    def test_advanced_encoding(self):
        """Test encoding avanzato con async e try/except."""
        code = self.test_codes["async_function"]
        
        result = self.encoder.encode(code, encoding_level=3)
        
        self.assertIn("⊶", result.compressed_symbols)  # Simbolo async
        self.assertGreater(result.compression_result.compression_ratio, 20)
    
    def test_round_trip_simple(self):
        """Test round-trip per funzione semplice."""
        code = self.test_codes["simple_function"]
        
        result = self.encoder.round_trip_test(code, encoding_level=2)
        
        self.assertTrue(result.ast_equivalent)
        self.assertTrue(result.semantic_equivalent)
        self.assertGreater(result.fidelity_score, 0.8)
        self.assertTrue(result.decoding_result.round_trip_successful)
    
    def test_round_trip_complex(self):
        """Test round-trip per algoritmo complesso."""
        code = self.test_codes["complex_algorithm"]
        
        result = self.encoder.round_trip_test(code, encoding_level=3)
        
        # Verifica che la struttura AST sia preservata
        try:
            original_ast = ast.parse(code)
            reconstructed_ast = ast.parse(result.reconstructed_code)
            
            # Confronta tipi di nodi principali
            original_nodes = [type(node).__name__ for node in ast.walk(original_ast)]
            reconstructed_nodes = [type(node).__name__ for node in ast.walk(reconstructed_ast)]
            
            # Deve contenere almeno gli stessi tipi di nodi
            self.assertTrue(set(original_nodes).issubset(set(reconstructed_nodes)))
            
        except SyntaxError:
            self.fail("Codice ricostruito non è sintatticamente valido")
    
    def test_compression_ratios(self):
        """Test rapporti di compressione per diversi livelli."""
        code = self.test_codes["for_loop_example"]
        
        ratios = {}
        for level in range(1, 4):
            result = self.encoder.encode(code, encoding_level=level)
            ratios[level] = result.compression_result.compression_ratio
        
        # 4️⃣ CORREZIONE: Livelli più alti generano più simboli dettagliati (comprimono meno)
        # Questo è corretto: più dettaglio = meno compressione
        self.assertLessEqual(ratios[3], ratios[2])
        self.assertLessEqual(ratios[2], ratios[1])
    
    def test_syntax_validation(self):
        """Test validazione sintassi per tutti gli esempi."""
        for name, code in self.test_codes.items():
            with self.subTest(example=name):
                result = self.encoder.round_trip_test(code, encoding_level=2)
                
                # Il codice ricostruito deve essere sintatticamente valido
                self.assertTrue(
                    result.decoding_result.reconstruction_result.syntax_valid,
                    f"Sintassi non valida per {name}"
                )
    
    def test_pattern_recognition(self):
        """Test riconoscimento pattern specifici."""
        # Test pattern for-if-return
        code = """
def find_first_positive(numbers):
    for num in numbers:
        if num > 0:
            return num
    return None
"""
        
        result = self.encoder.encode(code, encoding_level=5)
        
        # Dovrebbe riconoscere pattern complessi a livello 5
        self.assertGreater(len(result.compression_result.patterns_applied), 2)
    
    def test_metadata_preservation(self):
        """Test preservazione metadati durante encoding/decoding."""
        code = self.test_codes["simple_function"]
        
        encoding_result = self.encoder.encode(code, encoding_level=2)
        decoding_result = self.encoder.decode(
            encoding_result.compressed_symbols,
            encoding_result.metadata
        )
        
        # Verifica metadati
        self.assertEqual(encoding_result.metadata["encoding_level"], 2)
        self.assertEqual(encoding_result.metadata["language"], "python")
        self.assertIn("encoder_version", encoding_result.metadata)
        self.assertIn("encoding_timestamp", encoding_result.metadata)

class TestSemanticCompressor(unittest.TestCase):
    """Test per SemanticCompressor."""
    
    def setUp(self):
        """Setup per test compressore."""
        self.compressor = SemanticCompressor()
    
    def test_basic_compression(self):
        """Test compressione base."""
        code = "def test(): pass"
        
        result = self.compressor.compress(code, encoding_level=1)
        
        self.assertIn("⟨⟩", result.compressed_symbols)
        self.assertGreater(result.compression_ratio, 0)
    
    def test_pattern_application(self):
        """Test applicazione pattern."""
        code = """
if condition:
    pass
for item in items:
    pass
"""
        
        result = self.compressor.compress(code, encoding_level=2)
        
        self.assertIn("◊", result.compressed_symbols)  # If pattern
        self.assertIn("⟲", result.compressed_symbols)  # For pattern

class TestSymbolMapper(unittest.TestCase):
    """Test per SymbolMapper."""
    
    def setUp(self):
        """Setup per test mapper."""
        self.mapper = SymbolMapper()
    
    def test_symbol_to_ast_mapping(self):
        """Test mappatura simboli → AST."""
        symbols = ["⟨⟩", "◊", "⟲"]
        metadata = {"encoding_level": 2, "language": "python"}
        
        result = self.mapper.reverse_map(symbols, metadata)
        
        self.assertIsInstance(result.ast_structure, ast.Module)
        self.assertGreater(result.mapping_info["mapped_symbols"], 0)
    
    def test_complex_pattern_mapping(self):
        """Test mappatura pattern complessi."""
        symbols = ["⟈"]  # If-elif-else chain
        metadata = {"encoding_level": 5, "language": "python"}
        
        result = self.mapper.reverse_map(symbols, metadata)
        
        # Dovrebbe creare struttura if-elif-else
        self.assertIsInstance(result.ast_structure, ast.Module)

class TestNGDecoder(unittest.TestCase):
    """Test per NGDecoder."""
    
    def setUp(self):
        """Setup per test decoder."""
        self.decoder = NGDecoder()
    
    def test_basic_reconstruction(self):
        """Test ricostruzione base."""
        # Crea AST semplice
        func_node = ast.FunctionDef(
            name="test_func",
            args=ast.arguments(
                posonlyargs=[], args=[], vararg=None,
                kwonlyargs=[], kw_defaults=[], kwarg=None, defaults=[]
            ),
            body=[ast.Pass()],
            decorator_list=[],
            returns=None
        )
        module = ast.Module(body=[func_node], type_ignores=[])
        
        result = self.decoder.decode(module)
        
        self.assertTrue(result.syntax_valid)
        self.assertIn("def test_func", result.source_code)
    
    def test_formatting_options(self):
        """Test opzioni di formattazione."""
        # AST con if statement
        if_node = ast.If(
            test=ast.Name(id="condition", ctx=ast.Load()),
            body=[ast.Pass()],
            orelse=[]
        )
        module = ast.Module(body=[if_node], type_ignores=[])
        
        # Test con indentazione personalizzata
        options = FormattingOptions(indent_size=2, indent_char=" ")
        result = self.decoder.decode(module, formatting_options=options)
        
        self.assertTrue(result.syntax_valid)
        self.assertIn("if condition:", result.source_code)

def create_test_examples():
    """Crea file di esempio per test avanzati."""
    examples_dir = Path("examples")
    examples_dir.mkdir(exist_ok=True)
    
    # Esempio complesso 1: Algoritmo di sorting
    complex_1 = """
def merge_sort(arr):
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])
    
    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
    
    result.extend(left[i:])
    result.extend(right[j:])
    return result
"""
    
    with open(examples_dir / "complex_1.py", "w") as f:
        f.write(complex_1)
    
    # Esempio complesso 2: Classe con design pattern
    complex_2 = """
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.data = {}
            self.initialized = True
    
    def get_data(self, key):
        return self.data.get(key)
    
    def set_data(self, key, value):
        self.data[key] = value
"""
    
    with open(examples_dir / "complex_2.py", "w") as f:
        f.write(complex_2)

class TestRoundTripExamples(unittest.TestCase):
    """Test round-trip su esempi complessi da file."""
    
    @classmethod
    def setUpClass(cls):
        """Setup classe - crea esempi."""
        create_test_examples()
        cls.encoder = NGEncoder()
    
    def test_complex_example_1(self):
        """Test round-trip su algoritmo merge sort."""
        example_path = Path("examples/complex_1.py")
        if not example_path.exists():
            self.skipTest("File esempio non trovato")
        
        with open(example_path, "r") as f:
            code = f.read()
        
        result = self.encoder.round_trip_test(code, encoding_level=3)
        
        # Verifica AST equivalenza
        self.assertTrue(result.ast_equivalent, "AST non equivalente per merge sort")
        
        # Verifica che il codice ricostruito sia eseguibile
        try:
            exec(result.reconstructed_code)
        except Exception as e:
            self.fail(f"Codice ricostruito non eseguibile: {e}")
    
    def test_complex_example_2(self):
        """Test round-trip su design pattern Singleton."""
        example_path = Path("examples/complex_2.py")
        if not example_path.exists():
            self.skipTest("File esempio non trovato")
        
        with open(example_path, "r") as f:
            code = f.read()
        
        result = self.encoder.round_trip_test(code, encoding_level=4)
        
        # Verifica fidelity score alto
        self.assertGreater(result.fidelity_score, 0.7, "Fidelity score troppo basso")
        
        # Verifica sintassi valida
        self.assertTrue(
            result.decoding_result.reconstruction_result.syntax_valid,
            "Sintassi non valida per Singleton"
        )

if __name__ == "__main__":
    # Crea esempi prima di eseguire test
    create_test_examples()
    
    # Esegui test
    unittest.main(verbosity=2)
