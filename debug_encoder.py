#!/usr/bin/env python3
"""
Debug script per analizzare il problema dell'encoder
"""

import sys
sys.path.append('.')

from neuroglyph.core.encoder.encoder import NGEncoder
import ast

def debug_round_trip():
    """Debug del round-trip test."""
    encoder = NGEncoder()
    
    code = '''def hello_world():
    return "Hello, World!"'''
    
    print("=== CODICE ORIGINALE ===")
    print(repr(code))
    print(code)
    print()
    
    # Test encoding
    encoding_result = encoder.encode(code, encoding_level=2)
    print("=== SIMBOLI GENERATI ===")
    print(encoding_result.compressed_symbols)
    print()

    print("=== PAYLOAD ===")
    print("Payload:", encoding_result.metadata.get("payload", []))
    print("Symbol metadata:", encoding_result.metadata.get("symbol_metadata", {}))
    print()
    
    # Test decoding
    decoding_result = encoder.decode(
        encoding_result.compressed_symbols,
        encoding_result.metadata
    )
    
    print("=== CODICE RICOSTRUITO ===")
    print(repr(decoding_result.reconstructed_code))
    print(decoding_result.reconstructed_code)
    print()
    
    # Confronto AST
    print("=== CONFRONTO AST ===")
    try:
        orig_ast = ast.parse(code)
        recon_ast = ast.parse(decoding_result.reconstructed_code)
        
        print("Originale:")
        print(ast.dump(orig_ast, indent=2))
        print()
        
        print("Ricostruito:")
        print(ast.dump(recon_ast, indent=2))
        print()
        
        # Confronto nodi
        orig_nodes = [type(node).__name__ for node in ast.walk(orig_ast)]
        recon_nodes = [type(node).__name__ for node in ast.walk(recon_ast)]
        
        print("Nodi originali:", orig_nodes)
        print("Nodi ricostruiti:", recon_nodes)
        print()
        
        print("AST equivalente:", ast.dump(orig_ast) == ast.dump(recon_ast))
        
    except Exception as e:
        print(f"Errore nel parsing AST: {e}")

if __name__ == "__main__":
    debug_round_trip()
