#!/usr/bin/env python3
"""
Pulizia Automatica Registry NEUROGLYPH v2.0

Script per pulire e ricostruire il registry simbolico:
1. Rimuove duplicati ID e codepoint
2. Standardizza fallback generici
3. Aggiunge campi "meaning" mancanti
4. Riorganizza categorie
5. Valida Unicode safety
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from collections import Counter, defaultdict
import re
import unicodedata

def load_registry(registry_path: str) -> Dict[str, Any]:
    """Carica il registry simbolico."""
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return {}

def backup_registry(registry_path: str) -> str:
    """Crea backup del registry originale."""
    backup_path = registry_path.replace('.json', '_pre_clean.json')
    
    try:
        with open(registry_path, 'r', encoding='utf-8') as src:
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        print(f"✅ Backup creato: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ Errore creazione backup: {e}")
        return ""

def remove_duplicate_ids(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Rimuove simboli con ID duplicati, mantenendo il primo."""
    print("🔧 Rimozione ID duplicati...")
    
    seen_ids = set()
    cleaned_symbols = []
    removed_count = 0
    
    for symbol in symbols:
        symbol_id = symbol.get('id')
        if symbol_id not in seen_ids:
            seen_ids.add(symbol_id)
            cleaned_symbols.append(symbol)
        else:
            removed_count += 1
    
    print(f"   - Rimossi {removed_count} simboli con ID duplicati")
    print(f"   - Simboli rimanenti: {len(cleaned_symbols)}")
    
    return cleaned_symbols

def fix_duplicate_codepoints(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Corregge codepoint duplicati assegnando nuovi valori."""
    print("🔧 Correzione codepoint duplicati...")
    
    # Trova codepoint duplicati
    codepoint_counts = Counter(s.get('unicode_point') for s in symbols if s.get('unicode_point'))
    duplicate_codepoints = {cp for cp, count in codepoint_counts.items() if count > 1}
    
    if not duplicate_codepoints:
        print("   - Nessun codepoint duplicato trovato")
        return symbols
    
    print(f"   - Codepoint duplicati: {len(duplicate_codepoints)}")
    
    # Trova range liberi per nuovi codepoint
    used_codepoints = set()
    for symbol in symbols:
        cp = symbol.get('unicode_point', '')
        if cp.startswith('U+'):
            try:
                used_codepoints.add(int(cp[2:], 16))
            except ValueError:
                pass
    
    # Genera nuovi codepoint nel Private Use Area (U+E000-U+F8FF)
    next_private_use = 0xE000
    
    # Correggi duplicati
    codepoint_assignments = {}
    for symbol in symbols:
        cp = symbol.get('unicode_point', '')
        
        if cp in duplicate_codepoints:
            if cp not in codepoint_assignments:
                # Primo simbolo mantiene il codepoint originale
                codepoint_assignments[cp] = [cp]
            else:
                # Simboli successivi ricevono nuovo codepoint
                while next_private_use in used_codepoints:
                    next_private_use += 1
                
                new_cp = f"U+{next_private_use:04X}"
                symbol['unicode_point'] = new_cp
                used_codepoints.add(next_private_use)
                codepoint_assignments[cp].append(new_cp)
                next_private_use += 1
    
    print(f"   - Assegnati {sum(len(v)-1 for v in codepoint_assignments.values())} nuovi codepoint")
    
    return symbols

def improve_fallbacks(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Migliora fallback generici con descrizioni semantiche."""
    print("🔧 Miglioramento fallback...")
    
    # Mappature semantiche per domini comuni
    domain_fallbacks = {
        'logic': {
            'default': 'logic_op',
            'patterns': {
                r'and|∧': 'and',
                r'or|∨': 'or', 
                r'not|¬': 'not',
                r'implies|→': 'implies',
                r'equiv|↔': 'equiv'
            }
        },
        'math': {
            'default': 'math_op',
            'patterns': {
                r'sum|∑': 'sum',
                r'prod|∏': 'product',
                r'integral|∫': 'integral',
                r'partial|∂': 'partial',
                r'infinity|∞': 'infinity'
            }
        },
        'code': {
            'default': 'code_op',
            'patterns': {
                r'function|def': 'function',
                r'class': 'class',
                r'if': 'conditional',
                r'loop|for|while': 'loop',
                r'return': 'return'
            }
        }
    }
    
    improved_count = 0
    
    for symbol in symbols:
        fallback = symbol.get('fallback', '')
        domain = symbol.get('domain', '')
        symbol_char = symbol.get('symbol', '')
        
        # Migliora fallback generici [UXXX]
        if re.match(r'\[U\d+\]', fallback):
            improved_fallback = None
            
            # Cerca pattern specifici per dominio
            if domain in domain_fallbacks:
                domain_config = domain_fallbacks[domain]
                
                for pattern, replacement in domain_config['patterns'].items():
                    if re.search(pattern, symbol_char, re.IGNORECASE):
                        improved_fallback = replacement
                        break
                
                # Usa fallback di default per il dominio
                if not improved_fallback:
                    improved_fallback = domain_config['default']
            
            # Fallback generico basato su Unicode name
            if not improved_fallback:
                try:
                    unicode_name = unicodedata.name(symbol_char, '').lower()
                    if unicode_name:
                        # Semplifica il nome Unicode
                        improved_fallback = unicode_name.replace(' ', '_').replace('-', '_')
                        # Limita lunghezza
                        if len(improved_fallback) > 12:
                            improved_fallback = improved_fallback[:12]
                except ValueError:
                    improved_fallback = f"symbol_{domain}" if domain else "symbol"
            
            if improved_fallback and improved_fallback != fallback:
                symbol['fallback'] = improved_fallback
                improved_count += 1
    
    print(f"   - Migliorati {improved_count} fallback")
    
    return symbols

def add_meaning_fields(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Aggiunge campi 'meaning' mancanti."""
    print("🔧 Aggiunta campi 'meaning'...")
    
    added_count = 0
    
    for symbol in symbols:
        if 'meaning' not in symbol or not symbol['meaning']:
            # Genera meaning da fallback e dominio
            fallback = symbol.get('fallback', '')
            domain = symbol.get('domain', '')
            symbol_char = symbol.get('symbol', '')
            
            # Costruisci meaning descrittivo
            if fallback and not re.match(r'\[U\d+\]', fallback):
                meaning = fallback.replace('_', ' ').title()
            else:
                meaning = f"{domain.title()} Symbol" if domain else "Symbol"
            
            # Aggiungi contesto Unicode se disponibile
            try:
                if symbol_char and len(symbol_char) == 1:
                    unicode_name = unicodedata.name(symbol_char, '')
                    if unicode_name and len(unicode_name) < 30:
                        meaning = unicode_name.title()
            except (ValueError, TypeError):
                pass
            
            symbol['meaning'] = meaning
            added_count += 1
    
    print(f"   - Aggiunti {added_count} campi 'meaning'")
    
    return symbols

def reorganize_categories(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Riorganizza e standardizza le categorie."""
    print("🔧 Riorganizzazione categorie...")
    
    # Mappature per standardizzare domini
    domain_mappings = {
        # Mantieni domini principali
        'logic': 'logic',
        'math': 'math', 
        'code': 'code',
        'cognition': 'cognition',
        'meta': 'meta',
        'ai': 'ai',
        
        # Aggiungi categorie standard NEUROGLYPH
        'control': 'control_flow',
        'flow': 'control_flow',
        'data': 'data_type',
        'type': 'data_type',
        'var': 'variable',
        'variable': 'variable',
        'semantic': 'semantic_marker',
        'marker': 'semantic_marker',
        'advanced': 'advanced_construct',
        'construct': 'advanced_construct'
    }
    
    reorganized_count = 0
    
    for symbol in symbols:
        domain = symbol.get('domain', '')
        
        # Applica mappatura se disponibile
        if domain in domain_mappings:
            new_domain = domain_mappings[domain]
            if new_domain != domain:
                symbol['domain'] = new_domain
                reorganized_count += 1
    
    print(f"   - Riorganizzati {reorganized_count} domini")
    
    # Ordina simboli per categoria e meaning
    symbols.sort(key=lambda s: (s.get('domain', ''), s.get('meaning', ''), s.get('symbol', '')))
    
    return symbols

def validate_and_clean(symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Validazione finale e pulizia."""
    print("🔧 Validazione finale...")
    
    valid_symbols = []
    removed_count = 0
    
    for symbol in symbols:
        # Verifica campi obbligatori
        required_fields = ['id', 'symbol', 'fallback', 'domain', 'unicode_point']
        
        if all(symbol.get(field) for field in required_fields):
            # Verifica Unicode safety
            try:
                symbol_char = symbol['symbol']
                symbol_char.encode('utf-8').decode('utf-8')
                valid_symbols.append(symbol)
            except UnicodeError:
                removed_count += 1
        else:
            removed_count += 1
    
    print(f"   - Rimossi {removed_count} simboli non validi")
    print(f"   - Simboli validi: {len(valid_symbols)}")
    
    return valid_symbols

def update_stats(registry: Dict[str, Any], symbols: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Aggiorna statistiche del registry."""
    print("🔧 Aggiornamento statistiche...")
    
    # Conta domini
    domain_counts = Counter(s.get('domain') for s in symbols if s.get('domain'))
    
    # Conta score
    score_ranges = {
        '99+': 0,
        '95-98': 0,
        '90-94': 0,
        '85-89': 0,
        '80-84': 0,
        '<80': 0
    }
    
    for symbol in symbols:
        score = symbol.get('score', 0)
        if score >= 99:
            score_ranges['99+'] += 1
        elif score >= 95:
            score_ranges['95-98'] += 1
        elif score >= 90:
            score_ranges['90-94'] += 1
        elif score >= 85:
            score_ranges['85-89'] += 1
        elif score >= 80:
            score_ranges['80-84'] += 1
        else:
            score_ranges['<80'] += 1
    
    # Aggiorna registry
    registry['stats'] = {
        'total_symbols': len(symbols),
        'domain_distribution': dict(domain_counts),
        'quality_distribution': score_ranges,
        'last_updated': '2024-12-19',
        'cleanup_version': '2.0.0'
    }
    
    registry['approved_symbols'] = symbols
    
    return registry

def main():
    """Funzione principale di pulizia."""
    print("🧠 NEUROGLYPH v2.0 - Pulizia Registry")
    print("=" * 50)
    
    # Carica registry
    registry_path = "neuroglyph_ULTIMATE_registry.json"
    if not Path(registry_path).exists():
        print(f"❌ Registry non trovato: {registry_path}")
        return False
    
    registry = load_registry(registry_path)
    if not registry:
        return False
    
    # Crea backup
    backup_path = backup_registry(registry_path)
    if not backup_path:
        return False
    
    print(f"📊 Simboli originali: {len(registry.get('approved_symbols', []))}")
    
    # Esegui pulizia
    symbols = registry.get('approved_symbols', [])
    
    symbols = remove_duplicate_ids(symbols)
    symbols = fix_duplicate_codepoints(symbols)
    symbols = improve_fallbacks(symbols)
    symbols = add_meaning_fields(symbols)
    symbols = reorganize_categories(symbols)
    symbols = validate_and_clean(symbols)
    
    # Aggiorna registry
    cleaned_registry = update_stats(registry, symbols)
    
    # Salva registry pulito
    cleaned_path = registry_path.replace('.json', '_CLEAN.json')
    try:
        with open(cleaned_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_registry, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Registry pulito salvato: {cleaned_path}")
        print(f"📊 Simboli finali: {len(symbols)}")
        
        # Statistiche finali
        domain_counts = Counter(s.get('domain') for s in symbols)
        print(f"📊 Domini: {len(domain_counts)}")
        print(f"📊 Top 5 domini:")
        for domain, count in domain_counts.most_common(5):
            print(f"   - {domain}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore salvataggio: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
