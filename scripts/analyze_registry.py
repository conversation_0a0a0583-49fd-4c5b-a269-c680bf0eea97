#!/usr/bin/env python3
"""
Analisi Completa Registry NEUROGLYPH v2.0

Script per analizzare il registry attuale e identificare:
- Categorie presenti e distribuzione
- Simboli duplicati o ambigui
- Simboli mancanti rispetto al paper
- Problemi di codepoint e fallback
- Inconsistenze strutturali
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Set, Tuple
from collections import Counter, defaultdict
import re

def load_registry(registry_path: str) -> Dict[str, Any]:
    """Carica il registry simbolico."""
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return {}

def analyze_categories(registry: Dict[str, Any]) -> Dict[str, Any]:
    """Analizza le categorie presenti nel registry."""
    print("🔍 ANALISI CATEGORIE")
    print("=" * 50)
    
    approved_symbols = registry.get('approved_symbols', [])
    
    # Conta domini
    domains = Counter(s.get('domain') for s in approved_symbols if s.get('domain'))
    
    # Analizza struttura categorie
    categories_analysis = {
        'total_domains': len(domains),
        'domain_distribution': dict(domains.most_common()),
        'symbols_per_domain': {},
        'missing_standard_categories': []
    }
    
    # Categorie standard NEUROGLYPH
    standard_categories = {
        'control_flow', 'data_type', 'operator', 'variable', 
        'semantic_marker', 'advanced_construct', 'logic', 
        'memory', 'math', 'code', 'flow'
    }
    
    present_categories = set(domains.keys())
    missing_categories = standard_categories - present_categories
    
    categories_analysis['missing_standard_categories'] = list(missing_categories)
    
    print(f"📊 Domini totali: {len(domains)}")
    print(f"📊 Categorie standard mancanti: {missing_categories}")
    
    # Top 10 domini
    print(f"\n🏆 Top 10 Domini:")
    for domain, count in domains.most_common(10):
        percentage = (count / len(approved_symbols)) * 100
        print(f"   - {domain}: {count:,} simboli ({percentage:.1f}%)")
    
    return categories_analysis

def check_duplicates(registry: Dict[str, Any]) -> Dict[str, Any]:
    """Verifica duplicati di simboli e codepoint."""
    print("\n🔍 VERIFICA DUPLICATI")
    print("=" * 50)
    
    approved_symbols = registry.get('approved_symbols', [])
    
    # Verifica duplicati simboli
    symbols = [s.get('symbol') for s in approved_symbols if s.get('symbol')]
    symbol_counts = Counter(symbols)
    duplicate_symbols = {symbol: count for symbol, count in symbol_counts.items() if count > 1}
    
    # Verifica duplicati codepoint
    codepoints = [s.get('unicode_point') for s in approved_symbols if s.get('unicode_point')]
    codepoint_counts = Counter(codepoints)
    duplicate_codepoints = {cp: count for cp, count in codepoint_counts.items() if count > 1}
    
    # Verifica duplicati ID
    ids = [s.get('id') for s in approved_symbols if s.get('id')]
    id_counts = Counter(ids)
    duplicate_ids = {id_val: count for id_val, count in id_counts.items() if count > 1}
    
    duplicates_analysis = {
        'duplicate_symbols': duplicate_symbols,
        'duplicate_codepoints': duplicate_codepoints,
        'duplicate_ids': duplicate_ids,
        'total_duplicates': len(duplicate_symbols) + len(duplicate_codepoints) + len(duplicate_ids)
    }
    
    if duplicate_symbols:
        print(f"❌ Simboli duplicati: {len(duplicate_symbols)}")
        for symbol, count in list(duplicate_symbols.items())[:5]:
            print(f"   - '{symbol}' appare {count} volte")
    else:
        print("✅ Nessun simbolo duplicato")
    
    if duplicate_codepoints:
        print(f"❌ Codepoint duplicati: {len(duplicate_codepoints)}")
        for cp, count in list(duplicate_codepoints.items())[:5]:
            print(f"   - {cp} appare {count} volte")
    else:
        print("✅ Nessun codepoint duplicato")
    
    if duplicate_ids:
        print(f"❌ ID duplicati: {len(duplicate_ids)}")
    else:
        print("✅ Nessun ID duplicato")
    
    return duplicates_analysis

def check_missing_fields(registry: Dict[str, Any]) -> Dict[str, Any]:
    """Verifica campi mancanti nei simboli."""
    print("\n🔍 VERIFICA CAMPI MANCANTI")
    print("=" * 50)
    
    approved_symbols = registry.get('approved_symbols', [])
    
    required_fields = ['id', 'symbol', 'fallback', 'domain', 'unicode_point']
    recommended_fields = ['score', 'meaning', 'description']
    
    missing_analysis = {
        'symbols_missing_required': [],
        'symbols_missing_recommended': [],
        'field_coverage': {}
    }
    
    for field in required_fields + recommended_fields:
        missing_count = sum(1 for s in approved_symbols if not s.get(field))
        coverage = ((len(approved_symbols) - missing_count) / len(approved_symbols)) * 100
        missing_analysis['field_coverage'][field] = {
            'missing': missing_count,
            'coverage': coverage
        }
    
    print(f"📊 Copertura campi:")
    for field, data in missing_analysis['field_coverage'].items():
        status = "✅" if data['missing'] == 0 else "❌" if data['missing'] > 100 else "⚠️"
        print(f"   {status} {field}: {data['coverage']:.1f}% ({data['missing']} mancanti)")
    
    return missing_analysis

def check_fallback_quality(registry: Dict[str, Any]) -> Dict[str, Any]:
    """Verifica qualità dei fallback."""
    print("\n🔍 VERIFICA QUALITÀ FALLBACK")
    print("=" * 50)
    
    approved_symbols = registry.get('approved_symbols', [])
    
    fallback_analysis = {
        'generic_fallbacks': [],
        'long_fallbacks': [],
        'missing_fallbacks': [],
        'good_fallbacks': []
    }
    
    for symbol_data in approved_symbols:
        fallback = symbol_data.get('fallback', '')
        symbol = symbol_data.get('symbol', '')
        
        if not fallback:
            fallback_analysis['missing_fallbacks'].append(symbol)
        elif len(fallback) > 8:
            fallback_analysis['long_fallbacks'].append((symbol, fallback))
        elif re.match(r'\[U\d+\]', fallback):
            fallback_analysis['generic_fallbacks'].append((symbol, fallback))
        else:
            fallback_analysis['good_fallbacks'].append((symbol, fallback))
    
    print(f"✅ Fallback buoni: {len(fallback_analysis['good_fallbacks'])}")
    print(f"⚠️  Fallback generici: {len(fallback_analysis['generic_fallbacks'])}")
    print(f"❌ Fallback troppo lunghi: {len(fallback_analysis['long_fallbacks'])}")
    print(f"❌ Fallback mancanti: {len(fallback_analysis['missing_fallbacks'])}")
    
    if fallback_analysis['generic_fallbacks']:
        print(f"\n🔍 Esempi fallback generici:")
        for symbol, fallback in fallback_analysis['generic_fallbacks'][:5]:
            print(f"   - {symbol} → {fallback}")
    
    return fallback_analysis

def check_unicode_safety(registry: Dict[str, Any]) -> Dict[str, Any]:
    """Verifica sicurezza Unicode."""
    print("\n🔍 VERIFICA SICUREZZA UNICODE")
    print("=" * 50)
    
    approved_symbols = registry.get('approved_symbols', [])
    
    unicode_analysis = {
        'unsafe_symbols': [],
        'private_use_symbols': [],
        'standard_unicode_symbols': [],
        'missing_codepoints': []
    }
    
    for symbol_data in approved_symbols:
        symbol = symbol_data.get('symbol', '')
        codepoint = symbol_data.get('unicode_point', '')
        
        if not codepoint:
            unicode_analysis['missing_codepoints'].append(symbol)
            continue
        
        try:
            # Estrai il valore numerico del codepoint
            if codepoint.startswith('U+'):
                cp_value = int(codepoint[2:], 16)

                # Private Use Areas: U+E000-U+F8FF, U+F0000-U+FFFFD, U+100000-U+10FFFD
                if (0xE000 <= cp_value <= 0xF8FF or
                    0xF0000 <= cp_value <= 0xFFFFD or
                    0x100000 <= cp_value <= 0x10FFFD):
                    unicode_analysis['private_use_symbols'].append((symbol, codepoint))
                else:
                    unicode_analysis['standard_unicode_symbols'].append((symbol, codepoint))

                # Test encoding/decoding
                symbol.encode('utf-8').decode('utf-8')

        except (ValueError, UnicodeError):
            unicode_analysis['unsafe_symbols'].append((symbol, codepoint))
        except Exception:
            unicode_analysis['unsafe_symbols'].append((symbol, codepoint))
    
    print(f"✅ Simboli Unicode standard: {len(unicode_analysis['standard_unicode_symbols'])}")
    print(f"⚠️  Simboli Private Use: {len(unicode_analysis['private_use_symbols'])}")
    print(f"❌ Simboli non sicuri: {len(unicode_analysis['unsafe_symbols'])}")
    print(f"❌ Codepoint mancanti: {len(unicode_analysis['missing_codepoints'])}")
    
    return unicode_analysis

def generate_cleanup_report(registry: Dict[str, Any], analyses: Dict[str, Any]) -> str:
    """Genera report di pulizia."""
    report = []
    report.append("# NEUROGLYPH v2.0 - Registry Cleanup Report")
    report.append("=" * 60)
    report.append("")
    
    # Sommario esecutivo
    total_symbols = len(registry.get('approved_symbols', []))
    total_issues = (
        analyses['duplicates']['total_duplicates'] +
        len(analyses['unicode']['unsafe_symbols']) +
        len(analyses['unicode']['missing_codepoints']) +
        len(analyses['fallback']['missing_fallbacks'])
    )
    
    report.append("## 📊 SOMMARIO ESECUTIVO")
    report.append(f"- **Simboli totali**: {total_symbols:,}")
    report.append(f"- **Problemi identificati**: {total_issues}")
    report.append(f"- **Domini presenti**: {analyses['categories']['total_domains']}")
    report.append("")
    
    # Problemi critici
    report.append("## 🚨 PROBLEMI CRITICI DA RISOLVERE")
    report.append("")
    
    if analyses['duplicates']['duplicate_symbols']:
        report.append("### ❌ Simboli Duplicati")
        for symbol, count in analyses['duplicates']['duplicate_symbols'].items():
            report.append(f"- `{symbol}` appare {count} volte")
        report.append("")
    
    if analyses['unicode']['unsafe_symbols']:
        report.append("### ❌ Simboli Unicode Non Sicuri")
        for symbol, codepoint in analyses['unicode']['unsafe_symbols'][:10]:
            report.append(f"- `{symbol}` ({codepoint})")
        report.append("")
    
    if analyses['fallback']['generic_fallbacks']:
        report.append("### ⚠️ Fallback Generici da Migliorare")
        for symbol, fallback in analyses['fallback']['generic_fallbacks'][:10]:
            report.append(f"- `{symbol}` → `{fallback}`")
        report.append("")
    
    # Raccomandazioni
    report.append("## 🎯 RACCOMANDAZIONI")
    report.append("")
    report.append("1. **Rimuovere duplicati** - Consolidare simboli duplicati")
    report.append("2. **Standardizzare fallback** - Sostituire fallback generici con descrizioni semantiche")
    report.append("3. **Validare Unicode** - Verificare tutti i codepoint")
    report.append("4. **Aggiungere meaning** - Campo descrittivo per ogni simbolo")
    report.append("5. **Riorganizzare categorie** - Allineare con standard NEUROGLYPH")
    report.append("")
    
    return "\n".join(report)

def main():
    """Funzione principale di analisi."""
    print("🧠 NEUROGLYPH v2.0 - Analisi Registry Completa")
    print("=" * 60)
    
    # Carica registry
    registry_path = Path("neuroglyph_ULTIMATE_registry.json")
    if not registry_path.exists():
        print(f"❌ Registry non trovato: {registry_path}")
        return False
    
    registry = load_registry(str(registry_path))
    if not registry:
        return False
    
    print(f"📁 Registry: {registry_path}")
    print(f"📋 Versione: {registry.get('version', 'N/A')}")
    print(f"📊 Simboli: {len(registry.get('approved_symbols', []))}")
    print("")
    
    # Esegui analisi
    analyses = {}
    analyses['categories'] = analyze_categories(registry)
    analyses['duplicates'] = check_duplicates(registry)
    analyses['missing_fields'] = check_missing_fields(registry)
    analyses['fallback'] = check_fallback_quality(registry)
    analyses['unicode'] = check_unicode_safety(registry)
    
    # Genera report
    report = generate_cleanup_report(registry, analyses)
    
    # Salva report
    report_path = Path("registry_cleanup_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 Report salvato: {report_path}")
    
    # Risultato finale
    total_issues = (
        analyses['duplicates']['total_duplicates'] +
        len(analyses['unicode']['unsafe_symbols']) +
        len(analyses['unicode']['missing_codepoints']) +
        len(analyses['fallback']['missing_fallbacks'])
    )
    
    print("\n" + "=" * 60)
    if total_issues == 0:
        print("🎊 REGISTRY PERFETTO!")
        print("✅ Nessun problema identificato")
    else:
        print(f"⚠️  PROBLEMI IDENTIFICATI: {total_issues}")
        print("🔧 Consultare il report per i dettagli")
    
    return total_issues == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
