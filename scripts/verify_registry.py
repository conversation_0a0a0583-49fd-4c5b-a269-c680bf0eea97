#!/usr/bin/env python3
"""
Script di Verifica Registry NEUROGLYPH v2.0

Verifica l'integrità e la validità del registry simbolico
neuroglyph_ULTIMATE_registry.json con 9.236 simboli.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any
from collections import Counter

def load_registry(registry_path: str) -> Dict[str, Any]:
    """Carica il registry simbolico."""
    try:
        with open(registry_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Errore caricamento registry: {e}")
        return {}

def verify_structure(registry: Dict[str, Any]) -> bool:
    """Verifica la struttura del registry."""
    print("🔍 Verifica struttura registry...")
    
    required_keys = ['version', 'stats', 'approved_symbols']
    missing_keys = [key for key in required_keys if key not in registry]
    
    if missing_keys:
        print(f"❌ Chiavi mancanti: {missing_keys}")
        return False
    
    print("✅ Struttura registry valida")
    return True

def verify_symbols(registry: Dict[str, Any]) -> bool:
    """Verifica i simboli approvati."""
    print("🔍 Verifica simboli approvati...")
    
    approved_symbols = registry.get('approved_symbols', [])
    if not approved_symbols:
        print("❌ Nessun simbolo approvato trovato")
        return False
    
    print(f"📊 Simboli trovati: {len(approved_symbols)}")
    
    # Verifica unicità simboli
    symbols = [s.get('symbol') for s in approved_symbols if s.get('symbol')]
    symbol_counts = Counter(symbols)
    duplicates = [symbol for symbol, count in symbol_counts.items() if count > 1]
    
    if duplicates:
        print(f"❌ Simboli duplicati trovati: {len(duplicates)}")
        for symbol in duplicates[:5]:  # Mostra primi 5
            print(f"   - {symbol} (occorrenze: {symbol_counts[symbol]})")
        return False
    
    print("✅ Tutti i simboli sono unici")
    
    # Verifica campi obbligatori
    required_fields = ['id', 'symbol', 'fallback', 'domain']
    invalid_symbols = []
    
    for i, symbol_data in enumerate(approved_symbols):
        missing_fields = [field for field in required_fields if field not in symbol_data]
        if missing_fields:
            invalid_symbols.append((i, missing_fields))
    
    if invalid_symbols:
        print(f"❌ Simboli con campi mancanti: {len(invalid_symbols)}")
        for i, missing in invalid_symbols[:3]:  # Mostra primi 3
            print(f"   - Simbolo {i}: mancano {missing}")
        return False
    
    print("✅ Tutti i simboli hanno i campi obbligatori")
    return True

def verify_stats(registry: Dict[str, Any]) -> bool:
    """Verifica le statistiche del registry."""
    print("🔍 Verifica statistiche...")
    
    stats = registry.get('stats', {})
    approved_symbols = registry.get('approved_symbols', [])
    
    # Verifica conteggio totale
    declared_total = stats.get('total_symbols', 0)
    actual_total = len(approved_symbols)
    
    if declared_total != actual_total:
        print(f"❌ Mismatch conteggio simboli: dichiarati={declared_total}, effettivi={actual_total}")
        return False
    
    print(f"✅ Conteggio simboli corretto: {actual_total}")
    
    # Verifica distribuzione domini
    if 'domain_distribution' in stats:
        domain_stats = stats['domain_distribution']
        actual_domains = Counter(s.get('domain') for s in approved_symbols if s.get('domain'))
        
        for domain, declared_count in domain_stats.items():
            actual_count = actual_domains.get(domain, 0)
            if declared_count != actual_count:
                print(f"⚠️  Mismatch dominio '{domain}': dichiarati={declared_count}, effettivi={actual_count}")
    
    return True

def analyze_coverage(registry: Dict[str, Any]) -> None:
    """Analizza la copertura dei domini."""
    print("📊 Analisi copertura domini...")
    
    approved_symbols = registry.get('approved_symbols', [])
    
    # Distribuzione domini
    domains = Counter(s.get('domain') for s in approved_symbols if s.get('domain'))
    print(f"   - Domini totali: {len(domains)}")
    
    for domain, count in domains.most_common(10):
        percentage = (count / len(approved_symbols)) * 100
        print(f"   - {domain}: {count:,} simboli ({percentage:.1f}%)")
    
    # Distribuzione score
    scores = [s.get('score', 0) for s in approved_symbols if s.get('score')]
    if scores:
        avg_score = sum(scores) / len(scores)
        min_score = min(scores)
        max_score = max(scores)
        print(f"   - Score medio: {avg_score:.1f}")
        print(f"   - Score range: {min_score:.1f} - {max_score:.1f}")

def verify_unicode_safety(registry: Dict[str, Any]) -> bool:
    """Verifica la sicurezza Unicode dei simboli."""
    print("🔍 Verifica sicurezza Unicode...")
    
    approved_symbols = registry.get('approved_symbols', [])
    unsafe_symbols = []
    
    for symbol_data in approved_symbols:
        symbol = symbol_data.get('symbol', '')
        if symbol:
            try:
                # Test encoding/decoding
                symbol.encode('utf-8').decode('utf-8')
                
                # Verifica che non sia un carattere di controllo
                if any(ord(c) < 32 for c in symbol if c != '\n' and c != '\t'):
                    unsafe_symbols.append(symbol)
                    
            except UnicodeError:
                unsafe_symbols.append(symbol)
    
    if unsafe_symbols:
        print(f"❌ Simboli Unicode non sicuri: {len(unsafe_symbols)}")
        for symbol in unsafe_symbols[:5]:
            print(f"   - {repr(symbol)}")
        return False
    
    print("✅ Tutti i simboli sono Unicode-safe")
    return True

def main():
    """Funzione principale di verifica."""
    print("🧠 NEUROGLYPH v2.0 - Verifica Registry")
    print("=" * 50)

    # Trova il registry (usa parametro se fornito)
    if len(sys.argv) > 1:
        registry_path = Path(sys.argv[1])
    else:
        registry_path = Path("neuroglyph_ULTIMATE_registry.json")

    if not registry_path.exists():
        print(f"❌ Registry non trovato: {registry_path}")
        return False
    
    print(f"📁 Registry trovato: {registry_path}")
    
    # Carica registry
    registry = load_registry(str(registry_path))
    if not registry:
        return False
    
    print(f"📋 Versione registry: {registry.get('version', 'N/A')}")
    print(f"📅 Data creazione: {registry.get('creation_date', 'N/A')}")
    
    # Esegui verifiche
    checks = [
        verify_structure(registry),
        verify_symbols(registry),
        verify_stats(registry),
        verify_unicode_safety(registry)
    ]
    
    # Analisi aggiuntiva
    analyze_coverage(registry)
    
    # Risultato finale
    all_passed = all(checks)
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎊 VERIFICA COMPLETATA CON SUCCESSO!")
        print("✅ Il registry è valido e pronto per l'uso")
        
        stats = registry.get('stats', {})
        print(f"\n📊 Riepilogo:")
        print(f"   - Simboli totali: {stats.get('total_symbols', 0):,}")
        print(f"   - Domini coperti: {len(stats.get('domain_distribution', {}))}")
        quality_dist = stats.get('quality_distribution', {})
        if quality_dist:
            # Calcola qualità media pesata
            total_symbols = sum(quality_dist.values())
            weighted_sum = 0
            for range_key, count in quality_dist.items():
                if '+' in range_key:
                    # Per "99+" usa 99.5 come valore medio
                    value = float(range_key.replace('+', '')) + 0.5
                elif '<' in range_key:
                    # Per "<80" usa 75 come valore medio
                    value = 75.0
                else:
                    # Per "90-94" usa il valore iniziale
                    value = float(range_key.split('-')[0])
                weighted_sum += value * count
            avg_quality = weighted_sum / total_symbols if total_symbols > 0 else 0
            print(f"   - Qualità media: {avg_quality:.1f}")
        else:
            print(f"   - Qualità media: N/A")
        
    else:
        print("❌ VERIFICA FALLITA!")
        print("⚠️  Il registry presenta problemi che devono essere risolti")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
