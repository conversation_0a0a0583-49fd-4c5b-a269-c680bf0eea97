#!/usr/bin/env python3
"""
Test regex per debug
"""

import re

# Test del regex per binary_mul
text = "i * 2"
pattern = r"(\w+)\s*\*\s*(\w+)"

print(f"Text: '{text}'")
print(f"Pattern: '{pattern}'")

match = re.search(pattern, text)
if match:
    print(f"Match found!")
    print(f"Group 0: '{match.group(0)}'")
    print(f"Group 1: '{match.group(1)}'")
    print(f"Group 2: '{match.group(2)}'")
else:
    print("No match found!")

# Test con escape
escaped_pattern = rf"(\w+)\s*\{re.escape('*')}\s*(\w+)"
print(f"\nEscaped pattern: '{escaped_pattern}'")

match2 = re.search(escaped_pattern, text)
if match2:
    print(f"Escaped match found!")
    print(f"Group 0: '{match2.group(0)}'")
    print(f"Group 1: '{match2.group(1)}'")
    print(f"Group 2: '{match2.group(2)}'")
else:
    print("No escaped match found!")
