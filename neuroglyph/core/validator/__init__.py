"""
NG_VALIDATOR - Validazione e Correzione Automatica

Sistema di validazione che garantisce la correttezza logica
e semantica di ogni output del sistema.

Caratteristiche:
- Validazione simbolica rigorosa
- Correzione automatica errori
- Verifica coerenza logica
- Zero allucinazioni garantite
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .ng_validator import NGValidator

__all__ = ["NGValidator"]

def create_validator(**kwargs):
    """Factory function per creare un'istanza del validator."""
    from .ng_validator import NGValidator
    return NGValidator(**kwargs)
