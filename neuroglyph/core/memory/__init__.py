"""
NG_MEMORY - Sistema di Memoria Simbolica

Sistema di memoria avanzato che utilizza FAISS per similarity search
e LMDB per storage persistente dei simboli e contesti.

Caratteristiche:
- Storage simbolico con FAISS/LMDB
- Retrieval semantico veloce
- Memoria episodica e semantica
- Cache intelligente
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .ng_memory import NGMemory

__all__ = ["NGMemory"]

def create_memory(**kwargs):
    """Factory function per creare un'istanza del sistema di memoria."""
    from .ng_memory import NGMemory
    return NGMemory(**kwargs)
