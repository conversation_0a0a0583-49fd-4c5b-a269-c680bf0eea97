"""
NG_PARSER - Parsing Simbolico Deterministico

Il primo modulo della pipeline NEUROGLYPH che trasforma input naturale
in rappresentazione simbolica utilizzando i 9.236 neuroglifi.

Implementazione modulare seguendo il principio di sviluppo incrementale.
"""

import json
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

@dataclass
class ParsedToken:
    """Rappresenta un token parsato con metadati simbolici."""
    text: str
    symbol: Optional[str] = None
    symbol_id: Optional[str] = None
    semantic_type: Optional[str] = None
    confidence: float = 1.0
    position: int = 0

@dataclass
class ParsedPrompt:
    """Risultato del parsing simbolico completo."""
    original_text: str
    tokens: List[ParsedToken]
    symbols_used: List[str]
    semantic_segments: List[Dict[str, Any]]
    intents: List[str]
    metadata: Dict[str, Any]
    parsing_confidence: float

class NGParser:
    """
    NG_PARSER - Parsing Simbolico Deterministico
    
    Trasforma input naturale in rappresentazione simbolica utilizzando
    il registry di 9.236 neuroglifi con mappatura 1:1 garantita.
    """
    
    def __init__(self, registry_path: str = "neuroglyph_ULTIMATE_registry.json"):
        """
        Inizializza il parser simbolico.
        
        Args:
            registry_path: Percorso al registry dei simboli
        """
        self.registry_path = registry_path
        self.symbol_registry = {}
        self.symbol_mappings = {}
        self.semantic_patterns = {}
        self._load_registry()
        self._initialize_patterns()
        
        logger.info(f"NG_PARSER inizializzato con {len(self.symbol_registry)} simboli")
    
    def _load_registry(self) -> None:
        """Carica il registry simbolico."""
        try:
            registry_file = Path(self.registry_path)
            if not registry_file.exists():
                raise FileNotFoundError(f"Registry non trovato: {self.registry_path}")
            
            with open(registry_file, 'r', encoding='utf-8') as f:
                registry_data = json.load(f)
            
            # Estrai simboli approvati
            approved_symbols = registry_data.get('approved_symbols', [])
            
            for symbol_data in approved_symbols:
                symbol = symbol_data.get('symbol')
                if symbol:
                    self.symbol_registry[symbol] = symbol_data
                    
                    # Crea mappature inverse per lookup veloce
                    fallback = symbol_data.get('fallback', '')
                    domain = symbol_data.get('domain', '')
                    
                    if fallback:
                        self.symbol_mappings[fallback] = symbol
                    if domain:
                        if domain not in self.semantic_patterns:
                            self.semantic_patterns[domain] = []
                        self.semantic_patterns[domain].append(symbol)
            
            logger.info(f"Registry caricato: {len(self.symbol_registry)} simboli")
            
        except Exception as e:
            logger.error(f"Errore caricamento registry: {e}")
            raise
    
    def _initialize_patterns(self) -> None:
        """Inizializza pattern di riconoscimento semantico."""
        # Pattern base per riconoscimento intenti
        self.intent_patterns = {
            'coding': [
                r'\b(function|def|class|import|return)\b',
                r'\b(code|program|algorithm|script)\b',
                r'\b(create|write|implement|build)\b.*\b(function|class|method)\b'
            ],
            'logic': [
                r'\b(if|then|else|and|or|not)\b',
                r'\b(true|false|boolean|logic)\b',
                r'\b(prove|demonstrate|verify)\b'
            ],
            'math': [
                r'\b(calculate|compute|solve|equation)\b',
                r'\b(number|integer|float|decimal)\b',
                r'[+\-*/=<>]+'
            ],
            'query': [
                r'\b(what|how|why|when|where|who)\b',
                r'\b(explain|describe|tell|show)\b',
                r'\?'
            ]
        }
        
        # Pattern per segmentazione semantica
        self.segment_patterns = {
            'action': r'\b(create|make|build|generate|write|implement)\b',
            'object': r'\b(function|class|method|variable|list|dict)\b',
            'condition': r'\b(if|when|while|unless|until)\b',
            'operation': r'\b(sort|filter|map|reduce|transform)\b'
        }
    
    def parse(self, input_text: str) -> ParsedPrompt:
        """
        Parsing principale dell'input in rappresentazione simbolica.
        
        Args:
            input_text: Testo da parsare
            
        Returns:
            ParsedPrompt con rappresentazione simbolica completa
        """
        logger.debug(f"Parsing input: {input_text[:100]}...")
        
        # 1. Tokenizzazione base
        tokens = self._tokenize(input_text)
        
        # 2. Mappatura simbolica
        symbolic_tokens = self._map_to_symbols(tokens)
        
        # 3. Segmentazione semantica
        segments = self._segment_semantically(input_text, symbolic_tokens)
        
        # 4. Riconoscimento intenti
        intents = self._extract_intents(input_text)
        
        # 5. Estrazione simboli utilizzati
        symbols_used = [token.symbol for token in symbolic_tokens if token.symbol]
        
        # 6. Calcolo confidence
        confidence = self._calculate_confidence(symbolic_tokens)
        
        # 7. Metadati
        metadata = {
            'total_tokens': len(symbolic_tokens),
            'symbols_mapped': len(symbols_used),
            'unique_symbols': len(set(symbols_used)),
            'coverage': len(symbols_used) / len(symbolic_tokens) if symbolic_tokens else 0,
            'parser_version': '2.0.0'
        }
        
        result = ParsedPrompt(
            original_text=input_text,
            tokens=symbolic_tokens,
            symbols_used=symbols_used,
            semantic_segments=segments,
            intents=intents,
            metadata=metadata,
            parsing_confidence=confidence
        )
        
        logger.debug(f"Parsing completato: {len(symbols_used)} simboli, confidence={confidence:.3f}")
        return result
    
    def _tokenize(self, text: str) -> List[str]:
        """Tokenizzazione base del testo."""
        # Tokenizzazione semplice per ora - da migliorare
        tokens = re.findall(r'\w+|[^\w\s]', text.lower())
        return tokens
    
    def _map_to_symbols(self, tokens: List[str]) -> List[ParsedToken]:
        """Mappa token a simboli neuroglifi."""
        symbolic_tokens = []
        
        for i, token in enumerate(tokens):
            parsed_token = ParsedToken(
                text=token,
                position=i
            )
            
            # Cerca mappatura diretta
            if token in self.symbol_mappings:
                symbol = self.symbol_mappings[token]
                symbol_data = self.symbol_registry[symbol]
                
                parsed_token.symbol = symbol
                parsed_token.symbol_id = symbol_data.get('id')
                parsed_token.semantic_type = symbol_data.get('domain')
                parsed_token.confidence = symbol_data.get('score', 95.0) / 100.0
            
            # Cerca mappatura per fallback
            else:
                for fallback, symbol in self.symbol_mappings.items():
                    if fallback.lower().strip('[]') in token:
                        symbol_data = self.symbol_registry[symbol]
                        
                        parsed_token.symbol = symbol
                        parsed_token.symbol_id = symbol_data.get('id')
                        parsed_token.semantic_type = symbol_data.get('domain')
                        parsed_token.confidence = (symbol_data.get('score', 95.0) / 100.0) * 0.8  # Penalità per match parziale
                        break
            
            symbolic_tokens.append(parsed_token)
        
        return symbolic_tokens
    
    def _segment_semantically(self, text: str, tokens: List[ParsedToken]) -> List[Dict[str, Any]]:
        """Segmenta il testo in unità semantiche."""
        segments = []
        
        for pattern_type, pattern in self.segment_patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                segments.append({
                    'type': pattern_type,
                    'text': match.group(),
                    'start': match.start(),
                    'end': match.end(),
                    'confidence': 0.9
                })
        
        return sorted(segments, key=lambda x: x['start'])
    
    def _extract_intents(self, text: str) -> List[str]:
        """Estrae intenti dal testo."""
        intents = []
        
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    intents.append(intent_type)
                    break
        
        return list(set(intents))  # Rimuovi duplicati
    
    def _calculate_confidence(self, tokens: List[ParsedToken]) -> float:
        """Calcola confidence complessiva del parsing."""
        if not tokens:
            return 0.0
        
        total_confidence = sum(token.confidence for token in tokens)
        return total_confidence / len(tokens)
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Restituisce informazioni su un simbolo specifico."""
        return self.symbol_registry.get(symbol)
    
    def get_symbols_by_domain(self, domain: str) -> List[str]:
        """Restituisce tutti i simboli di un dominio specifico."""
        return self.semantic_patterns.get(domain, [])
    
    def validate_parsing(self, parsed: ParsedPrompt) -> Dict[str, Any]:
        """Valida il risultato del parsing."""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'metrics': {}
        }
        
        # Verifica coverage simbolica
        if parsed.metadata['coverage'] < 0.3:
            validation['warnings'].append(f"Bassa copertura simbolica: {parsed.metadata['coverage']:.2f}")
        
        # Verifica confidence
        if parsed.parsing_confidence < 0.5:
            validation['warnings'].append(f"Bassa confidence: {parsed.parsing_confidence:.2f}")
        
        # Verifica presenza intenti
        if not parsed.intents:
            validation['warnings'].append("Nessun intent riconosciuto")
        
        validation['metrics'] = {
            'symbol_coverage': parsed.metadata['coverage'],
            'parsing_confidence': parsed.parsing_confidence,
            'intents_found': len(parsed.intents),
            'segments_found': len(parsed.semantic_segments)
        }
        
        return validation
