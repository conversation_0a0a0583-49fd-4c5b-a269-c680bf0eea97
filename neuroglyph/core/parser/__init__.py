"""
NG_PARSER - Parsing Simbolico Deterministico

Il primo modulo della pipeline NEUROGLYPH che trasforma input naturale
in rappresentazione simbolica utilizzando i 9.236 neuroglifi.

Caratteristiche:
- Parsing deterministico (non probabilistico)
- Mappatura 1:1 con simboli atomici
- Zero splitting garantito
- Preservazione semantica completa
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .ng_parser import NGParser

__all__ = ["NGParser"]

def create_parser(**kwargs):
    """Factory function per creare un'istanza del parser."""
    from .ng_parser import NGParser
    return NGParser(**kwargs)
