"""
NEUROGLYPH Core Encoder/Decoder - Intelligenza Simbolica

Questo modulo implementa il cuore dell'intelligenza simbolica NEUROGLYPH:
- Encoder: Trasformazione codice → simboli NEUROGLYPH
- Decoder: Trasformazione simboli → codice ricostruito
- Compressione semantica avanzata
- Round-trip fidelity garantita
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .encoder import NGEncoder
    from .decoder import NGDecoder
    from .semantic_compressor import SemanticCompressor
    from .symbol_mapper import SymbolMapper

__all__ = [
    "NGEncoder",
    "NGDecoder", 
    "SemanticCompressor",
    "SymbolMapper",
]

# Lazy loading per performance
def create_encoder(**kwargs):
    """Factory function per creare un encoder NEUROGLYPH."""
    from .encoder import NGEncoder
    return NGEncoder(**kwargs)

def create_decoder(**kwargs):
    """Factory function per creare un decoder NEUROGLYPH."""
    from .decoder import NGDecoder
    return NGDecoder(**kwargs)

def create_semantic_compressor(**kwargs):
    """Factory function per creare un compressore semantico."""
    from .semantic_compressor import SemanticCompressor
    return SemanticCompressor(**kwargs)

def create_symbol_mapper(**kwargs):
    """Factory function per creare un mappatore simbolico."""
    from .symbol_mapper import SymbolMapper
    return SymbolMapper(**kwargs)
