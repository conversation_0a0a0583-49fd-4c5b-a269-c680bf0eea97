"""
NEUROGLYPH Core - Componenti Fondamentali

Questo modulo contiene i componenti core di NEUROGLYPH v2.0:
- Parser: Parsing simbolico deterministico
- Memory: Sistema di memoria simbolica con FAISS/LMDB  
- Reasoner: Motore di ragionamento con grafi DAG
- Validator: Validazione e correzione automatica
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .parser import NGParser
    from .memory import NGMemory
    from .reasoner import NGReasoner
    from .validator import NGValidator

__all__ = [
    "NGParser",
    "NGMemory", 
    "NGReasoner",
    "NGValidator",
]

# Lazy loading per performance
def get_parser():
    """Restituisce un'istanza del parser simbolico."""
    from .parser import NGParser
    return NGParser()

def get_memory():
    """Restituisce un'istanza del sistema di memoria."""
    from .memory import NGMemory
    return NGMemory()

def get_reasoner():
    """Restituisce un'istanza del motore di ragionamento."""
    from .reasoner import NGReasoner
    return NGReasoner()

def get_validator():
    """Restituisce un'istanza del validatore."""
    from .validator import NGValidator
    return NGValidator()

class NeuroglyphCore:
    """
    Classe principale che coordina tutti i componenti core di NEUROGLYPH.
    
    Implementa il pattern Facade per semplificare l'accesso ai componenti
    e garantire la corretta inizializzazione e coordinamento.
    """
    
    def __init__(self):
        self._parser = None
        self._memory = None
        self._reasoner = None
        self._validator = None
    
    @property
    def parser(self):
        """Accesso lazy al parser simbolico."""
        if self._parser is None:
            self._parser = get_parser()
        return self._parser
    
    @property
    def memory(self):
        """Accesso lazy al sistema di memoria."""
        if self._memory is None:
            self._memory = get_memory()
        return self._memory
    
    @property
    def reasoner(self):
        """Accesso lazy al motore di ragionamento."""
        if self._reasoner is None:
            self._reasoner = get_reasoner()
        return self._reasoner
    
    @property
    def validator(self):
        """Accesso lazy al validatore."""
        if self._validator is None:
            self._validator = get_validator()
        return self._validator
    
    def process(self, input_text: str) -> dict:
        """
        Processa un input attraverso l'intera pipeline core.
        
        Args:
            input_text: Testo da processare
            
        Returns:
            Risultato del processing con tutti i metadati
        """
        # Parsing simbolico
        parsed = self.parser.parse(input_text)
        
        # Lookup in memoria
        memory_context = self.memory.retrieve(parsed)
        
        # Ragionamento
        reasoning_result = self.reasoner.reason(parsed, memory_context)
        
        # Validazione
        validation_result = self.validator.validate(reasoning_result)
        
        return {
            "parsed": parsed,
            "memory_context": memory_context,
            "reasoning": reasoning_result,
            "validation": validation_result,
            "status": "success" if validation_result.is_valid else "error"
        }
