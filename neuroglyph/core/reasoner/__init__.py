"""
NG_REASONER - Motore di Ragionamento Simbolico

Motore di ragionamento che utilizza grafi DAG per rappresentare
catene di inferenza simboliche deterministiche.

Caratteristiche:
- Ragionamento con grafi DAG
- Operatori logici simbolici
- Multi-hop reasoning
- Tracciabilità completa
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .ng_reasoner import NGReasoner

__all__ = ["NGReasoner"]

def create_reasoner(**kwargs):
    """Factory function per creare un'istanza del reasoner."""
    from .ng_reasoner import NGReasoner
    return NGReasoner(**kwargs)
