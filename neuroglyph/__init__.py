"""
NEUROGLYPH v2.0 - GOD MODE
Il primo LLM veramente pensante con ragionamento simbolico deterministico

Questo modulo fornisce l'accesso ai componenti core di NEUROGLYPH:
- Core: Componenti fondamentali (parser, memory, reasoner, validator)
- NG-THINK: Architettura cognitiva simbolica (v1_base, v2_antifragile, v3_ultra)
"""

__version__ = "2.0.0"
__author__ = "NEUROGLYPH Team"
__description__ = "Il primo LLM veramente pensante con ragionamento simbolico deterministico"

# Importazioni principali (lazy loading per performance)
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .core import NeuroglyphCore
    from .ng_think import NGThinkEngine

__all__ = [
    "__version__",
    "__author__", 
    "__description__",
    "NeuroglyphCore",
    "NGThinkEngine",
]

def get_version() -> str:
    """Restituisce la versione di NEUROGLYPH."""
    return __version__

def get_registry_info() -> dict:
    """Restituisce informazioni sul registry simbolico."""
    return {
        "total_symbols": 9236,
        "registry_file": "neuroglyph_ULTIMATE_registry.json",
        "version": "ULTIMATE.1.0",
        "zero_splitting": True,
        "atomic_mapping": True
    }

def get_architecture_info() -> dict:
    """Restituisce informazioni sull'architettura NG-THINK."""
    return {
        "v1_base": {
            "modules": 5,
            "description": "Moduli base per parsing e ragionamento"
        },
        "v2_antifragile": {
            "modules": 4, 
            "description": "Architettura antifragile con auto-correzione"
        },
        "v3_ultra": {
            "modules": 6,
            "description": "Ultra fine-grained con tracciabilità completa"
        }
    }
