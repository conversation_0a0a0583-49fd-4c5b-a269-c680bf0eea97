#!/usr/bin/env python3
"""
Test rapido per verificare miglioramenti Encoder/Decoder NEUROGLYPH
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from neuroglyph.core.encoder.encoder import <PERSON>GEncoder

def test_simple_function():
    """Test funzione semplice."""
    print("🧪 Test funzione semplice")
    
    encoder = NGEncoder()
    code = 'def hello_world():\n    return "Hello, World!"'
    
    print(f"Codice originale:\n{code}")
    print()
    
    # Test round-trip
    result = encoder.round_trip_test(code, encoding_level=2)
    
    print(f"Simboli compressi: {result.encoding_result.compressed_symbols}")
    print(f"Compressione: {result.compression_ratio:.1f}%")
    print(f"AST equivalente: {result.ast_equivalent}")
    print(f"Semantica equivalente: {result.semantic_equivalent}")
    print(f"Fidelity score: {result.fidelity_score:.2f}")
    print()
    print(f"Codice ricostruito:\n{result.reconstructed_code}")
    
    return result

def test_for_loop():
    """Test loop con pattern."""
    print("\n🧪 Test for loop")
    
    encoder = NGEncoder()
    code = """def sum_numbers(nums):
    total = 0
    for num in nums:
        total += num
    return total"""
    
    print(f"Codice originale:\n{code}")
    print()
    
    # Test encoding
    encoding_result = encoder.encode(code, encoding_level=3)
    
    print(f"Simboli compressi: {encoding_result.compressed_symbols}")
    print(f"Pattern applicati: {encoding_result.compression_result.patterns_applied}")
    print(f"Compressione: {encoding_result.compression_result.compression_ratio:.1f}%")
    
    # Test decoding
    decoding_result = encoder.decode(
        encoding_result.compressed_symbols,
        encoding_result.metadata
    )
    
    print(f"Sintassi valida: {decoding_result.reconstruction_result.syntax_valid}")
    print(f"Fidelity score: {decoding_result.fidelity_score:.2f}")
    print()
    print(f"Codice ricostruito:\n{decoding_result.reconstructed_code}")
    
    return encoding_result, decoding_result

def test_pattern_recognition():
    """Test riconoscimento pattern complessi."""
    print("\n🧪 Test pattern recognition")
    
    encoder = NGEncoder()
    code = """def find_first_positive(numbers):
    for num in numbers:
        if num > 0:
            return num
    return None"""
    
    print(f"Codice originale:\n{code}")
    print()
    
    # Test con livello 5 (ultra)
    result = encoder.encode(code, encoding_level=5)
    
    print(f"Simboli compressi: {result.compressed_symbols}")
    print(f"Pattern applicati: {result.compression_result.patterns_applied}")
    print(f"Compressione: {result.compression_result.compression_ratio:.1f}%")
    
    return result

def main():
    """Test principale."""
    print("🔧 Test Encoder/Decoder NEUROGLYPH v2.0")
    print("=" * 50)
    
    try:
        # Test 1: Funzione semplice
        result1 = test_simple_function()
        
        # Test 2: For loop
        encoding2, decoding2 = test_for_loop()
        
        # Test 3: Pattern recognition
        result3 = test_pattern_recognition()
        
        print("\n✅ Tutti i test completati!")
        print(f"Test 1 - Fidelity: {result1.fidelity_score:.2f}")
        print(f"Test 2 - Fidelity: {decoding2.fidelity_score:.2f}")
        print(f"Test 3 - Pattern: {len(result3.compression_result.patterns_applied)}")
        
    except Exception as e:
        print(f"❌ Errore durante test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
